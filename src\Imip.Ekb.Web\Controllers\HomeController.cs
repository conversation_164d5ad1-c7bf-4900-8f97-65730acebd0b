using System;
using InertiaCore;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.Ekb.Web.Controllers;

public class HomeController : AbpController
{
    public IActionResult Index()
    {
        return Inertia.Render("Home", new
        {
            message = "Welcome to Imip.Ekb Dashboard",
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous"
        });
    }
}