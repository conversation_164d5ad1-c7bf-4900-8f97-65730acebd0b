using System;
using System.Threading.Tasks;
using Imip.Ekb.Products;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.Ekb.Controllers
{
    /// <summary>
    /// API controller for products
    /// </summary>
    [RemoteService]
    [Route("api/products")]
    public class ProductsController : AbpControllerBase, IProductAppService
    {
        private readonly IProductAppService _productAppService;

        public ProductsController(IProductAppService productAppService)
        {
            _productAppService = productAppService;
        }

        /// <summary>
        /// Get a list of products with pagination and filtering
        /// </summary>
        [HttpGet]
        public Task<PagedResultDto<ProductDto>> GetListAsync(GetProductListDto input)
        {
            return _productAppService.GetListAsync(input);
        }

        /// <summary>
        /// Get a product by ID
        /// </summary>
        [HttpGet("{id}")]
        public Task<ProductDto> GetAsync(Guid id)
        {
            return _productAppService.GetAsync(id);
        }

        /// <summary>
        /// Create a new product
        /// </summary>
        [HttpPost]
        public Task<ProductDto> CreateAsync(CreateUpdateProductDto input)
        {
            return _productAppService.CreateAsync(input);
        }

        /// <summary>
        /// Update an existing product
        /// </summary>
        [HttpPut("{id}")]
        public Task<ProductDto> UpdateAsync(Guid id, CreateUpdateProductDto input)
        {
            return _productAppService.UpdateAsync(id, input);
        }

        /// <summary>
        /// Delete a product
        /// </summary>
        [HttpDelete("{id}")]
        public Task DeleteAsync(Guid id)
        {
            return _productAppService.DeleteAsync(id);
        }

        /// <summary>
        /// Check if a SKU is already in use
        /// </summary>
        [HttpGet("check-sku/{sku}")]
        public Task<bool> IsSkuInUseAsync(string sku)
        {
            return _productAppService.IsSkuInUseAsync(sku);
        }

        /// <summary>
        /// Update the stock quantity of a product
        /// </summary>
        [HttpPut("{id}/stock")]
        public Task<ProductDto> UpdateStockQuantityAsync(Guid id, [FromBody] int newQuantity)
        {
            return _productAppService.UpdateStockQuantityAsync(id, newQuantity);
        }
    }
}
