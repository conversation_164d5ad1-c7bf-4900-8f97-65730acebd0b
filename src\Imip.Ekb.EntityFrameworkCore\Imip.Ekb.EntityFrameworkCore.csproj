<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>Imip.Ekb</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Imip.Ekb.Domain\Imip.Ekb.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.EntityFrameworkCore.SqlServer" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.PermissionManagement.EntityFrameworkCore" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.SettingManagement.EntityFrameworkCore" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.BackgroundJobs.EntityFrameworkCore" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.AuditLogging.EntityFrameworkCore" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.FeatureManagement.EntityFrameworkCore" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.BlobStoring.Database.EntityFrameworkCore" Version="9.1.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.OpenIddict.EntityFrameworkCore" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Identity.EntityFrameworkCore" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.TenantManagement.EntityFrameworkCore" Version="9.1.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
      <PrivateAssets>compile; contentFiles; build; buildMultitargeting; buildTransitive; analyzers; native</PrivateAssets>
    </PackageReference>
  </ItemGroup>

</Project>
