﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>Imip.Ekb</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Imip.Ekb.Domain.Shared\Imip.Ekb.Domain.Shared.csproj" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="Volo.Abp.PermissionManagement.Application.Contracts" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Application.Contracts" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.SettingManagement.Application.Contracts" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Identity.Application.Contracts" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Account.Application.Contracts" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.TenantManagement.Application.Contracts" Version="9.1.1" />
  </ItemGroup>

</Project>
