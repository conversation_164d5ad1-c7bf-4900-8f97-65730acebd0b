import{r as c,j as N,b as _t,g as Bt,c as ut}from"./App-BZgNu-zL.js";function $e(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function dt(...e){return t=>{let r=!1;const n=e.map(o=>{const a=$e(o,t);return!r&&typeof a=="function"&&(r=!0),a});if(r)return()=>{for(let o=0;o<n.length;o++){const a=n[o];typeof a=="function"?a():$e(e[o],null)}}}}function Ie(...e){return c.useCallback(dt(...e),e)}function ft(e){const t=Vt(e),r=c.forwardRef((n,o)=>{const{children:a,...i}=n,s=c.Children.toArray(a),u=s.find(Gt);if(u){const d=u.props.children,f=s.map(v=>v===u?c.Children.count(d)>1?c.Children.only(null):c.isValidElement(d)?d.props.children:null:v);return N.jsx(t,{...i,ref:o,children:c.isValidElement(d)?c.cloneElement(d,void 0,f):null})}return N.jsx(t,{...i,ref:o,children:a})});return r.displayName=`${e}.Slot`,r}var Wt=ft("Slot");function Vt(e){const t=c.forwardRef((r,n)=>{const{children:o,...a}=r;if(c.isValidElement(o)){const i=Ut(o),s=$t(a,o.props);return o.type!==c.Fragment&&(s.ref=n?dt(n,i):i),c.cloneElement(o,s)}return c.Children.count(o)>1?c.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var mt=Symbol("radix.slottable");function Yn(e){const t=({children:r})=>N.jsx(N.Fragment,{children:r});return t.displayName=`${e}.Slottable`,t.__radixId=mt,t}function Gt(e){return c.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===mt}function $t(e,t){const r={...t};for(const n in t){const o=e[n],a=t[n];/^on[A-Z]/.test(n)?o&&a?r[n]=(...s)=>{const u=a(...s);return o(...s),u}:o&&(r[n]=o):n==="style"?r[n]={...o,...a}:n==="className"&&(r[n]=[o,a].filter(Boolean).join(" "))}return{...e,...r}}function Ut(e){var n,o;let t=(n=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:n.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,r=t&&"isReactWarning"in t&&t.isReactWarning,r?e.props.ref:e.props.ref||e.ref)}function pt(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(r=pt(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function vt(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=pt(e))&&(n&&(n+=" "),n+=t);return n}const Ue=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,Ke=vt,Kt=(e,t)=>r=>{var n;if((t==null?void 0:t.variants)==null)return Ke(e,r==null?void 0:r.class,r==null?void 0:r.className);const{variants:o,defaultVariants:a}=t,i=Object.keys(o).map(d=>{const f=r==null?void 0:r[d],v=a==null?void 0:a[d];if(f===null)return null;const h=Ue(f)||Ue(v);return o[d][h]}),s=r&&Object.entries(r).reduce((d,f)=>{let[v,h]=f;return h===void 0||(d[v]=h),d},{}),u=t==null||(n=t.compoundVariants)===null||n===void 0?void 0:n.reduce((d,f)=>{let{class:v,className:h,...k}=f;return Object.entries(k).every(E=>{let[l,b]=E;return Array.isArray(b)?b.includes({...a,...s}[l]):{...a,...s}[l]===b})?[...d,v,h]:d},[]);return Ke(e,i,u,r==null?void 0:r.class,r==null?void 0:r.className)},ze="-",Ht=e=>{const t=Yt(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:i=>{const s=i.split(ze);return s[0]===""&&s.length!==1&&s.shift(),ht(s,t)||Xt(i)},getConflictingClassGroupIds:(i,s)=>{const u=r[i]||[];return s&&n[i]?[...u,...n[i]]:u}}},ht=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const r=e[0],n=t.nextPart.get(r),o=n?ht(e.slice(1),n):void 0;if(o)return o;if(t.validators.length===0)return;const a=e.join(ze);return(i=t.validators.find(({validator:s})=>s(a)))==null?void 0:i.classGroupId},He=/^\[(.+)\]$/,Xt=e=>{if(He.test(e)){const t=He.exec(e)[1],r=t==null?void 0:t.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},Yt=e=>{const{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(const o in r)Me(r[o],n,o,t);return n},Me=(e,t,r,n)=>{e.forEach(o=>{if(typeof o=="string"){const a=o===""?t:Xe(t,o);a.classGroupId=r;return}if(typeof o=="function"){if(Zt(o)){Me(o(n),t,r,n);return}t.validators.push({validator:o,classGroupId:r});return}Object.entries(o).forEach(([a,i])=>{Me(i,Xe(t,a),r,n)})})},Xe=(e,t)=>{let r=e;return t.split(ze).forEach(n=>{r.nextPart.has(n)||r.nextPart.set(n,{nextPart:new Map,validators:[]}),r=r.nextPart.get(n)}),r},Zt=e=>e.isThemeGetter,qt=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,n=new Map;const o=(a,i)=>{r.set(a,i),t++,t>e&&(t=0,n=r,r=new Map)};return{get(a){let i=r.get(a);if(i!==void 0)return i;if((i=n.get(a))!==void 0)return o(a,i),i},set(a,i){r.has(a)?r.set(a,i):o(a,i)}}},Le="!",Te=":",Qt=Te.length,Jt=e=>{const{prefix:t,experimentalParseClassName:r}=e;let n=o=>{const a=[];let i=0,s=0,u=0,d;for(let E=0;E<o.length;E++){let l=o[E];if(i===0&&s===0){if(l===Te){a.push(o.slice(u,E)),u=E+Qt;continue}if(l==="/"){d=E;continue}}l==="["?i++:l==="]"?i--:l==="("?s++:l===")"&&s--}const f=a.length===0?o:o.substring(u),v=er(f),h=v!==f,k=d&&d>u?d-u:void 0;return{modifiers:a,hasImportantModifier:h,baseClassName:v,maybePostfixModifierPosition:k}};if(t){const o=t+Te,a=n;n=i=>i.startsWith(o)?a(i.substring(o.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:i,maybePostfixModifierPosition:void 0}}if(r){const o=n;n=a=>r({className:a,parseClassName:o})}return n},er=e=>e.endsWith(Le)?e.substring(0,e.length-1):e.startsWith(Le)?e.substring(1):e,tr=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(n=>[n,!0]));return n=>{if(n.length<=1)return n;const o=[];let a=[];return n.forEach(i=>{i[0]==="["||t[i]?(o.push(...a.sort(),i),a=[]):a.push(i)}),o.push(...a.sort()),o}},rr=e=>({cache:qt(e.cacheSize),parseClassName:Jt(e),sortModifiers:tr(e),...Ht(e)}),nr=/\s+/,or=(e,t)=>{const{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o,sortModifiers:a}=t,i=[],s=e.trim().split(nr);let u="";for(let d=s.length-1;d>=0;d-=1){const f=s[d],{isExternal:v,modifiers:h,hasImportantModifier:k,baseClassName:E,maybePostfixModifierPosition:l}=r(f);if(v){u=f+(u.length>0?" "+u:u);continue}let b=!!l,w=n(b?E.substring(0,l):E);if(!w){if(!b){u=f+(u.length>0?" "+u:u);continue}if(w=n(E),!w){u=f+(u.length>0?" "+u:u);continue}b=!1}const A=a(h).join(":"),P=k?A+Le:A,S=P+w;if(i.includes(S))continue;i.push(S);const R=o(w,b);for(let T=0;T<R.length;++T){const C=R[T];i.push(P+C)}u=f+(u.length>0?" "+u:u)}return u};function ar(){let e=0,t,r,n="";for(;e<arguments.length;)(t=arguments[e++])&&(r=gt(t))&&(n&&(n+=" "),n+=r);return n}const gt=e=>{if(typeof e=="string")return e;let t,r="";for(let n=0;n<e.length;n++)e[n]&&(t=gt(e[n]))&&(r&&(r+=" "),r+=t);return r};function sr(e,...t){let r,n,o,a=i;function i(u){const d=t.reduce((f,v)=>v(f),e());return r=rr(d),n=r.cache.get,o=r.cache.set,a=s,s(u)}function s(u){const d=n(u);if(d)return d;const f=or(u,r);return o(u,f),f}return function(){return a(ar.apply(null,arguments))}}const M=e=>{const t=r=>r[e]||[];return t.isThemeGetter=!0,t},bt=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,yt=/^\((?:(\w[\w-]*):)?(.+)\)$/i,ir=/^\d+\/\d+$/,cr=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,lr=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,ur=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,dr=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,fr=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,U=e=>ir.test(e),x=e=>!!e&&!Number.isNaN(Number(e)),_=e=>!!e&&Number.isInteger(Number(e)),ye=e=>e.endsWith("%")&&x(e.slice(0,-1)),D=e=>cr.test(e),mr=()=>!0,pr=e=>lr.test(e)&&!ur.test(e),wt=()=>!1,vr=e=>dr.test(e),hr=e=>fr.test(e),gr=e=>!m(e)&&!p(e),br=e=>Z(e,Et,wt),m=e=>bt.test(e),V=e=>Z(e,Ct,pr),we=e=>Z(e,Er,x),Ye=e=>Z(e,xt,wt),yr=e=>Z(e,kt,hr),se=e=>Z(e,St,vr),p=e=>yt.test(e),Q=e=>q(e,Ct),wr=e=>q(e,Cr),Ze=e=>q(e,xt),xr=e=>q(e,Et),kr=e=>q(e,kt),ie=e=>q(e,St,!0),Z=(e,t,r)=>{const n=bt.exec(e);return n?n[1]?t(n[1]):r(n[2]):!1},q=(e,t,r=!1)=>{const n=yt.exec(e);return n?n[1]?t(n[1]):r:!1},xt=e=>e==="position"||e==="percentage",kt=e=>e==="image"||e==="url",Et=e=>e==="length"||e==="size"||e==="bg-size",Ct=e=>e==="length",Er=e=>e==="number",Cr=e=>e==="family-name",St=e=>e==="shadow",Sr=()=>{const e=M("color"),t=M("font"),r=M("text"),n=M("font-weight"),o=M("tracking"),a=M("leading"),i=M("breakpoint"),s=M("container"),u=M("spacing"),d=M("radius"),f=M("shadow"),v=M("inset-shadow"),h=M("text-shadow"),k=M("drop-shadow"),E=M("blur"),l=M("perspective"),b=M("aspect"),w=M("ease"),A=M("animate"),P=()=>["auto","avoid","all","avoid-page","page","left","right","column"],S=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],R=()=>[...S(),p,m],T=()=>["auto","hidden","clip","visible","scroll"],C=()=>["auto","contain","none"],g=()=>[p,m,u],O=()=>[U,"full","auto",...g()],G=()=>[_,"none","subgrid",p,m],je=()=>["auto",{span:["full",_,p,m]},_,p,m],te=()=>[_,"auto",p,m],De=()=>["auto","min","max","fr",p,m],he=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],$=()=>["start","end","center","stretch","center-safe","end-safe"],j=()=>["auto",...g()],W=()=>[U,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...g()],y=()=>[e,p,m],_e=()=>[...S(),Ze,Ye,{position:[p,m]}],Be=()=>["no-repeat",{repeat:["","x","y","space","round"]}],We=()=>["auto","cover","contain",xr,br,{size:[p,m]}],ge=()=>[ye,Q,V],I=()=>["","none","full",d,p,m],z=()=>["",x,Q,V],re=()=>["solid","dashed","dotted","double"],Ve=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],L=()=>[x,ye,Ze,Ye],Ge=()=>["","none",E,p,m],ne=()=>["none",x,p,m],oe=()=>["none",x,p,m],be=()=>[x,p,m],ae=()=>[U,"full",...g()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[D],breakpoint:[D],color:[mr],container:[D],"drop-shadow":[D],ease:["in","out","in-out"],font:[gr],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[D],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[D],shadow:[D],spacing:["px",x],text:[D],"text-shadow":[D],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",U,m,p,b]}],container:["container"],columns:[{columns:[x,m,p,s]}],"break-after":[{"break-after":P()}],"break-before":[{"break-before":P()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:R()}],overflow:[{overflow:T()}],"overflow-x":[{"overflow-x":T()}],"overflow-y":[{"overflow-y":T()}],overscroll:[{overscroll:C()}],"overscroll-x":[{"overscroll-x":C()}],"overscroll-y":[{"overscroll-y":C()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:O()}],"inset-x":[{"inset-x":O()}],"inset-y":[{"inset-y":O()}],start:[{start:O()}],end:[{end:O()}],top:[{top:O()}],right:[{right:O()}],bottom:[{bottom:O()}],left:[{left:O()}],visibility:["visible","invisible","collapse"],z:[{z:[_,"auto",p,m]}],basis:[{basis:[U,"full","auto",s,...g()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[x,U,"auto","initial","none",m]}],grow:[{grow:["",x,p,m]}],shrink:[{shrink:["",x,p,m]}],order:[{order:[_,"first","last","none",p,m]}],"grid-cols":[{"grid-cols":G()}],"col-start-end":[{col:je()}],"col-start":[{"col-start":te()}],"col-end":[{"col-end":te()}],"grid-rows":[{"grid-rows":G()}],"row-start-end":[{row:je()}],"row-start":[{"row-start":te()}],"row-end":[{"row-end":te()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":De()}],"auto-rows":[{"auto-rows":De()}],gap:[{gap:g()}],"gap-x":[{"gap-x":g()}],"gap-y":[{"gap-y":g()}],"justify-content":[{justify:[...he(),"normal"]}],"justify-items":[{"justify-items":[...$(),"normal"]}],"justify-self":[{"justify-self":["auto",...$()]}],"align-content":[{content:["normal",...he()]}],"align-items":[{items:[...$(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...$(),{baseline:["","last"]}]}],"place-content":[{"place-content":he()}],"place-items":[{"place-items":[...$(),"baseline"]}],"place-self":[{"place-self":["auto",...$()]}],p:[{p:g()}],px:[{px:g()}],py:[{py:g()}],ps:[{ps:g()}],pe:[{pe:g()}],pt:[{pt:g()}],pr:[{pr:g()}],pb:[{pb:g()}],pl:[{pl:g()}],m:[{m:j()}],mx:[{mx:j()}],my:[{my:j()}],ms:[{ms:j()}],me:[{me:j()}],mt:[{mt:j()}],mr:[{mr:j()}],mb:[{mb:j()}],ml:[{ml:j()}],"space-x":[{"space-x":g()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":g()}],"space-y-reverse":["space-y-reverse"],size:[{size:W()}],w:[{w:[s,"screen",...W()]}],"min-w":[{"min-w":[s,"screen","none",...W()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[i]},...W()]}],h:[{h:["screen","lh",...W()]}],"min-h":[{"min-h":["screen","lh","none",...W()]}],"max-h":[{"max-h":["screen","lh",...W()]}],"font-size":[{text:["base",r,Q,V]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,p,we]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",ye,m]}],"font-family":[{font:[wr,m,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,p,m]}],"line-clamp":[{"line-clamp":[x,"none",p,we]}],leading:[{leading:[a,...g()]}],"list-image":[{"list-image":["none",p,m]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",p,m]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:y()}],"text-color":[{text:y()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...re(),"wavy"]}],"text-decoration-thickness":[{decoration:[x,"from-font","auto",p,V]}],"text-decoration-color":[{decoration:y()}],"underline-offset":[{"underline-offset":[x,"auto",p,m]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:g()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",p,m]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",p,m]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:_e()}],"bg-repeat":[{bg:Be()}],"bg-size":[{bg:We()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},_,p,m],radial:["",p,m],conic:[_,p,m]},kr,yr]}],"bg-color":[{bg:y()}],"gradient-from-pos":[{from:ge()}],"gradient-via-pos":[{via:ge()}],"gradient-to-pos":[{to:ge()}],"gradient-from":[{from:y()}],"gradient-via":[{via:y()}],"gradient-to":[{to:y()}],rounded:[{rounded:I()}],"rounded-s":[{"rounded-s":I()}],"rounded-e":[{"rounded-e":I()}],"rounded-t":[{"rounded-t":I()}],"rounded-r":[{"rounded-r":I()}],"rounded-b":[{"rounded-b":I()}],"rounded-l":[{"rounded-l":I()}],"rounded-ss":[{"rounded-ss":I()}],"rounded-se":[{"rounded-se":I()}],"rounded-ee":[{"rounded-ee":I()}],"rounded-es":[{"rounded-es":I()}],"rounded-tl":[{"rounded-tl":I()}],"rounded-tr":[{"rounded-tr":I()}],"rounded-br":[{"rounded-br":I()}],"rounded-bl":[{"rounded-bl":I()}],"border-w":[{border:z()}],"border-w-x":[{"border-x":z()}],"border-w-y":[{"border-y":z()}],"border-w-s":[{"border-s":z()}],"border-w-e":[{"border-e":z()}],"border-w-t":[{"border-t":z()}],"border-w-r":[{"border-r":z()}],"border-w-b":[{"border-b":z()}],"border-w-l":[{"border-l":z()}],"divide-x":[{"divide-x":z()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":z()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...re(),"hidden","none"]}],"divide-style":[{divide:[...re(),"hidden","none"]}],"border-color":[{border:y()}],"border-color-x":[{"border-x":y()}],"border-color-y":[{"border-y":y()}],"border-color-s":[{"border-s":y()}],"border-color-e":[{"border-e":y()}],"border-color-t":[{"border-t":y()}],"border-color-r":[{"border-r":y()}],"border-color-b":[{"border-b":y()}],"border-color-l":[{"border-l":y()}],"divide-color":[{divide:y()}],"outline-style":[{outline:[...re(),"none","hidden"]}],"outline-offset":[{"outline-offset":[x,p,m]}],"outline-w":[{outline:["",x,Q,V]}],"outline-color":[{outline:y()}],shadow:[{shadow:["","none",f,ie,se]}],"shadow-color":[{shadow:y()}],"inset-shadow":[{"inset-shadow":["none",v,ie,se]}],"inset-shadow-color":[{"inset-shadow":y()}],"ring-w":[{ring:z()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:y()}],"ring-offset-w":[{"ring-offset":[x,V]}],"ring-offset-color":[{"ring-offset":y()}],"inset-ring-w":[{"inset-ring":z()}],"inset-ring-color":[{"inset-ring":y()}],"text-shadow":[{"text-shadow":["none",h,ie,se]}],"text-shadow-color":[{"text-shadow":y()}],opacity:[{opacity:[x,p,m]}],"mix-blend":[{"mix-blend":[...Ve(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Ve()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[x]}],"mask-image-linear-from-pos":[{"mask-linear-from":L()}],"mask-image-linear-to-pos":[{"mask-linear-to":L()}],"mask-image-linear-from-color":[{"mask-linear-from":y()}],"mask-image-linear-to-color":[{"mask-linear-to":y()}],"mask-image-t-from-pos":[{"mask-t-from":L()}],"mask-image-t-to-pos":[{"mask-t-to":L()}],"mask-image-t-from-color":[{"mask-t-from":y()}],"mask-image-t-to-color":[{"mask-t-to":y()}],"mask-image-r-from-pos":[{"mask-r-from":L()}],"mask-image-r-to-pos":[{"mask-r-to":L()}],"mask-image-r-from-color":[{"mask-r-from":y()}],"mask-image-r-to-color":[{"mask-r-to":y()}],"mask-image-b-from-pos":[{"mask-b-from":L()}],"mask-image-b-to-pos":[{"mask-b-to":L()}],"mask-image-b-from-color":[{"mask-b-from":y()}],"mask-image-b-to-color":[{"mask-b-to":y()}],"mask-image-l-from-pos":[{"mask-l-from":L()}],"mask-image-l-to-pos":[{"mask-l-to":L()}],"mask-image-l-from-color":[{"mask-l-from":y()}],"mask-image-l-to-color":[{"mask-l-to":y()}],"mask-image-x-from-pos":[{"mask-x-from":L()}],"mask-image-x-to-pos":[{"mask-x-to":L()}],"mask-image-x-from-color":[{"mask-x-from":y()}],"mask-image-x-to-color":[{"mask-x-to":y()}],"mask-image-y-from-pos":[{"mask-y-from":L()}],"mask-image-y-to-pos":[{"mask-y-to":L()}],"mask-image-y-from-color":[{"mask-y-from":y()}],"mask-image-y-to-color":[{"mask-y-to":y()}],"mask-image-radial":[{"mask-radial":[p,m]}],"mask-image-radial-from-pos":[{"mask-radial-from":L()}],"mask-image-radial-to-pos":[{"mask-radial-to":L()}],"mask-image-radial-from-color":[{"mask-radial-from":y()}],"mask-image-radial-to-color":[{"mask-radial-to":y()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":S()}],"mask-image-conic-pos":[{"mask-conic":[x]}],"mask-image-conic-from-pos":[{"mask-conic-from":L()}],"mask-image-conic-to-pos":[{"mask-conic-to":L()}],"mask-image-conic-from-color":[{"mask-conic-from":y()}],"mask-image-conic-to-color":[{"mask-conic-to":y()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:_e()}],"mask-repeat":[{mask:Be()}],"mask-size":[{mask:We()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",p,m]}],filter:[{filter:["","none",p,m]}],blur:[{blur:Ge()}],brightness:[{brightness:[x,p,m]}],contrast:[{contrast:[x,p,m]}],"drop-shadow":[{"drop-shadow":["","none",k,ie,se]}],"drop-shadow-color":[{"drop-shadow":y()}],grayscale:[{grayscale:["",x,p,m]}],"hue-rotate":[{"hue-rotate":[x,p,m]}],invert:[{invert:["",x,p,m]}],saturate:[{saturate:[x,p,m]}],sepia:[{sepia:["",x,p,m]}],"backdrop-filter":[{"backdrop-filter":["","none",p,m]}],"backdrop-blur":[{"backdrop-blur":Ge()}],"backdrop-brightness":[{"backdrop-brightness":[x,p,m]}],"backdrop-contrast":[{"backdrop-contrast":[x,p,m]}],"backdrop-grayscale":[{"backdrop-grayscale":["",x,p,m]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[x,p,m]}],"backdrop-invert":[{"backdrop-invert":["",x,p,m]}],"backdrop-opacity":[{"backdrop-opacity":[x,p,m]}],"backdrop-saturate":[{"backdrop-saturate":[x,p,m]}],"backdrop-sepia":[{"backdrop-sepia":["",x,p,m]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":g()}],"border-spacing-x":[{"border-spacing-x":g()}],"border-spacing-y":[{"border-spacing-y":g()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",p,m]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[x,"initial",p,m]}],ease:[{ease:["linear","initial",w,p,m]}],delay:[{delay:[x,p,m]}],animate:[{animate:["none",A,p,m]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[l,p,m]}],"perspective-origin":[{"perspective-origin":R()}],rotate:[{rotate:ne()}],"rotate-x":[{"rotate-x":ne()}],"rotate-y":[{"rotate-y":ne()}],"rotate-z":[{"rotate-z":ne()}],scale:[{scale:oe()}],"scale-x":[{"scale-x":oe()}],"scale-y":[{"scale-y":oe()}],"scale-z":[{"scale-z":oe()}],"scale-3d":["scale-3d"],skew:[{skew:be()}],"skew-x":[{"skew-x":be()}],"skew-y":[{"skew-y":be()}],transform:[{transform:[p,m,"","none","gpu","cpu"]}],"transform-origin":[{origin:R()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:ae()}],"translate-x":[{"translate-x":ae()}],"translate-y":[{"translate-y":ae()}],"translate-z":[{"translate-z":ae()}],"translate-none":["translate-none"],accent:[{accent:y()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:y()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",p,m]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":g()}],"scroll-mx":[{"scroll-mx":g()}],"scroll-my":[{"scroll-my":g()}],"scroll-ms":[{"scroll-ms":g()}],"scroll-me":[{"scroll-me":g()}],"scroll-mt":[{"scroll-mt":g()}],"scroll-mr":[{"scroll-mr":g()}],"scroll-mb":[{"scroll-mb":g()}],"scroll-ml":[{"scroll-ml":g()}],"scroll-p":[{"scroll-p":g()}],"scroll-px":[{"scroll-px":g()}],"scroll-py":[{"scroll-py":g()}],"scroll-ps":[{"scroll-ps":g()}],"scroll-pe":[{"scroll-pe":g()}],"scroll-pt":[{"scroll-pt":g()}],"scroll-pr":[{"scroll-pr":g()}],"scroll-pb":[{"scroll-pb":g()}],"scroll-pl":[{"scroll-pl":g()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",p,m]}],fill:[{fill:["none",...y()]}],"stroke-w":[{stroke:[x,Q,V,we]}],stroke:[{stroke:["none",...y()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Pr=sr(Sr);function ee(...e){return Pr(vt(e))}const Rr=Kt("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function Zn({className:e,variant:t,size:r,asChild:n=!1,...o}){const a=n?Wt:"button";return N.jsx(a,{"data-slot":"button",className:ee(Rr({variant:t,size:r,className:e})),...o})}var Pt=_t();const Ar=Bt(Pt);var Mr=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],pe=Mr.reduce((e,t)=>{const r=ft(`Primitive.${t}`),n=c.forwardRef((o,a)=>{const{asChild:i,...s}=o,u=i?r:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),N.jsx(u,{...s,ref:a})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function Lr(e,t){e&&Pt.flushSync(()=>e.dispatchEvent(t))}function xe(e,t,{checkForDefaultPrevented:r=!0}={}){return function(o){if(e==null||e(o),r===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function qn(e,t){const r=c.createContext(t),n=a=>{const{children:i,...s}=a,u=c.useMemo(()=>s,Object.values(s));return N.jsx(r.Provider,{value:u,children:i})};n.displayName=e+"Provider";function o(a){const i=c.useContext(r);if(i)return i;if(t!==void 0)return t;throw new Error(`\`${a}\` must be used within \`${e}\``)}return[n,o]}function Qn(e,t=[]){let r=[];function n(a,i){const s=c.createContext(i),u=r.length;r=[...r,i];const d=v=>{var w;const{scope:h,children:k,...E}=v,l=((w=h==null?void 0:h[e])==null?void 0:w[u])||s,b=c.useMemo(()=>E,Object.values(E));return N.jsx(l.Provider,{value:b,children:k})};d.displayName=a+"Provider";function f(v,h){var l;const k=((l=h==null?void 0:h[e])==null?void 0:l[u])||s,E=c.useContext(k);if(E)return E;if(i!==void 0)return i;throw new Error(`\`${v}\` must be used within \`${a}\``)}return[d,f]}const o=()=>{const a=r.map(i=>c.createContext(i));return function(s){const u=(s==null?void 0:s[e])||a;return c.useMemo(()=>({[`__scope${e}`]:{...s,[e]:u}}),[s,u])}};return o.scopeName=e,[n,Tr(o,...t)]}function Tr(...e){const t=e[0];if(e.length===1)return t;const r=()=>{const n=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(a){const i=n.reduce((s,{useScope:u,scopeName:d})=>{const v=u(a)[`__scope${d}`];return{...s,...v}},{});return c.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return r.scopeName=t.scopeName,r}function J(e){const t=c.useRef(e);return c.useEffect(()=>{t.current=e}),c.useMemo(()=>(...r)=>{var n;return(n=t.current)==null?void 0:n.call(t,...r)},[])}function Nr(e,t=globalThis==null?void 0:globalThis.document){const r=J(e);c.useEffect(()=>{const n=o=>{o.key==="Escape"&&r(o)};return t.addEventListener("keydown",n,{capture:!0}),()=>t.removeEventListener("keydown",n,{capture:!0})},[r,t])}var Or="DismissableLayer",Ne="dismissableLayer.update",Ir="dismissableLayer.pointerDownOutside",zr="dismissableLayer.focusOutside",qe,Rt=c.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Fr=c.forwardRef((e,t)=>{const{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:n,onPointerDownOutside:o,onFocusOutside:a,onInteractOutside:i,onDismiss:s,...u}=e,d=c.useContext(Rt),[f,v]=c.useState(null),h=(f==null?void 0:f.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,k]=c.useState({}),E=Ie(t,C=>v(C)),l=Array.from(d.layers),[b]=[...d.layersWithOutsidePointerEventsDisabled].slice(-1),w=l.indexOf(b),A=f?l.indexOf(f):-1,P=d.layersWithOutsidePointerEventsDisabled.size>0,S=A>=w,R=_r(C=>{const g=C.target,O=[...d.branches].some(G=>G.contains(g));!S||O||(o==null||o(C),i==null||i(C),C.defaultPrevented||s==null||s())},h),T=Br(C=>{const g=C.target;[...d.branches].some(G=>G.contains(g))||(a==null||a(C),i==null||i(C),C.defaultPrevented||s==null||s())},h);return Nr(C=>{A===d.layers.size-1&&(n==null||n(C),!C.defaultPrevented&&s&&(C.preventDefault(),s()))},h),c.useEffect(()=>{if(f)return r&&(d.layersWithOutsidePointerEventsDisabled.size===0&&(qe=h.body.style.pointerEvents,h.body.style.pointerEvents="none"),d.layersWithOutsidePointerEventsDisabled.add(f)),d.layers.add(f),Qe(),()=>{r&&d.layersWithOutsidePointerEventsDisabled.size===1&&(h.body.style.pointerEvents=qe)}},[f,h,r,d]),c.useEffect(()=>()=>{f&&(d.layers.delete(f),d.layersWithOutsidePointerEventsDisabled.delete(f),Qe())},[f,d]),c.useEffect(()=>{const C=()=>k({});return document.addEventListener(Ne,C),()=>document.removeEventListener(Ne,C)},[]),N.jsx(pe.div,{...u,ref:E,style:{pointerEvents:P?S?"auto":"none":void 0,...e.style},onFocusCapture:xe(e.onFocusCapture,T.onFocusCapture),onBlurCapture:xe(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:xe(e.onPointerDownCapture,R.onPointerDownCapture)})});Fr.displayName=Or;var jr="DismissableLayerBranch",Dr=c.forwardRef((e,t)=>{const r=c.useContext(Rt),n=c.useRef(null),o=Ie(t,n);return c.useEffect(()=>{const a=n.current;if(a)return r.branches.add(a),()=>{r.branches.delete(a)}},[r.branches]),N.jsx(pe.div,{...e,ref:o})});Dr.displayName=jr;function _r(e,t=globalThis==null?void 0:globalThis.document){const r=J(e),n=c.useRef(!1),o=c.useRef(()=>{});return c.useEffect(()=>{const a=s=>{if(s.target&&!n.current){let u=function(){At(Ir,r,d,{discrete:!0})};const d={originalEvent:s};s.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=u,t.addEventListener("click",o.current,{once:!0})):u()}else t.removeEventListener("click",o.current);n.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",a),t.removeEventListener("click",o.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}function Br(e,t=globalThis==null?void 0:globalThis.document){const r=J(e),n=c.useRef(!1);return c.useEffect(()=>{const o=a=>{a.target&&!n.current&&At(zr,r,{originalEvent:a},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}function Qe(){const e=new CustomEvent(Ne);document.dispatchEvent(e)}function At(e,t,r,{discrete:n}){const o=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),n?Lr(o,a):o.dispatchEvent(a)}var ke=0;function Jn(){c.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Je()),document.body.insertAdjacentElement("beforeend",e[1]??Je()),ke++,()=>{ke===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),ke--}},[])}function Je(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Ee="focusScope.autoFocusOnMount",Ce="focusScope.autoFocusOnUnmount",et={bubbles:!1,cancelable:!0},Wr="FocusScope",Vr=c.forwardRef((e,t)=>{const{loop:r=!1,trapped:n=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...i}=e,[s,u]=c.useState(null),d=J(o),f=J(a),v=c.useRef(null),h=Ie(t,l=>u(l)),k=c.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;c.useEffect(()=>{if(n){let l=function(P){if(k.paused||!s)return;const S=P.target;s.contains(S)?v.current=S:B(v.current,{select:!0})},b=function(P){if(k.paused||!s)return;const S=P.relatedTarget;S!==null&&(s.contains(S)||B(v.current,{select:!0}))},w=function(P){if(document.activeElement===document.body)for(const R of P)R.removedNodes.length>0&&B(s)};document.addEventListener("focusin",l),document.addEventListener("focusout",b);const A=new MutationObserver(w);return s&&A.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",l),document.removeEventListener("focusout",b),A.disconnect()}}},[n,s,k.paused]),c.useEffect(()=>{if(s){rt.add(k);const l=document.activeElement;if(!s.contains(l)){const w=new CustomEvent(Ee,et);s.addEventListener(Ee,d),s.dispatchEvent(w),w.defaultPrevented||(Gr(Xr(Mt(s)),{select:!0}),document.activeElement===l&&B(s))}return()=>{s.removeEventListener(Ee,d),setTimeout(()=>{const w=new CustomEvent(Ce,et);s.addEventListener(Ce,f),s.dispatchEvent(w),w.defaultPrevented||B(l??document.body,{select:!0}),s.removeEventListener(Ce,f),rt.remove(k)},0)}}},[s,d,f,k]);const E=c.useCallback(l=>{if(!r&&!n||k.paused)return;const b=l.key==="Tab"&&!l.altKey&&!l.ctrlKey&&!l.metaKey,w=document.activeElement;if(b&&w){const A=l.currentTarget,[P,S]=$r(A);P&&S?!l.shiftKey&&w===S?(l.preventDefault(),r&&B(P,{select:!0})):l.shiftKey&&w===P&&(l.preventDefault(),r&&B(S,{select:!0})):w===A&&l.preventDefault()}},[r,n,k.paused]);return N.jsx(pe.div,{tabIndex:-1,...i,ref:h,onKeyDown:E})});Vr.displayName=Wr;function Gr(e,{select:t=!1}={}){const r=document.activeElement;for(const n of e)if(B(n,{select:t}),document.activeElement!==r)return}function $r(e){const t=Mt(e),r=tt(t,e),n=tt(t.reverse(),e);return[r,n]}function Mt(e){const t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:n=>{const o=n.tagName==="INPUT"&&n.type==="hidden";return n.disabled||n.hidden||o?NodeFilter.FILTER_SKIP:n.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function tt(e,t){for(const r of e)if(!Ur(r,{upTo:t}))return r}function Ur(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Kr(e){return e instanceof HTMLInputElement&&"select"in e}function B(e,{select:t=!1}={}){if(e&&e.focus){const r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&Kr(e)&&t&&e.select()}}var rt=Hr();function Hr(){let e=[];return{add(t){const r=e[0];t!==r&&(r==null||r.pause()),e=nt(e,t),e.unshift(t)},remove(t){var r;e=nt(e,t),(r=e[0])==null||r.resume()}}}function nt(e,t){const r=[...e],n=r.indexOf(t);return n!==-1&&r.splice(n,1),r}function Xr(e){return e.filter(t=>t.tagName!=="A")}var Fe=globalThis!=null&&globalThis.document?c.useLayoutEffect:()=>{},Yr=ut[" useId ".trim().toString()]||(()=>{}),Zr=0;function eo(e){const[t,r]=c.useState(Yr());return Fe(()=>{r(n=>n??String(Zr++))},[e]),e||(t?`radix-${t}`:"")}var qr="Portal",Qr=c.forwardRef((e,t)=>{var s;const{container:r,...n}=e,[o,a]=c.useState(!1);Fe(()=>a(!0),[]);const i=r||o&&((s=globalThis==null?void 0:globalThis.document)==null?void 0:s.body);return i?Ar.createPortal(N.jsx(pe.div,{...n,ref:t}),i):null});Qr.displayName=qr;var Jr=ut[" useInsertionEffect ".trim().toString()]||Fe;function to({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){const[o,a,i]=en({defaultProp:t,onChange:r}),s=e!==void 0,u=s?e:o;{const f=c.useRef(e!==void 0);c.useEffect(()=>{const v=f.current;v!==s&&console.warn(`${n} is changing from ${v?"controlled":"uncontrolled"} to ${s?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),f.current=s},[s,n])}const d=c.useCallback(f=>{var v;if(s){const h=tn(f)?f(e):f;h!==e&&((v=i.current)==null||v.call(i,h))}else a(f)},[s,e,a,i]);return[u,d]}function en({defaultProp:e,onChange:t}){const[r,n]=c.useState(e),o=c.useRef(r),a=c.useRef(t);return Jr(()=>{a.current=t},[t]),c.useEffect(()=>{var i;o.current!==r&&((i=a.current)==null||i.call(a,r),o.current=r)},[r,o]),[r,n,a]}function tn(e){return typeof e=="function"}var rn=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},K=new WeakMap,ce=new WeakMap,le={},Se=0,Lt=function(e){return e&&(e.host||Lt(e.parentNode))},nn=function(e,t){return t.map(function(r){if(e.contains(r))return r;var n=Lt(r);return n&&e.contains(n)?n:(console.error("aria-hidden",r,"in not contained inside",e,". Doing nothing"),null)}).filter(function(r){return!!r})},on=function(e,t,r,n){var o=nn(t,Array.isArray(e)?e:[e]);le[r]||(le[r]=new WeakMap);var a=le[r],i=[],s=new Set,u=new Set(o),d=function(v){!v||s.has(v)||(s.add(v),d(v.parentNode))};o.forEach(d);var f=function(v){!v||u.has(v)||Array.prototype.forEach.call(v.children,function(h){if(s.has(h))f(h);else try{var k=h.getAttribute(n),E=k!==null&&k!=="false",l=(K.get(h)||0)+1,b=(a.get(h)||0)+1;K.set(h,l),a.set(h,b),i.push(h),l===1&&E&&ce.set(h,!0),b===1&&h.setAttribute(r,"true"),E||h.setAttribute(n,"true")}catch(w){console.error("aria-hidden: cannot operate on ",h,w)}})};return f(t),s.clear(),Se++,function(){i.forEach(function(v){var h=K.get(v)-1,k=a.get(v)-1;K.set(v,h),a.set(v,k),h||(ce.has(v)||v.removeAttribute(n),ce.delete(v)),k||v.removeAttribute(r)}),Se--,Se||(K=new WeakMap,K=new WeakMap,ce=new WeakMap,le={})}},ro=function(e,t,r){r===void 0&&(r="data-aria-hidden");var n=Array.from(Array.isArray(e)?e:[e]),o=rn(e);return o?(n.push.apply(n,Array.from(o.querySelectorAll("[aria-live], script"))),on(n,o,r,"aria-hidden")):function(){return null}},F=function(){return F=Object.assign||function(t){for(var r,n=1,o=arguments.length;n<o;n++){r=arguments[n];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(t[a]=r[a])}return t},F.apply(this,arguments)};function Tt(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}function an(e,t,r){if(r||arguments.length===2)for(var n=0,o=t.length,a;n<o;n++)(a||!(n in t))&&(a||(a=Array.prototype.slice.call(t,0,n)),a[n]=t[n]);return e.concat(a||Array.prototype.slice.call(t))}var fe="right-scroll-bar-position",me="width-before-scroll-bar",sn="with-scroll-bars-hidden",cn="--removed-body-scroll-bar-size";function Pe(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function ln(e,t){var r=c.useState(function(){return{value:e,callback:t,facade:{get current(){return r.value},set current(n){var o=r.value;o!==n&&(r.value=n,r.callback(n,o))}}}})[0];return r.callback=t,r.facade}var un=typeof window<"u"?c.useLayoutEffect:c.useEffect,ot=new WeakMap;function dn(e,t){var r=ln(null,function(n){return e.forEach(function(o){return Pe(o,n)})});return un(function(){var n=ot.get(r);if(n){var o=new Set(n),a=new Set(e),i=r.current;o.forEach(function(s){a.has(s)||Pe(s,null)}),a.forEach(function(s){o.has(s)||Pe(s,i)})}ot.set(r,e)},[e]),r}function fn(e){return e}function mn(e,t){t===void 0&&(t=fn);var r=[],n=!1,o={read:function(){if(n)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:e},useMedium:function(a){var i=t(a,n);return r.push(i),function(){r=r.filter(function(s){return s!==i})}},assignSyncMedium:function(a){for(n=!0;r.length;){var i=r;r=[],i.forEach(a)}r={push:function(s){return a(s)},filter:function(){return r}}},assignMedium:function(a){n=!0;var i=[];if(r.length){var s=r;r=[],s.forEach(a),i=r}var u=function(){var f=i;i=[],f.forEach(a)},d=function(){return Promise.resolve().then(u)};d(),r={push:function(f){i.push(f),d()},filter:function(f){return i=i.filter(f),r}}}};return o}function pn(e){e===void 0&&(e={});var t=mn(null);return t.options=F({async:!0,ssr:!1},e),t}var Nt=function(e){var t=e.sideCar,r=Tt(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw new Error("Sidecar medium not found");return c.createElement(n,F({},r))};Nt.isSideCarExport=!0;function vn(e,t){return e.useMedium(t),Nt}var Ot=pn(),Re=function(){},ve=c.forwardRef(function(e,t){var r=c.useRef(null),n=c.useState({onScrollCapture:Re,onWheelCapture:Re,onTouchMoveCapture:Re}),o=n[0],a=n[1],i=e.forwardProps,s=e.children,u=e.className,d=e.removeScrollBar,f=e.enabled,v=e.shards,h=e.sideCar,k=e.noRelative,E=e.noIsolation,l=e.inert,b=e.allowPinchZoom,w=e.as,A=w===void 0?"div":w,P=e.gapMode,S=Tt(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),R=h,T=dn([r,t]),C=F(F({},S),o);return c.createElement(c.Fragment,null,f&&c.createElement(R,{sideCar:Ot,removeScrollBar:d,shards:v,noRelative:k,noIsolation:E,inert:l,setCallbacks:a,allowPinchZoom:!!b,lockRef:r,gapMode:P}),i?c.cloneElement(c.Children.only(s),F(F({},C),{ref:T})):c.createElement(A,F({},C,{className:u,ref:T}),s))});ve.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};ve.classNames={fullWidth:me,zeroRight:fe};var hn=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function gn(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=hn();return t&&e.setAttribute("nonce",t),e}function bn(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function yn(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var wn=function(){var e=0,t=null;return{add:function(r){e==0&&(t=gn())&&(bn(t,r),yn(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},xn=function(){var e=wn();return function(t,r){c.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},It=function(){var e=xn(),t=function(r){var n=r.styles,o=r.dynamic;return e(n,o),null};return t},kn={left:0,top:0,right:0,gap:0},Ae=function(e){return parseInt(e||"",10)||0},En=function(e){var t=window.getComputedStyle(document.body),r=t[e==="padding"?"paddingLeft":"marginLeft"],n=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[Ae(r),Ae(n),Ae(o)]},Cn=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return kn;var t=En(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},Sn=It(),Y="data-scroll-locked",Pn=function(e,t,r,n){var o=e.left,a=e.top,i=e.right,s=e.gap;return r===void 0&&(r="margin"),`
  .`.concat(sn,` {
   overflow: hidden `).concat(n,`;
   padding-right: `).concat(s,"px ").concat(n,`;
  }
  body[`).concat(Y,`] {
    overflow: hidden `).concat(n,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(n,";"),r==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(a,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(s,"px ").concat(n,`;
    `),r==="padding"&&"padding-right: ".concat(s,"px ").concat(n,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(fe,` {
    right: `).concat(s,"px ").concat(n,`;
  }
  
  .`).concat(me,` {
    margin-right: `).concat(s,"px ").concat(n,`;
  }
  
  .`).concat(fe," .").concat(fe,` {
    right: 0 `).concat(n,`;
  }
  
  .`).concat(me," .").concat(me,` {
    margin-right: 0 `).concat(n,`;
  }
  
  body[`).concat(Y,`] {
    `).concat(cn,": ").concat(s,`px;
  }
`)},at=function(){var e=parseInt(document.body.getAttribute(Y)||"0",10);return isFinite(e)?e:0},Rn=function(){c.useEffect(function(){return document.body.setAttribute(Y,(at()+1).toString()),function(){var e=at()-1;e<=0?document.body.removeAttribute(Y):document.body.setAttribute(Y,e.toString())}},[])},An=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=n===void 0?"margin":n;Rn();var a=c.useMemo(function(){return Cn(o)},[o]);return c.createElement(Sn,{styles:Pn(a,!t,o,r?"":"!important")})},Oe=!1;if(typeof window<"u")try{var ue=Object.defineProperty({},"passive",{get:function(){return Oe=!0,!0}});window.addEventListener("test",ue,ue),window.removeEventListener("test",ue,ue)}catch{Oe=!1}var H=Oe?{passive:!1}:!1,Mn=function(e){return e.tagName==="TEXTAREA"},zt=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return r[t]!=="hidden"&&!(r.overflowY===r.overflowX&&!Mn(e)&&r[t]==="visible")},Ln=function(e){return zt(e,"overflowY")},Tn=function(e){return zt(e,"overflowX")},st=function(e,t){var r=t.ownerDocument,n=t;do{typeof ShadowRoot<"u"&&n instanceof ShadowRoot&&(n=n.host);var o=Ft(e,n);if(o){var a=jt(e,n),i=a[1],s=a[2];if(i>s)return!0}n=n.parentNode}while(n&&n!==r.body);return!1},Nn=function(e){var t=e.scrollTop,r=e.scrollHeight,n=e.clientHeight;return[t,r,n]},On=function(e){var t=e.scrollLeft,r=e.scrollWidth,n=e.clientWidth;return[t,r,n]},Ft=function(e,t){return e==="v"?Ln(t):Tn(t)},jt=function(e,t){return e==="v"?Nn(t):On(t)},In=function(e,t){return e==="h"&&t==="rtl"?-1:1},zn=function(e,t,r,n,o){var a=In(e,window.getComputedStyle(t).direction),i=a*n,s=r.target,u=t.contains(s),d=!1,f=i>0,v=0,h=0;do{var k=jt(e,s),E=k[0],l=k[1],b=k[2],w=l-b-a*E;(E||w)&&Ft(e,s)&&(v+=w,h+=E),s=s.parentNode.host||s.parentNode}while(!u&&s!==document.body||u&&(t.contains(s)||t===s));return(f&&Math.abs(v)<1||!f&&Math.abs(h)<1)&&(d=!0),d},de=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},it=function(e){return[e.deltaX,e.deltaY]},ct=function(e){return e&&"current"in e?e.current:e},Fn=function(e,t){return e[0]===t[0]&&e[1]===t[1]},jn=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},Dn=0,X=[];function _n(e){var t=c.useRef([]),r=c.useRef([0,0]),n=c.useRef(),o=c.useState(Dn++)[0],a=c.useState(It)[0],i=c.useRef(e);c.useEffect(function(){i.current=e},[e]),c.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var l=an([e.lockRef.current],(e.shards||[]).map(ct),!0).filter(Boolean);return l.forEach(function(b){return b.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),l.forEach(function(b){return b.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=c.useCallback(function(l,b){if("touches"in l&&l.touches.length===2||l.type==="wheel"&&l.ctrlKey)return!i.current.allowPinchZoom;var w=de(l),A=r.current,P="deltaX"in l?l.deltaX:A[0]-w[0],S="deltaY"in l?l.deltaY:A[1]-w[1],R,T=l.target,C=Math.abs(P)>Math.abs(S)?"h":"v";if("touches"in l&&C==="h"&&T.type==="range")return!1;var g=st(C,T);if(!g)return!0;if(g?R=C:(R=C==="v"?"h":"v",g=st(C,T)),!g)return!1;if(!n.current&&"changedTouches"in l&&(P||S)&&(n.current=R),!R)return!0;var O=n.current||R;return zn(O,b,l,O==="h"?P:S)},[]),u=c.useCallback(function(l){var b=l;if(!(!X.length||X[X.length-1]!==a)){var w="deltaY"in b?it(b):de(b),A=t.current.filter(function(R){return R.name===b.type&&(R.target===b.target||b.target===R.shadowParent)&&Fn(R.delta,w)})[0];if(A&&A.should){b.cancelable&&b.preventDefault();return}if(!A){var P=(i.current.shards||[]).map(ct).filter(Boolean).filter(function(R){return R.contains(b.target)}),S=P.length>0?s(b,P[0]):!i.current.noIsolation;S&&b.cancelable&&b.preventDefault()}}},[]),d=c.useCallback(function(l,b,w,A){var P={name:l,delta:b,target:w,should:A,shadowParent:Bn(w)};t.current.push(P),setTimeout(function(){t.current=t.current.filter(function(S){return S!==P})},1)},[]),f=c.useCallback(function(l){r.current=de(l),n.current=void 0},[]),v=c.useCallback(function(l){d(l.type,it(l),l.target,s(l,e.lockRef.current))},[]),h=c.useCallback(function(l){d(l.type,de(l),l.target,s(l,e.lockRef.current))},[]);c.useEffect(function(){return X.push(a),e.setCallbacks({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:h}),document.addEventListener("wheel",u,H),document.addEventListener("touchmove",u,H),document.addEventListener("touchstart",f,H),function(){X=X.filter(function(l){return l!==a}),document.removeEventListener("wheel",u,H),document.removeEventListener("touchmove",u,H),document.removeEventListener("touchstart",f,H)}},[]);var k=e.removeScrollBar,E=e.inert;return c.createElement(c.Fragment,null,E?c.createElement(a,{styles:jn(o)}):null,k?c.createElement(An,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function Bn(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const Wn=vn(Ot,_n);var Vn=c.forwardRef(function(e,t){return c.createElement(ve,F({},e,{ref:t,sideCar:Wn}))});Vn.classNames=ve.classNames;/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gn=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),$n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,r,n)=>n?n.toUpperCase():r.toLowerCase()),lt=e=>{const t=$n(e);return t.charAt(0).toUpperCase()+t.slice(1)},Dt=(...e)=>e.filter((t,r,n)=>!!t&&t.trim()!==""&&n.indexOf(t)===r).join(" ").trim(),Un=e=>{for(const t in e)if(t.startsWith("aria-")||t==="role"||t==="title")return!0};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Kn={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hn=c.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:o="",children:a,iconNode:i,...s},u)=>c.createElement("svg",{ref:u,...Kn,width:t,height:t,stroke:e,strokeWidth:n?Number(r)*24/Number(t):r,className:Dt("lucide",o),...!a&&!Un(s)&&{"aria-hidden":"true"},...s},[...i.map(([d,f])=>c.createElement(d,f)),...Array.isArray(a)?a:[a]]));/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const no=(e,t)=>{const r=c.forwardRef(({className:n,...o},a)=>c.createElement(Hn,{ref:a,iconNode:t,className:Dt(`lucide-${Gn(lt(e))}`,`lucide-${e}`,n),...o}));return r.displayName=lt(e),r};function oo({className:e,...t}){return N.jsx("div",{"data-slot":"card",className:ee("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function ao({className:e,...t}){return N.jsx("div",{"data-slot":"card-header",className:ee("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function so({className:e,...t}){return N.jsx("div",{"data-slot":"card-title",className:ee("leading-none font-semibold",e),...t})}function io({className:e,...t}){return N.jsx("div",{"data-slot":"card-content",className:ee("px-6",e),...t})}export{Zn as B,oo as C,Fr as D,Vr as F,pe as P,Vn as R,Wt as S,ao as a,so as b,io as c,ee as d,no as e,Kt as f,Qn as g,Ie as h,xe as i,ft as j,Fe as k,J as l,eo as m,Qr as n,ro as o,Jn as p,qn as q,Pt as r,Yn as s,Rr as t,to as u};
