using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace Imip.Ekb.Employees;

public interface IEmployeeRepository : IRepository<Employee, Guid>
{
    Task<List<Employee>> GetListAsync(
        int skipCount,
        int maxResultCount,
        string sorting,
        string? filter = null,
        string? department = null,
        bool? isActive = null,
        CancellationToken cancellationToken = default);

    Task<long> GetCountAsync(
        string? filter = null,
        string? department = null,
        bool? isActive = null,
        CancellationToken cancellationToken = default);

    Task<Employee?> FindByEmailAsync(
        string email,
        CancellationToken cancellationToken = default);

    Task<List<string>> GetDepartmentsAsync(
        CancellationToken cancellationToken = default);

    Task<bool> IsEmailUniqueAsync(
        string email,
        Guid? excludeId = null,
        CancellationToken cancellationToken = default);
}