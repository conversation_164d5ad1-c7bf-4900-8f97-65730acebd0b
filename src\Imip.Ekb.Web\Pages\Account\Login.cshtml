@page
@using Microsoft.AspNetCore.Mvc.Localization
@using Volo.Abp.Account.Localization
@using Volo.Abp.Account.Settings
@using Volo.Abp.Settings
@model Volo.Abp.Account.Web.Pages.Account.LoginModel
@inject IHtmlLocalizer<AccountResource> L
@inject ISettingProvider SettingProvider
@{
    Layout = null;
}

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@L["Login"] - Imip.Ekb</title>

    <!-- Tailwind CSS -->
    <script src="~/libs/tailwind/tailwind-browser.js"></script>

    <script>
        // Configure Tailwind with a custom theme
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        border: "#e2e8f0",
                        input: "#e2e8f0",
                        ring: "#3b82f6",
                        background: "#ffffff",
                        foreground: "#0f1729",
                        primary: {
                            DEFAULT: "#3b82f6",
                            foreground: "#f8fafc",
                        },
                        secondary: {
                            DEFAULT: "#f1f5f9",
                            foreground: "#1e293b",
                        },
                        destructive: {
                            DEFAULT: "#ef4444",
                            foreground: "#f8fafc",
                        },
                        muted: {
                            DEFAULT: "#f1f5f9",
                            foreground: "#64748b",
                        },
                        accent: {
                            DEFAULT: "#f1f5f9",
                            foreground: "#1e293b",
                        },
                        card: {
                            DEFAULT: "#ffffff",
                            foreground: "#0f1729",
                        },
                    },
                    borderRadius: {
                        lg: "0.5rem",
                        md: "calc(0.5rem - 2px)",
                        sm: "calc(0.5rem - 4px)",
                    },
                },
            },
        };
    </script>

    <style>
        /* Define color variables for the theme for non-Tailwind elements */
        :root {
            --background: #ffffff;
            --foreground: #0f1729;
            --card: #ffffff;
            --card-foreground: #0f1729;
            --primary: #3b82f6;
            --primary-foreground: #f8fafc;
            --secondary: #f1f5f9;
            --secondary-foreground: #1e293b;
            --muted: #f1f5f9;
            --muted-foreground: #64748b;
            --accent: #f1f5f9;
            --accent-foreground: #1e293b;
            --destructive: #ef4444;
            --destructive-foreground: #f8fafc;
            --border: #e2e8f0;
            --input: #e2e8f0;
            --ring: #3b82f6;
            --radius: 0.5rem;
        }

        /* Button component */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            height: 2.5rem;
            padding-left: 1rem;
            padding-right: 1rem;
            transition-property: color, background-color, border-color;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            transition-duration: 150ms;
        }

        .btn:focus-visible {
            outline: none;
            box-shadow: 0 0 0 2px var(--ring);
        }

        .btn:disabled {
            pointer-events: none;
            opacity: 0.5;
        }

        .btn-primary {
            background-color: var(--primary);
            color: var(--primary-foreground);
        }

        .btn-primary:hover {
            background-color: #3b82f6e6;
        }

        .btn-outline {
            border: 1px solid var(--input);
            background-color: var(--background);
        }

        .btn-outline:hover {
            background-color: var(--accent);
            color: var(--accent-foreground);
        }

        .btn-full {
            width: 100%;
        }

        /* Card component */
        .card {
            border-radius: 0.5rem;
            border: 1px solid var(--border);
            background-color: var(--card);
            color: var(--card-foreground);
            box-shadow: 0 1px 3px #0000001a;
            /* Equivalent to rgba(0, 0, 0, 0.1) */
        }

        .card-header {
            display: flex;
            flex-direction: column;
            padding: 1.5rem;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            line-height: 1;
            letter-spacing: -0.025em;
            margin-bottom: 0.375rem;
        }

        .card-description {
            font-size: 0.875rem;
            color: var(--muted-foreground);
        }

        .card-content {
            padding: 0 1.5rem 1.5rem 1.5rem;
        }

        /* Input component */
        .input {
            display: flex;
            height: 2.5rem;
            width: 100%;
            border-radius: 0.375rem;
            border: 1px solid var(--input);
            background-color: var(--background);
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
        }

        .input:focus-visible {
            outline: none;
            box-shadow: 0 0 0 2px var(--ring);
        }

        .input:disabled {
            cursor: not-allowed;
            opacity: 0.5;
        }

        /* Label component */
        .label {
            font-size: 0.875rem;
            font-weight: 500;
            line-height: 1;
        }

        /* Validation */
        .validation-error {
            color: var(--destructive);
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        /* Checkbox */
        .checkbox {
            height: 1rem;
            width: 1rem;
            border-radius: 0.25rem;
            border: 1px solid #d1d5db;
        }

        /* Utility classes */
        .flex {
            display: flex;
        }

        .flex-col {
            flex-direction: column;
        }

        .items-center {
            align-items: center;
        }

        .justify-center {
            justify-content: center;
        }

        .space-x-2>*+* {
            margin-left: 0.5rem;
        }

        .space-y-1\.5>*+* {
            margin-top: 0.375rem;
        }

        .gap-2 {
            gap: 0.5rem;
        }

        .gap-6 {
            gap: 1.5rem;
        }

        .min-h-screen {
            min-height: 100vh;
        }

        .w-full {
            width: 100%;
        }

        .max-w-sm {
            max-width: 24rem;
        }

        .p-6 {
            padding: 1.5rem;
        }

        .md\:p-10 {
            padding: 2.5rem;
        }

        .grid {
            display: grid;
        }

        .mt-4 {
            margin-top: 1rem;
        }

        .ml-auto {
            margin-left: auto;
        }

        .text-sm {
            font-size: 0.875rem;
        }

        .text-center {
            text-align: center;
        }

        .underline {
            text-decoration: underline;
        }

        .underline-offset-4 {
            text-underline-offset: 4px;
        }

        .hover\:underline:hover {
            text-decoration: underline;
        }

        .bg-background {
            background-color: var(--background);
        }

        .text-foreground {
            color: var(--foreground);
        }
    </style>
</head>

<body class="bg-background text-foreground">
    <div class="flex min-h-screen w-full items-center justify-center p-6 md:p-10">
        <div class="w-full max-w-sm">
            <div class="flex flex-col gap-6">
                <div class="card">
                    <div class="card-header">
                        <h1 class="card-title">@L["Login"]</h1>
                        <p class="card-description">@L["LoginWithYourAccount"]</p>
                    </div>
                    <div class="card-content">
                        <form method="post" class="abp-account-login-form">
                            <input asp-for="ReturnUrl" type="hidden" />
                            <input asp-for="ReturnUrlHash" type="hidden" />

                            <div class="flex flex-col gap-6">
                                <div class="grid gap-2">
                                    <label asp-for="LoginInput.UserNameOrEmailAddress"
                                        class="label">@L["UserNameOrEmailAddress"]</label>
                                    <input asp-for="LoginInput.UserNameOrEmailAddress" class="input"
                                        autocomplete="username" />
                                    <span asp-validation-for="LoginInput.UserNameOrEmailAddress"
                                        class="validation-error"></span>
                                </div>

                                <div class="grid gap-2">
                                    <div class="flex items-center">
                                        <label asp-for="LoginInput.Password" class="label">@L["Password"]</label>
                                        @if (await SettingProvider.IsTrueAsync(AccountSettingNames.EnableLocalLogin) &&
                                                                                await
                                                                                SettingProvider.IsTrueAsync(AccountSettingNames.IsSelfRegistrationEnabled))
                                        {
                                            <a href="@Url.Page("./ForgotPassword", new { returnUrl = Model.ReturnUrl, returnUrlHash = Model.ReturnUrlHash })"
                                                class="ml-auto inline-block text-sm underline-offset-4 hover:underline">@L["ForgotPassword"]</a>
                                        }
                                    </div>
                                    <input asp-for="LoginInput.Password" class="input"
                                        autocomplete="current-password" />
                                    <span asp-validation-for="LoginInput.Password" class="validation-error"></span>
                                </div>

                                <div class="flex items-center space-x-2">
                                    <input asp-for="LoginInput.RememberMe" type="checkbox" id="remember-me"
                                        class="checkbox" />
                                    <label for="remember-me" class="text-sm">@L["RememberMe"]</label>
                                </div>

                                <button type="submit" class="btn btn-primary btn-full">@L["Login"]</button>

                                @if (Model.ExternalProviders.Any())
                                {
                                    @foreach (var provider in Model.ExternalProviders)
                                    {
                                        <a href="/Account/Login?handler=ExternalLogin&provider=@provider.AuthenticationScheme&returnUrl=@Model.ReturnUrl&returnUrlHash=@Model.ReturnUrlHash"
                                            class="btn btn-outline btn-full">
                                            @L["LoginWith", provider.DisplayName]
                                        </a>
                                    }
                                }
                            </div>

                            @if (await SettingProvider.IsTrueAsync(AccountSettingNames.EnableLocalLogin) && await
                                                        SettingProvider.IsTrueAsync(AccountSettingNames.IsSelfRegistrationEnabled))
                            {
                                <div class="mt-4 text-center text-sm">
                                    @L["AreYouANewUser"]
                                    <a href="@Url.Page("./Register", new { returnUrl = Model.ReturnUrl, returnUrlHash = Model.ReturnUrlHash })"
                                        class="underline underline-offset-4">
                                        @L["Register"]
                                    </a>
                                </div>
                            }
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/libs/jquery/jquery.js"></script>
    <script src="~/libs/jquery-validate/jquery.validate.js"></script>
    <script src="~/libs/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js"></script>
</body>

</html>
