{"id": "1c28463d-b28e-4703-a8ce-491720461bbd", "template": "app", "versions": {"AbpFramework": "9.1.1", "AbpStudio": "0.9.26", "TargetDotnetFramework": "net9.0"}, "modules": {"Imip.Ekb": {"path": "Imip.Ekb.abpmdl"}}, "runProfiles": {"Default": {"path": "etc/abp-studio/run-profiles/Default.abprun.json"}}, "options": {"httpRequests": {"ignoredUrls": []}}, "creatingStudioConfiguration": {"template": "app", "createdAbpStudioVersion": "0.9.26", "tiered": "false", "runInstallLibs": "true", "useLocalReferences": "false", "multiTenancy": "true", "includeTests": "true", "kubernetesConfiguration": "false", "uiFramework": "mvc", "mobileFramework": "none", "distributedEventBus": "none", "databaseProvider": "ef", "runDbMigrator": "true", "databaseManagementSystem": "sqlserver", "separateTenantSchema": "false", "createInitialMigration": "true", "theme": "leptonx-lite", "themeStyle": "", "themeMenuPlacement": "", "publicWebsite": "false", "socialLogin": "true", "createCommand": "abp new Imip.Ekb -t app --ui-framework mvc --database-provider ef --database-management-system sqlserver --theme leptonx-lite --without-cms-kit --dont-run-bundling -no-file-management"}}