import{v,j as e,Y as p}from"./App-BZgNu-zL.js";import{B as c,C as g,a as b,b as y,c as f}from"./card-YgUuHGlh.js";import{I as n,S as C,a as E,b as S,c as D,d}from"./select-CfwQ2FQL.js";import{A as F,a as P,L as i,S as w}from"./alert-BKei1lJX.js";import{A}from"./arrow-left-Ji5qialn.js";const B=({departments:j,employee:t,errors:m,error:o})=>{const{data:r,setData:l,post:N,processing:h}=v({firstName:t.firstName||"",lastName:t.lastName||"",email:t.email||"",phoneNumber:t.phoneNumber||"",department:t.department||"",position:t.position||"",hireDate:t.hireDate||new Date().toISOString().split("T")[0],salary:t.salary||0,photoPath:t.photoPath||""}),u=s=>{s.preventDefault(),N("/employees/create")},a=s=>{var x;return(x=m==null?void 0:m[s])==null?void 0:x[0]};return e.jsxs("div",{className:"container mx-auto p-6 max-w-4xl",children:[e.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[e.jsx(p,{href:"/employees",children:e.jsxs(c,{variant:"outline",size:"sm",children:[e.jsx(A,{className:"w-4 h-4 mr-2"}),"Back to Employees"]})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Add New Employee"}),e.jsx("p",{className:"text-gray-600 mt-1",children:"Create a new employee record"})]})]}),o&&e.jsx(F,{className:"mb-6",variant:"destructive",children:e.jsx(P,{children:o})}),e.jsxs(g,{children:[e.jsx(b,{children:e.jsx(y,{children:"Employee Information"})}),e.jsx(f,{children:e.jsxs("form",{onSubmit:u,className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(i,{htmlFor:"firstName",children:"First Name *"}),e.jsx(n,{id:"firstName",value:r.firstName,onChange:s=>l("firstName",s.target.value),className:a("firstName")?"border-red-500":"",placeholder:"Enter first name"}),a("firstName")&&e.jsx("p",{className:"text-sm text-red-600",children:a("firstName")})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(i,{htmlFor:"lastName",children:"Last Name *"}),e.jsx(n,{id:"lastName",value:r.lastName,onChange:s=>l("lastName",s.target.value),className:a("lastName")?"border-red-500":"",placeholder:"Enter last name"}),a("lastName")&&e.jsx("p",{className:"text-sm text-red-600",children:a("lastName")})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(i,{htmlFor:"email",children:"Email *"}),e.jsx(n,{id:"email",type:"email",value:r.email,onChange:s=>l("email",s.target.value),className:a("email")?"border-red-500":"",placeholder:"Enter email address"}),a("email")&&e.jsx("p",{className:"text-sm text-red-600",children:a("email")})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(i,{htmlFor:"phoneNumber",children:"Phone Number"}),e.jsx(n,{id:"phoneNumber",value:r.phoneNumber,onChange:s=>l("phoneNumber",s.target.value),className:a("phoneNumber")?"border-red-500":"",placeholder:"Enter phone number"}),a("phoneNumber")&&e.jsx("p",{className:"text-sm text-red-600",children:a("phoneNumber")})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(i,{htmlFor:"department",children:"Department *"}),e.jsxs(C,{value:r.department,onValueChange:s=>l("department",s),children:[e.jsx(E,{className:a("department")?"border-red-500":"",children:e.jsx(S,{placeholder:"Select department"})}),e.jsxs(D,{children:[j.map(s=>e.jsx(d,{value:s,children:s},s)),e.jsx(d,{value:"Human Resources",children:"Human Resources"}),e.jsx(d,{value:"Engineering",children:"Engineering"}),e.jsx(d,{value:"Marketing",children:"Marketing"}),e.jsx(d,{value:"Sales",children:"Sales"}),e.jsx(d,{value:"Finance",children:"Finance"}),e.jsx(d,{value:"Operations",children:"Operations"})]})]}),a("department")&&e.jsx("p",{className:"text-sm text-red-600",children:a("department")})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(i,{htmlFor:"position",children:"Position *"}),e.jsx(n,{id:"position",value:r.position,onChange:s=>l("position",s.target.value),className:a("position")?"border-red-500":"",placeholder:"Enter job position"}),a("position")&&e.jsx("p",{className:"text-sm text-red-600",children:a("position")})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(i,{htmlFor:"hireDate",children:"Hire Date *"}),e.jsx(n,{id:"hireDate",type:"date",value:r.hireDate,onChange:s=>l("hireDate",s.target.value),className:a("hireDate")?"border-red-500":""}),a("hireDate")&&e.jsx("p",{className:"text-sm text-red-600",children:a("hireDate")})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(i,{htmlFor:"salary",children:"Salary *"}),e.jsx(n,{id:"salary",type:"number",step:"0.01",min:"0",value:r.salary,onChange:s=>l("salary",parseFloat(s.target.value)||0),className:a("salary")?"border-red-500":"",placeholder:"Enter salary amount"}),a("salary")&&e.jsx("p",{className:"text-sm text-red-600",children:a("salary")})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(i,{htmlFor:"photoPath",children:"Photo URL"}),e.jsx(n,{id:"photoPath",value:r.photoPath,onChange:s=>l("photoPath",s.target.value),className:a("photoPath")?"border-red-500":"",placeholder:"Enter photo URL (optional)"}),a("photoPath")&&e.jsx("p",{className:"text-sm text-red-600",children:a("photoPath")})]}),e.jsxs("div",{className:"flex justify-end gap-4 pt-6 border-t",children:[e.jsx(p,{href:"/employees",children:e.jsx(c,{type:"button",variant:"outline",children:"Cancel"})}),e.jsxs(c,{type:"submit",disabled:h,children:[e.jsx(w,{className:"w-4 h-4 mr-2"}),h?"Creating...":"Create Employee"]})]})]})})]})]})};export{B as default};
