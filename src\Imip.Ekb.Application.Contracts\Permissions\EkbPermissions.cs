namespace Imip.Ekb.Permissions;

public static class EkbPermissions
{
    public const string GroupName = "Ekb";

    // Product permissions
    public static class Products
    {
        public const string Default = GroupName + ".Products";
        public const string Create = Default + ".Create";
        public const string Edit = Default + ".Edit";
        public const string Delete = Default + ".Delete";
    }

    // Employee permissions
    public static class Employees
    {
        public const string Default = GroupName + ".Employees";
        public const string Create = Default + ".Create";
        public const string Edit = Default + ".Edit";
        public const string Delete = Default + ".Delete";
        public const string ManageActivation = Default + ".ManageActivation";
        public const string Export = Default + ".Export";
    }
}
