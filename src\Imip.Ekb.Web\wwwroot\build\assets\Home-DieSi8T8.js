import{j as e}from"./App-BZgNu-zL.js";const n=({message:s,timestamp:t,user:r})=>e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:e.jsxs("div",{className:"max-w-4xl w-full",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:s}),e.jsx("p",{className:"text-lg text-gray-600 mb-8",children:"A modern React SPA powered by ABP Framework"}),e.jsxs("div",{className:"text-sm text-gray-500",children:["Welcome, ",r," | Last updated: ",t]})]}),e.jsxs("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center",children:e.jsx("svg",{className:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 ml-3",children:"Fast Development"})]}),e.jsx("p",{className:"text-gray-600",children:"Built with Vite, React, and TypeScript for lightning-fast development experience."})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center",children:e.jsx("svg",{className:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 ml-3",children:"ABP Framework"})]}),e.jsx("p",{className:"text-gray-600",children:"Powered by ABP Framework with built-in authentication, authorization, and more."})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center",children:e.jsx("svg",{className:"w-6 h-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"})})}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 ml-3",children:"Modern UI"})]}),e.jsx("p",{className:"text-gray-600",children:"Beautiful UI components with Tailwind CSS and shadcn/ui for a modern look and feel."})]})]}),e.jsx("div",{className:"mt-8 text-center",children:e.jsxs("div",{className:"space-x-4",children:[e.jsx("a",{href:"/employees",className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:"Manage Employees"}),e.jsx("a",{href:"/swagger",className:"inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors",children:"API Documentation"}),e.jsx("a",{href:"/Account/Logout",className:"inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors",children:"Logout"})]})})]})});export{n as default};
