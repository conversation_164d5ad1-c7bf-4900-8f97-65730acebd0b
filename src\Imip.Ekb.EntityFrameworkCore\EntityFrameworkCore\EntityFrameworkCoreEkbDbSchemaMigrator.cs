﻿using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Imip.Ekb.Data;
using Volo.Abp.DependencyInjection;

namespace Imip.Ekb.EntityFrameworkCore;

public class EntityFrameworkCoreEkbDbSchemaMigrator
    : IEkbDbSchemaMigrator, ITransientDependency
{
    private readonly IServiceProvider _serviceProvider;

    public EntityFrameworkCoreEkbDbSchemaMigrator(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public async Task MigrateAsync()
    {
        /* We intentionally resolving the EkbDbContext
         * from IServiceProvider (instead of directly injecting it)
         * to properly get the connection string of the current tenant in the
         * current scope.
         */

        await _serviceProvider
            .GetRequiredService<EkbDbContext>()
            .Database
            .MigrateAsync();
    }
}
