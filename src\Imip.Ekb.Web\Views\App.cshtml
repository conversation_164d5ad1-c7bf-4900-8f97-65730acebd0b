@using InertiaCore
@using InertiaCore.Utils

@{
    Layout = null;
}

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Imip.Ekb Dashboard</title>
    @await Inertia.Head(Model)
    @Vite.Input("src/App.tsx")
</head>

<body>
    @Vite.ReactRefresh()
    @await Inertia.Html(Model)
    @{
        var token = Html.AntiForgeryToken();
    }
    <script>
        window.__RequestVerificationToken = '@Html.Raw(token.ToString().Replace("\"", "\\\""))';
    </script>
</body>

</html>
