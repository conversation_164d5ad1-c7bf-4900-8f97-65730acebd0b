using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.Products
{
    /// <summary>
    /// DTO for filtering and pagination of products
    /// </summary>
    public class GetProductListDto : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// Filter by product name
        /// </summary>
        public string? Filter { get; set; }

        /// <summary>
        /// Filter by active status
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// Minimum price filter
        /// </summary>
        public decimal? MinPrice { get; set; }

        /// <summary>
        /// Maximum price filter
        /// </summary>
        public decimal? MaxPrice { get; set; }
    }
}
