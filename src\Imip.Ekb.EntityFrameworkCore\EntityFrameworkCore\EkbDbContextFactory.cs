﻿using System;
using System.IO;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;

namespace Imip.Ekb.EntityFrameworkCore;

/* This class is needed for EF Core console commands
 * (like Add-Migration and Update-Database commands) */
public class EkbDbContextFactory : IDesignTimeDbContextFactory<EkbDbContext>
{
    public EkbDbContext CreateDbContext(string[] args)
    {
        var configuration = BuildConfiguration();
        
        EkbEfCoreEntityExtensionMappings.Configure();

        var builder = new DbContextOptionsBuilder<EkbDbContext>()
            .UseSqlServer(configuration.GetConnectionString("Default"));
        
        return new EkbDbContext(builder.Options);
    }

    private static IConfigurationRoot BuildConfiguration()
    {
        var builder = new ConfigurationBuilder()
            .SetBasePath(Path.Combine(Directory.GetCurrentDirectory(), "../Imip.Ekb.DbMigrator/"))
            .AddJsonFile("appsettings.json", optional: false);

        return builder.Build();
    }
}
