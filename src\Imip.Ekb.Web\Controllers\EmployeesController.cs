using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Imip.Ekb.Employees;
using Imip.Ekb.Permissions;
using InertiaCore;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.Ekb.Web.Controllers;

[Authorize(EkbPermissions.Employees.Default)]
public class EmployeesController : AbpController
{
    private readonly IEmployeeAppService _employeeAppService;

    public EmployeesController(IEmployeeAppService employeeAppService)
    {
        _employeeAppService = employeeAppService;
    }

    public async Task<IActionResult> Index(GetEmployeeListDto input)
    {
        input.MaxResultCount = input.MaxResultCount <= 0 ? 10 : input.MaxResultCount;

        var employees = await _employeeAppService.GetListAsync(input);
        var departments = await _employeeAppService.GetDepartmentsAsync();

        return Inertia.Render("Employees/Index", new
        {
            employees = employees.Items,
            totalCount = employees.TotalCount,
            currentPage = (input.SkipCount / input.MaxResultCount) + 1,
            pageSize = input.MaxResultCount,
            filter = input.Filter ?? "",
            department = input.Department ?? "",
            isActive = input.IsActive,
            departments,
            permissions = new
            {
                create = await AuthorizationService.IsGrantedAsync(EkbPermissions.Employees.Create),
                edit = await AuthorizationService.IsGrantedAsync(EkbPermissions.Employees.Edit),
                delete = await AuthorizationService.IsGrantedAsync(EkbPermissions.Employees.Delete),
                manageActivation = await AuthorizationService.IsGrantedAsync(EkbPermissions.Employees.ManageActivation),
                export = await AuthorizationService.IsGrantedAsync(EkbPermissions.Employees.Export)
            }
        });
    }

    [Authorize(EkbPermissions.Employees.Create)]
    public async Task<IActionResult> Create()
    {
        var departments = await _employeeAppService.GetDepartmentsAsync();

        return Inertia.Render("Employees/Create", new
        {
            departments,
            employee = new CreateUpdateEmployeeDto()
        });
    }

    [HttpPost]
    [Authorize(EkbPermissions.Employees.Create)]
    public async Task<IActionResult> Create(CreateUpdateEmployeeDto input)
    {
        if (!ModelState.IsValid)
        {
            var departments = await _employeeAppService.GetDepartmentsAsync();
            return Inertia.Render("Employees/Create", new
            {
                departments,
                employee = input,
                errors = ModelState
            });
        }

        try
        {
            await _employeeAppService.CreateAsync(input);
            return RedirectToAction(nameof(Index));
        }
        catch (Exception ex)
        {
            var departments = await _employeeAppService.GetDepartmentsAsync();
            return Inertia.Render("Employees/Create", new
            {
                departments,
                employee = input,
                error = ex.Message
            });
        }
    }

    [Authorize(EkbPermissions.Employees.Edit)]
    public async Task<IActionResult> Edit(Guid id)
    {
        var employee = await _employeeAppService.GetAsync(id);
        var departments = await _employeeAppService.GetDepartmentsAsync();

        return Inertia.Render("Employees/Edit", new
        {
            employee,
            departments
        });
    }

    [HttpPost]
    [Authorize(EkbPermissions.Employees.Edit)]
    public async Task<IActionResult> Edit(Guid id, CreateUpdateEmployeeDto input)
    {
        if (!ModelState.IsValid)
        {
            var employee = await _employeeAppService.GetAsync(id);
            var departments = await _employeeAppService.GetDepartmentsAsync();
            return Inertia.Render("Employees/Edit", new
            {
                employee,
                departments,
                errors = ModelState
            });
        }

        try
        {
            await _employeeAppService.UpdateAsync(id, input);
            return RedirectToAction(nameof(Index));
        }
        catch (Exception ex)
        {
            var employee = await _employeeAppService.GetAsync(id);
            var departments = await _employeeAppService.GetDepartmentsAsync();
            return Inertia.Render("Employees/Edit", new
            {
                employee,
                departments,
                error = ex.Message
            });
        }
    }

    public async Task<IActionResult> Show(Guid id)
    {
        var employee = await _employeeAppService.GetAsync(id);

        return Inertia.Render("Employees/Show", new
        {
            employee,
            permissions = new
            {
                edit = await AuthorizationService.IsGrantedAsync(EkbPermissions.Employees.Edit),
                delete = await AuthorizationService.IsGrantedAsync(EkbPermissions.Employees.Delete),
                manageActivation = await AuthorizationService.IsGrantedAsync(EkbPermissions.Employees.ManageActivation)
            }
        });
    }

    [HttpPost]
    [Authorize(EkbPermissions.Employees.Delete)]
    public async Task<IActionResult> Delete(Guid id)
    {
        await _employeeAppService.DeleteAsync(id);
        return RedirectToAction(nameof(Index));
    }

    [HttpPost]
    [Authorize(EkbPermissions.Employees.Delete)]
    public async Task<IActionResult> DeleteMultiple(List<Guid> ids)
    {
        await _employeeAppService.DeleteMultipleAsync(ids);
        return RedirectToAction(nameof(Index));
    }

    [HttpPost]
    [Authorize(EkbPermissions.Employees.ManageActivation)]
    public async Task<IActionResult> Activate(Guid id)
    {
        await _employeeAppService.ActivateAsync(id);
        return RedirectToAction(nameof(Index));
    }

    [HttpPost]
    [Authorize(EkbPermissions.Employees.ManageActivation)]
    public async Task<IActionResult> Deactivate(Guid id)
    {
        await _employeeAppService.DeactivateAsync(id);
        return RedirectToAction(nameof(Index));
    }

    [HttpGet]
    [Authorize(EkbPermissions.Employees.Export)]
    public async Task<IActionResult> Export(GetEmployeeListDto input)
    {
        var data = await _employeeAppService.ExportToExcelAsync(input);
        return File(data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "employees.xlsx");
    }
}