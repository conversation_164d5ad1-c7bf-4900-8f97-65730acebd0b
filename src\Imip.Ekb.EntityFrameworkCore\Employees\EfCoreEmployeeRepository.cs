using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using Imip.Ekb.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.Ekb.Employees;

public class EfCoreEmployeeRepository : EfCoreRepository<EkbDbContext, Employee, Guid>, IEmployeeRepository
{
    public EfCoreEmployeeRepository(IDbContextProvider<EkbDbContext> dbContextProvider)
        : base(dbContextProvider)
    {
    }

    public async Task<List<Employee>> GetListAsync(
        int skipCount,
        int maxResultCount,
        string sorting,
        string? filter = null,
        string? department = null,
        bool? isActive = null,
        CancellationToken cancellationToken = default)
    {
        var dbSet = await GetDbSetAsync();
        var query = dbSet.AsQueryable();

        query = ApplyFilter(query, filter, department, isActive);

        return await query
            .OrderBy(sorting.IsNullOrWhiteSpace() ? nameof(Employee.FirstName) : sorting)
            .Skip(skipCount)
            .Take(maxResultCount)
            .ToListAsync(GetCancellationToken(cancellationToken));
    }

    public async Task<long> GetCountAsync(
        string? filter = null,
        string? department = null,
        bool? isActive = null,
        CancellationToken cancellationToken = default)
    {
        var dbSet = await GetDbSetAsync();
        var query = dbSet.AsQueryable();

        query = ApplyFilter(query, filter, department, isActive);

        return await query.CountAsync(GetCancellationToken(cancellationToken));
    }

    public async Task<Employee?> FindByEmailAsync(
        string email,
        CancellationToken cancellationToken = default)
    {
        var dbSet = await GetDbSetAsync();
        return await dbSet.FirstOrDefaultAsync(
            x => x.Email == email,
            GetCancellationToken(cancellationToken));
    }

    public async Task<List<string>> GetDepartmentsAsync(
        CancellationToken cancellationToken = default)
    {
        var dbSet = await GetDbSetAsync();
        return await dbSet
            .Where(x => !string.IsNullOrEmpty(x.Department))
            .Select(x => x.Department)
            .Distinct()
            .OrderBy(x => x)
            .ToListAsync(GetCancellationToken(cancellationToken));
    }

    public async Task<bool> IsEmailUniqueAsync(
        string email,
        Guid? excludeId = null,
        CancellationToken cancellationToken = default)
    {
        var dbSet = await GetDbSetAsync();
        var query = dbSet.Where(x => x.Email == email);

        if (excludeId.HasValue)
        {
            query = query.Where(x => x.Id != excludeId.Value);
        }

        return !await query.AnyAsync(GetCancellationToken(cancellationToken));
    }

    private static IQueryable<Employee> ApplyFilter(
        IQueryable<Employee> query,
        string? filter,
        string? department,
        bool? isActive)
    {
        if (!string.IsNullOrWhiteSpace(filter))
        {
            query = query.Where(x =>
                x.FirstName.Contains(filter) ||
                x.LastName.Contains(filter) ||
                x.Email.Contains(filter) ||
                x.Position.Contains(filter) ||
                (x.PhoneNumber != null && x.PhoneNumber.Contains(filter)));
        }

        if (!string.IsNullOrWhiteSpace(department))
        {
            query = query.Where(x => x.Department == department);
        }

        if (isActive.HasValue)
        {
            query = query.Where(x => x.IsActive == isActive.Value);
        }

        return query;
    }
}
