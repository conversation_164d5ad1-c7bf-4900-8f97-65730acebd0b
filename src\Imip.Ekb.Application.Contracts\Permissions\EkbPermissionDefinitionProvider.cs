using Imip.Ekb.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;
using Volo.Abp.MultiTenancy;

namespace Imip.Ekb.Permissions;

public class EkbPermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        var myGroup = context.AddGroup(EkbPermissions.GroupName);

        // Define product permissions
        var productsPermission = myGroup.AddPermission(EkbPermissions.Products.Default, L("Permission:Products"));
        productsPermission.AddChild(EkbPermissions.Products.Create, L("Permission:Products.Create"));
        productsPermission.AddChild(EkbPermissions.Products.Edit, L("Permission:Products.Edit"));
        productsPermission.AddChild(EkbPermissions.Products.Delete, L("Permission:Products.Delete"));
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<EkbResource>(name);
    }
}
