using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Account.Web;
using Volo.Abp.Account.Web.Pages.Account;
using Volo.Abp.Identity;
using Volo.Abp.Security.Claims;
using Volo.Abp.Validation;

namespace Imip.Ekb.Web.Pages.Account
{
    // This class inherits from the ABP's LoginModel to maintain all the functionality
    public class LoginModel : Volo.Abp.Account.Web.Pages.Account.LoginModel
    {
        private readonly ILogger<LoginModel> _logger;

        public LoginModel(
            IAuthenticationSchemeProvider schemeProvider,
            IOptions<AbpAccountOptions> accountOptions,
            IOptions<IdentityOptions> identityOptions,
            IdentityDynamicClaimsPrincipalContributorCache identityDynamicClaimsPrincipalContributorCache,
            ILogger<LoginModel> logger)
            : base(schemeProvider, accountOptions, identityOptions, identityDynamicClaimsPrincipalContributorCache)
        {
            _logger = logger;
        }

        // Override the OnGetAsync method to maintain the original functionality
        public override async Task<IActionResult> OnGetAsync()
        {
            return await base.OnGetAsync();
        }

        // Override the OnPostAsync method with custom error handling
        public override async Task<IActionResult> OnPostAsync(string action)
        {
            try
            {
                // Log the form data for debugging
                _logger.LogInformation("Login attempt with username: {Username}", LoginInput?.UserNameOrEmailAddress);

                // Ensure the model state is valid before proceeding
                if (!ModelState.IsValid)
                {
                    foreach (var state in ModelState)
                    {
                        foreach (var error in state.Value.Errors)
                        {
                            _logger.LogWarning("Validation error for {Field}: {Error}", state.Key, error.ErrorMessage);
                        }
                    }

                    // Return the page with validation errors
                    return Page();
                }

                // Call the base implementation if model state is valid
                return await base.OnPostAsync(action);
            }
            catch (AbpValidationException ex)
            {
                // Log the validation exception
                _logger.LogError(ex, "Validation error during login");

                // Add the validation errors to the model state
                foreach (var error in ex.ValidationErrors)
                {
                    var memberName = error.MemberNames.Any() ? error.MemberNames.First() : string.Empty;
                    ModelState.AddModelError(memberName, error.ErrorMessage);
                }

                // Return the page with validation errors
                return Page();
            }
            catch (Exception ex)
            {
                // Log any other exceptions
                _logger.LogError(ex, "Error during login");

                // Add a generic error message
                ModelState.AddModelError(string.Empty, "An error occurred during login. Please try again.");

                // Return the page with the error message
                return Page();
            }
        }
    }
}
