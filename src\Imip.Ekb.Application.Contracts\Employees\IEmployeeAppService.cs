using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Employees;

public interface IEmployeeAppService : ICrudAppService<
        EmployeeDto,
        Guid,
        GetEmployeeListDto,
        CreateUpdateEmployeeDto,
        CreateUpdateEmployeeDto>
{
    Task<List<string>> GetDepartmentsAsync();
    Task<bool> IsEmailUniqueAsync(string email, Guid? excludeId = null);
    Task DeleteMultipleAsync(List<Guid> ids);
    Task<byte[]> ExportToExcelAsync(GetEmployeeListDto input);
    Task ActivateAsync(Guid id);
    Task DeactivateAsync(Guid id);
}