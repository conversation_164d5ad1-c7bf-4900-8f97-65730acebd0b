var Gm=a=>{throw TypeError(a)};var lo=(a,i,r)=>i.has(a)||Gm("Cannot "+r);var k=(a,i,r)=>(lo(a,i,"read from private field"),r?r.call(a):i.get(a)),Ee=(a,i,r)=>i.has(a)?Gm("Cannot add the same private member more than once"):i instanceof WeakSet?i.add(a):i.set(a,r),ce=(a,i,r,s)=>(lo(a,i,"write to private field"),s?s.call(a,r):i.set(a,r),r),it=(a,i,r)=>(lo(a,i,"access private method"),r);var pr=(a,i,r,s)=>({set _(o){ce(a,i,o,r)},get _(){return k(a,i,s)}});(function(){const i=document.createElement("link").relList;if(i&&i.supports&&i.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))s(o);new MutationObserver(o=>{for(const f of o)if(f.type==="childList")for(const h of f.addedNodes)h.tagName==="LINK"&&h.rel==="modulepreload"&&s(h)}).observe(document,{childList:!0,subtree:!0});function r(o){const f={};return o.integrity&&(f.integrity=o.integrity),o.referrerPolicy&&(f.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?f.credentials="include":o.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function s(o){if(o.ep)return;o.ep=!0;const f=r(o);fetch(o.href,f)}})();var ao={exports:{}},Li={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ym;function y0(){if(Ym)return Li;Ym=1;var a=Symbol.for("react.transitional.element"),i=Symbol.for("react.fragment");function r(s,o,f){var h=null;if(f!==void 0&&(h=""+f),o.key!==void 0&&(h=""+o.key),"key"in o){f={};for(var g in o)g!=="key"&&(f[g]=o[g])}else f=o;return o=f.ref,{$$typeof:a,type:s,key:h,ref:o!==void 0?o:null,props:f}}return Li.Fragment=i,Li.jsx=r,Li.jsxs=r,Li}var Qm;function p0(){return Qm||(Qm=1,ao.exports=y0()),ao.exports}var O=p0(),io={exports:{}},de={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vm;function g0(){if(Vm)return de;Vm=1;var a=Symbol.for("react.transitional.element"),i=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),h=Symbol.for("react.context"),g=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),E=Symbol.iterator;function T(S){return S===null||typeof S!="object"?null:(S=E&&S[E]||S["@@iterator"],typeof S=="function"?S:null)}var B={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},A=Object.assign,M={};function C(S,Q,$){this.props=S,this.context=Q,this.refs=M,this.updater=$||B}C.prototype.isReactComponent={},C.prototype.setState=function(S,Q){if(typeof S!="object"&&typeof S!="function"&&S!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,S,Q,"setState")},C.prototype.forceUpdate=function(S){this.updater.enqueueForceUpdate(this,S,"forceUpdate")};function H(){}H.prototype=C.prototype;function Z(S,Q,$){this.props=S,this.context=Q,this.refs=M,this.updater=$||B}var Y=Z.prototype=new H;Y.constructor=Z,A(Y,C.prototype),Y.isPureReactComponent=!0;var P=Array.isArray,K={H:null,A:null,T:null,S:null,V:null},ue=Object.prototype.hasOwnProperty;function fe(S,Q,$,X,W,pe){return $=pe.ref,{$$typeof:a,type:S,key:Q,ref:$!==void 0?$:null,props:pe}}function F(S,Q){return fe(S.type,Q,void 0,void 0,void 0,S.props)}function le(S){return typeof S=="object"&&S!==null&&S.$$typeof===a}function je(S){var Q={"=":"=0",":":"=2"};return"$"+S.replace(/[=:]/g,function($){return Q[$]})}var ct=/\/+/g;function Ge(S,Q){return typeof S=="object"&&S!==null&&S.key!=null?je(""+S.key):Q.toString(36)}function Wt(){}function kt(S){switch(S.status){case"fulfilled":return S.value;case"rejected":throw S.reason;default:switch(typeof S.status=="string"?S.then(Wt,Wt):(S.status="pending",S.then(function(Q){S.status==="pending"&&(S.status="fulfilled",S.value=Q)},function(Q){S.status==="pending"&&(S.status="rejected",S.reason=Q)})),S.status){case"fulfilled":return S.value;case"rejected":throw S.reason}}throw S}function He(S,Q,$,X,W){var pe=typeof S;(pe==="undefined"||pe==="boolean")&&(S=null);var se=!1;if(S===null)se=!0;else switch(pe){case"bigint":case"string":case"number":se=!0;break;case"object":switch(S.$$typeof){case a:case i:se=!0;break;case v:return se=S._init,He(se(S._payload),Q,$,X,W)}}if(se)return W=W(S),se=X===""?"."+Ge(S,0):X,P(W)?($="",se!=null&&($=se.replace(ct,"$&/")+"/"),He(W,Q,$,"",function(St){return St})):W!=null&&(le(W)&&(W=F(W,$+(W.key==null||S&&S.key===W.key?"":(""+W.key).replace(ct,"$&/")+"/")+se)),Q.push(W)),1;se=0;var Se=X===""?".":X+":";if(P(S))for(var Me=0;Me<S.length;Me++)X=S[Me],pe=Se+Ge(X,Me),se+=He(X,Q,$,pe,W);else if(Me=T(S),typeof Me=="function")for(S=Me.call(S),Me=0;!(X=S.next()).done;)X=X.value,pe=Se+Ge(X,Me++),se+=He(X,Q,$,pe,W);else if(pe==="object"){if(typeof S.then=="function")return He(kt(S),Q,$,X,W);throw Q=String(S),Error("Objects are not valid as a React child (found: "+(Q==="[object Object]"?"object with keys {"+Object.keys(S).join(", ")+"}":Q)+"). If you meant to render a collection of children, use an array instead.")}return se}function j(S,Q,$){if(S==null)return S;var X=[],W=0;return He(S,X,"","",function(pe){return Q.call($,pe,W++)}),X}function J(S){if(S._status===-1){var Q=S._result;Q=Q(),Q.then(function($){(S._status===0||S._status===-1)&&(S._status=1,S._result=$)},function($){(S._status===0||S._status===-1)&&(S._status=2,S._result=$)}),S._status===-1&&(S._status=0,S._result=Q)}if(S._status===1)return S._result.default;throw S._result}var V=typeof reportError=="function"?reportError:function(S){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var Q=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof S=="object"&&S!==null&&typeof S.message=="string"?String(S.message):String(S),error:S});if(!window.dispatchEvent(Q))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",S);return}console.error(S)};function Re(){}return de.Children={map:j,forEach:function(S,Q,$){j(S,function(){Q.apply(this,arguments)},$)},count:function(S){var Q=0;return j(S,function(){Q++}),Q},toArray:function(S){return j(S,function(Q){return Q})||[]},only:function(S){if(!le(S))throw Error("React.Children.only expected to receive a single React element child.");return S}},de.Component=C,de.Fragment=r,de.Profiler=o,de.PureComponent=Z,de.StrictMode=s,de.Suspense=p,de.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=K,de.__COMPILER_RUNTIME={__proto__:null,c:function(S){return K.H.useMemoCache(S)}},de.cache=function(S){return function(){return S.apply(null,arguments)}},de.cloneElement=function(S,Q,$){if(S==null)throw Error("The argument must be a React element, but you passed "+S+".");var X=A({},S.props),W=S.key,pe=void 0;if(Q!=null)for(se in Q.ref!==void 0&&(pe=void 0),Q.key!==void 0&&(W=""+Q.key),Q)!ue.call(Q,se)||se==="key"||se==="__self"||se==="__source"||se==="ref"&&Q.ref===void 0||(X[se]=Q[se]);var se=arguments.length-2;if(se===1)X.children=$;else if(1<se){for(var Se=Array(se),Me=0;Me<se;Me++)Se[Me]=arguments[Me+2];X.children=Se}return fe(S.type,W,void 0,void 0,pe,X)},de.createContext=function(S){return S={$$typeof:h,_currentValue:S,_currentValue2:S,_threadCount:0,Provider:null,Consumer:null},S.Provider=S,S.Consumer={$$typeof:f,_context:S},S},de.createElement=function(S,Q,$){var X,W={},pe=null;if(Q!=null)for(X in Q.key!==void 0&&(pe=""+Q.key),Q)ue.call(Q,X)&&X!=="key"&&X!=="__self"&&X!=="__source"&&(W[X]=Q[X]);var se=arguments.length-2;if(se===1)W.children=$;else if(1<se){for(var Se=Array(se),Me=0;Me<se;Me++)Se[Me]=arguments[Me+2];W.children=Se}if(S&&S.defaultProps)for(X in se=S.defaultProps,se)W[X]===void 0&&(W[X]=se[X]);return fe(S,pe,void 0,void 0,null,W)},de.createRef=function(){return{current:null}},de.forwardRef=function(S){return{$$typeof:g,render:S}},de.isValidElement=le,de.lazy=function(S){return{$$typeof:v,_payload:{_status:-1,_result:S},_init:J}},de.memo=function(S,Q){return{$$typeof:m,type:S,compare:Q===void 0?null:Q}},de.startTransition=function(S){var Q=K.T,$={};K.T=$;try{var X=S(),W=K.S;W!==null&&W($,X),typeof X=="object"&&X!==null&&typeof X.then=="function"&&X.then(Re,V)}catch(pe){V(pe)}finally{K.T=Q}},de.unstable_useCacheRefresh=function(){return K.H.useCacheRefresh()},de.use=function(S){return K.H.use(S)},de.useActionState=function(S,Q,$){return K.H.useActionState(S,Q,$)},de.useCallback=function(S,Q){return K.H.useCallback(S,Q)},de.useContext=function(S){return K.H.useContext(S)},de.useDebugValue=function(){},de.useDeferredValue=function(S,Q){return K.H.useDeferredValue(S,Q)},de.useEffect=function(S,Q,$){var X=K.H;if(typeof $=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return X.useEffect(S,Q)},de.useId=function(){return K.H.useId()},de.useImperativeHandle=function(S,Q,$){return K.H.useImperativeHandle(S,Q,$)},de.useInsertionEffect=function(S,Q){return K.H.useInsertionEffect(S,Q)},de.useLayoutEffect=function(S,Q){return K.H.useLayoutEffect(S,Q)},de.useMemo=function(S,Q){return K.H.useMemo(S,Q)},de.useOptimistic=function(S,Q){return K.H.useOptimistic(S,Q)},de.useReducer=function(S,Q,$){return K.H.useReducer(S,Q,$)},de.useRef=function(S){return K.H.useRef(S)},de.useState=function(S){return K.H.useState(S)},de.useSyncExternalStore=function(S,Q,$){return K.H.useSyncExternalStore(S,Q,$)},de.useTransition=function(){return K.H.useTransition()},de.version="19.1.0",de}var Xm;function zo(){return Xm||(Xm=1,io.exports=g0()),io.exports}var _=zo(),uo={exports:{}},ki={},ro={exports:{}},so={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zm;function v0(){return Zm||(Zm=1,function(a){function i(j,J){var V=j.length;j.push(J);e:for(;0<V;){var Re=V-1>>>1,S=j[Re];if(0<o(S,J))j[Re]=J,j[V]=S,V=Re;else break e}}function r(j){return j.length===0?null:j[0]}function s(j){if(j.length===0)return null;var J=j[0],V=j.pop();if(V!==J){j[0]=V;e:for(var Re=0,S=j.length,Q=S>>>1;Re<Q;){var $=2*(Re+1)-1,X=j[$],W=$+1,pe=j[W];if(0>o(X,V))W<S&&0>o(pe,X)?(j[Re]=pe,j[W]=V,Re=W):(j[Re]=X,j[$]=V,Re=$);else if(W<S&&0>o(pe,V))j[Re]=pe,j[W]=V,Re=W;else break e}}return J}function o(j,J){var V=j.sortIndex-J.sortIndex;return V!==0?V:j.id-J.id}if(a.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;a.unstable_now=function(){return f.now()}}else{var h=Date,g=h.now();a.unstable_now=function(){return h.now()-g}}var p=[],m=[],v=1,E=null,T=3,B=!1,A=!1,M=!1,C=!1,H=typeof setTimeout=="function"?setTimeout:null,Z=typeof clearTimeout=="function"?clearTimeout:null,Y=typeof setImmediate<"u"?setImmediate:null;function P(j){for(var J=r(m);J!==null;){if(J.callback===null)s(m);else if(J.startTime<=j)s(m),J.sortIndex=J.expirationTime,i(p,J);else break;J=r(m)}}function K(j){if(M=!1,P(j),!A)if(r(p)!==null)A=!0,ue||(ue=!0,Ge());else{var J=r(m);J!==null&&He(K,J.startTime-j)}}var ue=!1,fe=-1,F=5,le=-1;function je(){return C?!0:!(a.unstable_now()-le<F)}function ct(){if(C=!1,ue){var j=a.unstable_now();le=j;var J=!0;try{e:{A=!1,M&&(M=!1,Z(fe),fe=-1),B=!0;var V=T;try{t:{for(P(j),E=r(p);E!==null&&!(E.expirationTime>j&&je());){var Re=E.callback;if(typeof Re=="function"){E.callback=null,T=E.priorityLevel;var S=Re(E.expirationTime<=j);if(j=a.unstable_now(),typeof S=="function"){E.callback=S,P(j),J=!0;break t}E===r(p)&&s(p),P(j)}else s(p);E=r(p)}if(E!==null)J=!0;else{var Q=r(m);Q!==null&&He(K,Q.startTime-j),J=!1}}break e}finally{E=null,T=V,B=!1}J=void 0}}finally{J?Ge():ue=!1}}}var Ge;if(typeof Y=="function")Ge=function(){Y(ct)};else if(typeof MessageChannel<"u"){var Wt=new MessageChannel,kt=Wt.port2;Wt.port1.onmessage=ct,Ge=function(){kt.postMessage(null)}}else Ge=function(){H(ct,0)};function He(j,J){fe=H(function(){j(a.unstable_now())},J)}a.unstable_IdlePriority=5,a.unstable_ImmediatePriority=1,a.unstable_LowPriority=4,a.unstable_NormalPriority=3,a.unstable_Profiling=null,a.unstable_UserBlockingPriority=2,a.unstable_cancelCallback=function(j){j.callback=null},a.unstable_forceFrameRate=function(j){0>j||125<j?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):F=0<j?Math.floor(1e3/j):5},a.unstable_getCurrentPriorityLevel=function(){return T},a.unstable_next=function(j){switch(T){case 1:case 2:case 3:var J=3;break;default:J=T}var V=T;T=J;try{return j()}finally{T=V}},a.unstable_requestPaint=function(){C=!0},a.unstable_runWithPriority=function(j,J){switch(j){case 1:case 2:case 3:case 4:case 5:break;default:j=3}var V=T;T=j;try{return J()}finally{T=V}},a.unstable_scheduleCallback=function(j,J,V){var Re=a.unstable_now();switch(typeof V=="object"&&V!==null?(V=V.delay,V=typeof V=="number"&&0<V?Re+V:Re):V=Re,j){case 1:var S=-1;break;case 2:S=250;break;case 5:S=1073741823;break;case 4:S=1e4;break;default:S=5e3}return S=V+S,j={id:v++,callback:J,priorityLevel:j,startTime:V,expirationTime:S,sortIndex:-1},V>Re?(j.sortIndex=V,i(m,j),r(p)===null&&j===r(m)&&(M?(Z(fe),fe=-1):M=!0,He(K,V-Re))):(j.sortIndex=S,i(p,j),A||B||(A=!0,ue||(ue=!0,Ge()))),j},a.unstable_shouldYield=je,a.unstable_wrapCallback=function(j){var J=T;return function(){var V=T;T=J;try{return j.apply(this,arguments)}finally{T=V}}}}(so)),so}var Km;function b0(){return Km||(Km=1,ro.exports=v0()),ro.exports}var co={exports:{}},ut={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Jm;function S0(){if(Jm)return ut;Jm=1;var a=zo();function i(p){var m="https://react.dev/errors/"+p;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var v=2;v<arguments.length;v++)m+="&args[]="+encodeURIComponent(arguments[v])}return"Minified React error #"+p+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function r(){}var s={d:{f:r,r:function(){throw Error(i(522))},D:r,C:r,L:r,m:r,X:r,S:r,M:r},p:0,findDOMNode:null},o=Symbol.for("react.portal");function f(p,m,v){var E=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:o,key:E==null?null:""+E,children:p,containerInfo:m,implementation:v}}var h=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function g(p,m){if(p==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return ut.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,ut.createPortal=function(p,m){var v=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(i(299));return f(p,m,null,v)},ut.flushSync=function(p){var m=h.T,v=s.p;try{if(h.T=null,s.p=2,p)return p()}finally{h.T=m,s.p=v,s.d.f()}},ut.preconnect=function(p,m){typeof p=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,s.d.C(p,m))},ut.prefetchDNS=function(p){typeof p=="string"&&s.d.D(p)},ut.preinit=function(p,m){if(typeof p=="string"&&m&&typeof m.as=="string"){var v=m.as,E=g(v,m.crossOrigin),T=typeof m.integrity=="string"?m.integrity:void 0,B=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;v==="style"?s.d.S(p,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:E,integrity:T,fetchPriority:B}):v==="script"&&s.d.X(p,{crossOrigin:E,integrity:T,fetchPriority:B,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},ut.preinitModule=function(p,m){if(typeof p=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var v=g(m.as,m.crossOrigin);s.d.M(p,{crossOrigin:v,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&s.d.M(p)},ut.preload=function(p,m){if(typeof p=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var v=m.as,E=g(v,m.crossOrigin);s.d.L(p,v,{crossOrigin:E,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},ut.preloadModule=function(p,m){if(typeof p=="string")if(m){var v=g(m.as,m.crossOrigin);s.d.m(p,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:v,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else s.d.m(p)},ut.requestFormReset=function(p){s.d.r(p)},ut.unstable_batchedUpdates=function(p,m){return p(m)},ut.useFormState=function(p,m,v){return h.H.useFormState(p,m,v)},ut.useFormStatus=function(){return h.H.useHostTransitionStatus()},ut.version="19.1.0",ut}var Fm;function x0(){if(Fm)return co.exports;Fm=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(i){console.error(i)}}return a(),co.exports=S0(),co.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var $m;function E0(){if($m)return ki;$m=1;var a=b0(),i=zo(),r=x0();function s(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function h(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function g(e){if(f(e)!==e)throw Error(s(188))}function p(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(s(188));return t!==e?null:e}for(var n=e,l=t;;){var u=n.return;if(u===null)break;var c=u.alternate;if(c===null){if(l=u.return,l!==null){n=l;continue}break}if(u.child===c.child){for(c=u.child;c;){if(c===n)return g(u),e;if(c===l)return g(u),t;c=c.sibling}throw Error(s(188))}if(n.return!==l.return)n=u,l=c;else{for(var d=!1,y=u.child;y;){if(y===n){d=!0,n=u,l=c;break}if(y===l){d=!0,l=u,n=c;break}y=y.sibling}if(!d){for(y=c.child;y;){if(y===n){d=!0,n=c,l=u;break}if(y===l){d=!0,l=c,n=u;break}y=y.sibling}if(!d)throw Error(s(189))}}if(n.alternate!==l)throw Error(s(190))}if(n.tag!==3)throw Error(s(188));return n.stateNode.current===n?e:t}function m(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=m(e),t!==null)return t;e=e.sibling}return null}var v=Object.assign,E=Symbol.for("react.element"),T=Symbol.for("react.transitional.element"),B=Symbol.for("react.portal"),A=Symbol.for("react.fragment"),M=Symbol.for("react.strict_mode"),C=Symbol.for("react.profiler"),H=Symbol.for("react.provider"),Z=Symbol.for("react.consumer"),Y=Symbol.for("react.context"),P=Symbol.for("react.forward_ref"),K=Symbol.for("react.suspense"),ue=Symbol.for("react.suspense_list"),fe=Symbol.for("react.memo"),F=Symbol.for("react.lazy"),le=Symbol.for("react.activity"),je=Symbol.for("react.memo_cache_sentinel"),ct=Symbol.iterator;function Ge(e){return e===null||typeof e!="object"?null:(e=ct&&e[ct]||e["@@iterator"],typeof e=="function"?e:null)}var Wt=Symbol.for("react.client.reference");function kt(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===Wt?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case A:return"Fragment";case C:return"Profiler";case M:return"StrictMode";case K:return"Suspense";case ue:return"SuspenseList";case le:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case B:return"Portal";case Y:return(e.displayName||"Context")+".Provider";case Z:return(e._context.displayName||"Context")+".Consumer";case P:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case fe:return t=e.displayName||null,t!==null?t:kt(e.type)||"Memo";case F:t=e._payload,e=e._init;try{return kt(e(t))}catch{}}return null}var He=Array.isArray,j=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,J=r.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,V={pending:!1,data:null,method:null,action:null},Re=[],S=-1;function Q(e){return{current:e}}function $(e){0>S||(e.current=Re[S],Re[S]=null,S--)}function X(e,t){S++,Re[S]=e.current,e.current=t}var W=Q(null),pe=Q(null),se=Q(null),Se=Q(null);function Me(e,t){switch(X(se,t),X(pe,e),X(W,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?ym(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=ym(t),e=pm(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}$(W),X(W,e)}function St(){$(W),$(pe),$(se)}function Dn(e){e.memoizedState!==null&&X(Se,e);var t=W.current,n=pm(t,e.type);t!==n&&(X(pe,e),X(W,n))}function zn(e){pe.current===e&&($(W),$(pe)),Se.current===e&&($(Se),Ui._currentValue=V)}var _n=Object.prototype.hasOwnProperty,Vr=a.unstable_scheduleCallback,Xr=a.unstable_cancelCallback,Kp=a.unstable_shouldYield,Jp=a.unstable_requestPaint,Pt=a.unstable_now,Fp=a.unstable_getCurrentPriorityLevel,Jo=a.unstable_ImmediatePriority,Fo=a.unstable_UserBlockingPriority,nu=a.unstable_NormalPriority,$p=a.unstable_LowPriority,$o=a.unstable_IdlePriority,Wp=a.log,Pp=a.unstable_setDisableYieldValue,Ya=null,xt=null;function Un(e){if(typeof Wp=="function"&&Pp(e),xt&&typeof xt.setStrictMode=="function")try{xt.setStrictMode(Ya,e)}catch{}}var Et=Math.clz32?Math.clz32:tg,Ip=Math.log,eg=Math.LN2;function tg(e){return e>>>=0,e===0?32:31-(Ip(e)/eg|0)|0}var lu=256,au=4194304;function fl(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function iu(e,t,n){var l=e.pendingLanes;if(l===0)return 0;var u=0,c=e.suspendedLanes,d=e.pingedLanes;e=e.warmLanes;var y=l&134217727;return y!==0?(l=y&~c,l!==0?u=fl(l):(d&=y,d!==0?u=fl(d):n||(n=y&~e,n!==0&&(u=fl(n))))):(y=l&~c,y!==0?u=fl(y):d!==0?u=fl(d):n||(n=l&~e,n!==0&&(u=fl(n)))),u===0?0:t!==0&&t!==u&&(t&c)===0&&(c=u&-u,n=t&-t,c>=n||c===32&&(n&4194048)!==0)?t:u}function Qa(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function ng(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Wo(){var e=lu;return lu<<=1,(lu&4194048)===0&&(lu=256),e}function Po(){var e=au;return au<<=1,(au&62914560)===0&&(au=4194304),e}function Zr(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Va(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function lg(e,t,n,l,u,c){var d=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var y=e.entanglements,b=e.expirationTimes,N=e.hiddenUpdates;for(n=d&~n;0<n;){var q=31-Et(n),G=1<<q;y[q]=0,b[q]=-1;var D=N[q];if(D!==null)for(N[q]=null,q=0;q<D.length;q++){var z=D[q];z!==null&&(z.lane&=-536870913)}n&=~G}l!==0&&Io(e,l,0),c!==0&&u===0&&e.tag!==0&&(e.suspendedLanes|=c&~(d&~t))}function Io(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-Et(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|n&4194090}function ef(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var l=31-Et(n),u=1<<l;u&t|e[l]&t&&(e[l]|=t),n&=~u}}function Kr(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Jr(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function tf(){var e=J.p;return e!==0?e:(e=window.event,e===void 0?32:jm(e.type))}function ag(e,t){var n=J.p;try{return J.p=e,t()}finally{J.p=n}}var jn=Math.random().toString(36).slice(2),lt="__reactFiber$"+jn,ht="__reactProps$"+jn,Bl="__reactContainer$"+jn,Fr="__reactEvents$"+jn,ig="__reactListeners$"+jn,ug="__reactHandles$"+jn,nf="__reactResources$"+jn,Xa="__reactMarker$"+jn;function $r(e){delete e[lt],delete e[ht],delete e[Fr],delete e[ig],delete e[ug]}function Ll(e){var t=e[lt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Bl]||n[lt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Sm(e);e!==null;){if(n=e[lt])return n;e=Sm(e)}return t}e=n,n=e.parentNode}return null}function kl(e){if(e=e[lt]||e[Bl]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Za(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(s(33))}function Gl(e){var t=e[nf];return t||(t=e[nf]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function $e(e){e[Xa]=!0}var lf=new Set,af={};function dl(e,t){Yl(e,t),Yl(e+"Capture",t)}function Yl(e,t){for(af[e]=t,e=0;e<t.length;e++)lf.add(t[e])}var rg=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),uf={},rf={};function sg(e){return _n.call(rf,e)?!0:_n.call(uf,e)?!1:rg.test(e)?rf[e]=!0:(uf[e]=!0,!1)}function uu(e,t,n){if(sg(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function ru(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function on(e,t,n,l){if(l===null)e.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+l)}}var Wr,sf;function Ql(e){if(Wr===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Wr=t&&t[1]||"",sf=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Wr+e+sf}var Pr=!1;function Ir(e,t){if(!e||Pr)return"";Pr=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var G=function(){throw Error()};if(Object.defineProperty(G.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(G,[])}catch(z){var D=z}Reflect.construct(e,[],G)}else{try{G.call()}catch(z){D=z}e.call(G.prototype)}}else{try{throw Error()}catch(z){D=z}(G=e())&&typeof G.catch=="function"&&G.catch(function(){})}}catch(z){if(z&&D&&typeof z.stack=="string")return[z.stack,D.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var u=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");u&&u.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=l.DetermineComponentFrameRoot(),d=c[0],y=c[1];if(d&&y){var b=d.split(`
`),N=y.split(`
`);for(u=l=0;l<b.length&&!b[l].includes("DetermineComponentFrameRoot");)l++;for(;u<N.length&&!N[u].includes("DetermineComponentFrameRoot");)u++;if(l===b.length||u===N.length)for(l=b.length-1,u=N.length-1;1<=l&&0<=u&&b[l]!==N[u];)u--;for(;1<=l&&0<=u;l--,u--)if(b[l]!==N[u]){if(l!==1||u!==1)do if(l--,u--,0>u||b[l]!==N[u]){var q=`
`+b[l].replace(" at new "," at ");return e.displayName&&q.includes("<anonymous>")&&(q=q.replace("<anonymous>",e.displayName)),q}while(1<=l&&0<=u);break}}}finally{Pr=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?Ql(n):""}function cg(e){switch(e.tag){case 26:case 27:case 5:return Ql(e.type);case 16:return Ql("Lazy");case 13:return Ql("Suspense");case 19:return Ql("SuspenseList");case 0:case 15:return Ir(e.type,!1);case 11:return Ir(e.type.render,!1);case 1:return Ir(e.type,!0);case 31:return Ql("Activity");default:return""}}function cf(e){try{var t="";do t+=cg(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function Mt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function of(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function og(e){var t=of(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var u=n.get,c=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return u.call(this)},set:function(d){l=""+d,c.call(this,d)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(d){l=""+d},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function su(e){e._valueTracker||(e._valueTracker=og(e))}function ff(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),l="";return e&&(l=of(e)?e.checked?"true":"false":e.value),e=l,e!==n?(t.setValue(e),!0):!1}function cu(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var fg=/[\n"\\]/g;function Dt(e){return e.replace(fg,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function es(e,t,n,l,u,c,d,y){e.name="",d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?e.type=d:e.removeAttribute("type"),t!=null?d==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Mt(t)):e.value!==""+Mt(t)&&(e.value=""+Mt(t)):d!=="submit"&&d!=="reset"||e.removeAttribute("value"),t!=null?ts(e,d,Mt(t)):n!=null?ts(e,d,Mt(n)):l!=null&&e.removeAttribute("value"),u==null&&c!=null&&(e.defaultChecked=!!c),u!=null&&(e.checked=u&&typeof u!="function"&&typeof u!="symbol"),y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?e.name=""+Mt(y):e.removeAttribute("name")}function df(e,t,n,l,u,c,d,y){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(e.type=c),t!=null||n!=null){if(!(c!=="submit"&&c!=="reset"||t!=null))return;n=n!=null?""+Mt(n):"",t=t!=null?""+Mt(t):n,y||t===e.value||(e.value=t),e.defaultValue=t}l=l??u,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=y?e.checked:!!l,e.defaultChecked=!!l,d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"&&(e.name=d)}function ts(e,t,n){t==="number"&&cu(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function Vl(e,t,n,l){if(e=e.options,t){t={};for(var u=0;u<n.length;u++)t["$"+n[u]]=!0;for(n=0;n<e.length;n++)u=t.hasOwnProperty("$"+e[n].value),e[n].selected!==u&&(e[n].selected=u),u&&l&&(e[n].defaultSelected=!0)}else{for(n=""+Mt(n),t=null,u=0;u<e.length;u++){if(e[u].value===n){e[u].selected=!0,l&&(e[u].defaultSelected=!0);return}t!==null||e[u].disabled||(t=e[u])}t!==null&&(t.selected=!0)}}function hf(e,t,n){if(t!=null&&(t=""+Mt(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+Mt(n):""}function mf(e,t,n,l){if(t==null){if(l!=null){if(n!=null)throw Error(s(92));if(He(l)){if(1<l.length)throw Error(s(93));l=l[0]}n=l}n==null&&(n=""),t=n}n=Mt(t),e.defaultValue=n,l=e.textContent,l===n&&l!==""&&l!==null&&(e.value=l)}function Xl(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var dg=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function yf(e,t,n){var l=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,n):typeof n!="number"||n===0||dg.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function pf(e,t,n){if(t!=null&&typeof t!="object")throw Error(s(62));if(e=e.style,n!=null){for(var l in n)!n.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var u in t)l=t[u],t.hasOwnProperty(u)&&n[u]!==l&&yf(e,u,l)}else for(var c in t)t.hasOwnProperty(c)&&yf(e,c,t[c])}function ns(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var hg=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),mg=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function ou(e){return mg.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var ls=null;function as(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Zl=null,Kl=null;function gf(e){var t=kl(e);if(t&&(e=t.stateNode)){var n=e[ht]||null;e:switch(e=t.stateNode,t.type){case"input":if(es(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Dt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var l=n[t];if(l!==e&&l.form===e.form){var u=l[ht]||null;if(!u)throw Error(s(90));es(l,u.value,u.defaultValue,u.defaultValue,u.checked,u.defaultChecked,u.type,u.name)}}for(t=0;t<n.length;t++)l=n[t],l.form===e.form&&ff(l)}break e;case"textarea":hf(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&Vl(e,!!n.multiple,t,!1)}}}var is=!1;function vf(e,t,n){if(is)return e(t,n);is=!0;try{var l=e(t);return l}finally{if(is=!1,(Zl!==null||Kl!==null)&&(Fu(),Zl&&(t=Zl,e=Kl,Kl=Zl=null,gf(t),e)))for(t=0;t<e.length;t++)gf(e[t])}}function Ka(e,t){var n=e.stateNode;if(n===null)return null;var l=n[ht]||null;if(l===null)return null;n=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(s(231,t,typeof n));return n}var fn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),us=!1;if(fn)try{var Ja={};Object.defineProperty(Ja,"passive",{get:function(){us=!0}}),window.addEventListener("test",Ja,Ja),window.removeEventListener("test",Ja,Ja)}catch{us=!1}var Hn=null,rs=null,fu=null;function bf(){if(fu)return fu;var e,t=rs,n=t.length,l,u="value"in Hn?Hn.value:Hn.textContent,c=u.length;for(e=0;e<n&&t[e]===u[e];e++);var d=n-e;for(l=1;l<=d&&t[n-l]===u[c-l];l++);return fu=u.slice(e,1<l?1-l:void 0)}function du(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function hu(){return!0}function Sf(){return!1}function mt(e){function t(n,l,u,c,d){this._reactName=n,this._targetInst=u,this.type=l,this.nativeEvent=c,this.target=d,this.currentTarget=null;for(var y in e)e.hasOwnProperty(y)&&(n=e[y],this[y]=n?n(c):c[y]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?hu:Sf,this.isPropagationStopped=Sf,this}return v(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=hu)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=hu)},persist:function(){},isPersistent:hu}),t}var hl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},mu=mt(hl),Fa=v({},hl,{view:0,detail:0}),yg=mt(Fa),ss,cs,$a,yu=v({},Fa,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:fs,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==$a&&($a&&e.type==="mousemove"?(ss=e.screenX-$a.screenX,cs=e.screenY-$a.screenY):cs=ss=0,$a=e),ss)},movementY:function(e){return"movementY"in e?e.movementY:cs}}),xf=mt(yu),pg=v({},yu,{dataTransfer:0}),gg=mt(pg),vg=v({},Fa,{relatedTarget:0}),os=mt(vg),bg=v({},hl,{animationName:0,elapsedTime:0,pseudoElement:0}),Sg=mt(bg),xg=v({},hl,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Eg=mt(xg),Ag=v({},hl,{data:0}),Ef=mt(Ag),Rg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Tg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},wg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Og(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=wg[e])?!!t[e]:!1}function fs(){return Og}var Ng=v({},Fa,{key:function(e){if(e.key){var t=Rg[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=du(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Tg[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:fs,charCode:function(e){return e.type==="keypress"?du(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?du(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Cg=mt(Ng),Mg=v({},yu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Af=mt(Mg),Dg=v({},Fa,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:fs}),zg=mt(Dg),_g=v({},hl,{propertyName:0,elapsedTime:0,pseudoElement:0}),Ug=mt(_g),jg=v({},yu,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Hg=mt(jg),qg=v({},hl,{newState:0,oldState:0}),Bg=mt(qg),Lg=[9,13,27,32],ds=fn&&"CompositionEvent"in window,Wa=null;fn&&"documentMode"in document&&(Wa=document.documentMode);var kg=fn&&"TextEvent"in window&&!Wa,Rf=fn&&(!ds||Wa&&8<Wa&&11>=Wa),Tf=" ",wf=!1;function Of(e,t){switch(e){case"keyup":return Lg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Nf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Jl=!1;function Gg(e,t){switch(e){case"compositionend":return Nf(t);case"keypress":return t.which!==32?null:(wf=!0,Tf);case"textInput":return e=t.data,e===Tf&&wf?null:e;default:return null}}function Yg(e,t){if(Jl)return e==="compositionend"||!ds&&Of(e,t)?(e=bf(),fu=rs=Hn=null,Jl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Rf&&t.locale!=="ko"?null:t.data;default:return null}}var Qg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Cf(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Qg[e.type]:t==="textarea"}function Mf(e,t,n,l){Zl?Kl?Kl.push(l):Kl=[l]:Zl=l,t=tr(t,"onChange"),0<t.length&&(n=new mu("onChange","change",null,n,l),e.push({event:n,listeners:t}))}var Pa=null,Ia=null;function Vg(e){om(e,0)}function pu(e){var t=Za(e);if(ff(t))return e}function Df(e,t){if(e==="change")return t}var zf=!1;if(fn){var hs;if(fn){var ms="oninput"in document;if(!ms){var _f=document.createElement("div");_f.setAttribute("oninput","return;"),ms=typeof _f.oninput=="function"}hs=ms}else hs=!1;zf=hs&&(!document.documentMode||9<document.documentMode)}function Uf(){Pa&&(Pa.detachEvent("onpropertychange",jf),Ia=Pa=null)}function jf(e){if(e.propertyName==="value"&&pu(Ia)){var t=[];Mf(t,Ia,e,as(e)),vf(Vg,t)}}function Xg(e,t,n){e==="focusin"?(Uf(),Pa=t,Ia=n,Pa.attachEvent("onpropertychange",jf)):e==="focusout"&&Uf()}function Zg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return pu(Ia)}function Kg(e,t){if(e==="click")return pu(t)}function Jg(e,t){if(e==="input"||e==="change")return pu(t)}function Fg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var At=typeof Object.is=="function"?Object.is:Fg;function ei(e,t){if(At(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),l=Object.keys(t);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var u=n[l];if(!_n.call(t,u)||!At(e[u],t[u]))return!1}return!0}function Hf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function qf(e,t){var n=Hf(e);e=0;for(var l;n;){if(n.nodeType===3){if(l=e+n.textContent.length,e<=t&&l>=t)return{node:n,offset:t-e};e=l}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Hf(n)}}function Bf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Bf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Lf(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=cu(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=cu(e.document)}return t}function ys(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var $g=fn&&"documentMode"in document&&11>=document.documentMode,Fl=null,ps=null,ti=null,gs=!1;function kf(e,t,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;gs||Fl==null||Fl!==cu(l)||(l=Fl,"selectionStart"in l&&ys(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),ti&&ei(ti,l)||(ti=l,l=tr(ps,"onSelect"),0<l.length&&(t=new mu("onSelect","select",null,t,n),e.push({event:t,listeners:l}),t.target=Fl)))}function ml(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var $l={animationend:ml("Animation","AnimationEnd"),animationiteration:ml("Animation","AnimationIteration"),animationstart:ml("Animation","AnimationStart"),transitionrun:ml("Transition","TransitionRun"),transitionstart:ml("Transition","TransitionStart"),transitioncancel:ml("Transition","TransitionCancel"),transitionend:ml("Transition","TransitionEnd")},vs={},Gf={};fn&&(Gf=document.createElement("div").style,"AnimationEvent"in window||(delete $l.animationend.animation,delete $l.animationiteration.animation,delete $l.animationstart.animation),"TransitionEvent"in window||delete $l.transitionend.transition);function yl(e){if(vs[e])return vs[e];if(!$l[e])return e;var t=$l[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Gf)return vs[e]=t[n];return e}var Yf=yl("animationend"),Qf=yl("animationiteration"),Vf=yl("animationstart"),Wg=yl("transitionrun"),Pg=yl("transitionstart"),Ig=yl("transitioncancel"),Xf=yl("transitionend"),Zf=new Map,bs="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");bs.push("scrollEnd");function Gt(e,t){Zf.set(e,t),dl(t,[e])}var Kf=new WeakMap;function zt(e,t){if(typeof e=="object"&&e!==null){var n=Kf.get(e);return n!==void 0?n:(t={value:e,source:t,stack:cf(t)},Kf.set(e,t),t)}return{value:e,source:t,stack:cf(t)}}var _t=[],Wl=0,Ss=0;function gu(){for(var e=Wl,t=Ss=Wl=0;t<e;){var n=_t[t];_t[t++]=null;var l=_t[t];_t[t++]=null;var u=_t[t];_t[t++]=null;var c=_t[t];if(_t[t++]=null,l!==null&&u!==null){var d=l.pending;d===null?u.next=u:(u.next=d.next,d.next=u),l.pending=u}c!==0&&Jf(n,u,c)}}function vu(e,t,n,l){_t[Wl++]=e,_t[Wl++]=t,_t[Wl++]=n,_t[Wl++]=l,Ss|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function xs(e,t,n,l){return vu(e,t,n,l),bu(e)}function Pl(e,t){return vu(e,null,null,t),bu(e)}function Jf(e,t,n){e.lanes|=n;var l=e.alternate;l!==null&&(l.lanes|=n);for(var u=!1,c=e.return;c!==null;)c.childLanes|=n,l=c.alternate,l!==null&&(l.childLanes|=n),c.tag===22&&(e=c.stateNode,e===null||e._visibility&1||(u=!0)),e=c,c=c.return;return e.tag===3?(c=e.stateNode,u&&t!==null&&(u=31-Et(n),e=c.hiddenUpdates,l=e[u],l===null?e[u]=[t]:l.push(t),t.lane=n|536870912),c):null}function bu(e){if(50<wi)throw wi=0,Oc=null,Error(s(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Il={};function ev(e,t,n,l){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Rt(e,t,n,l){return new ev(e,t,n,l)}function Es(e){return e=e.prototype,!(!e||!e.isReactComponent)}function dn(e,t){var n=e.alternate;return n===null?(n=Rt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Ff(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Su(e,t,n,l,u,c){var d=0;if(l=e,typeof e=="function")Es(e)&&(d=1);else if(typeof e=="string")d=n0(e,n,W.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case le:return e=Rt(31,n,t,u),e.elementType=le,e.lanes=c,e;case A:return pl(n.children,u,c,t);case M:d=8,u|=24;break;case C:return e=Rt(12,n,t,u|2),e.elementType=C,e.lanes=c,e;case K:return e=Rt(13,n,t,u),e.elementType=K,e.lanes=c,e;case ue:return e=Rt(19,n,t,u),e.elementType=ue,e.lanes=c,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case H:case Y:d=10;break e;case Z:d=9;break e;case P:d=11;break e;case fe:d=14;break e;case F:d=16,l=null;break e}d=29,n=Error(s(130,e===null?"null":typeof e,"")),l=null}return t=Rt(d,n,t,u),t.elementType=e,t.type=l,t.lanes=c,t}function pl(e,t,n,l){return e=Rt(7,e,l,t),e.lanes=n,e}function As(e,t,n){return e=Rt(6,e,null,t),e.lanes=n,e}function Rs(e,t,n){return t=Rt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var ea=[],ta=0,xu=null,Eu=0,Ut=[],jt=0,gl=null,hn=1,mn="";function vl(e,t){ea[ta++]=Eu,ea[ta++]=xu,xu=e,Eu=t}function $f(e,t,n){Ut[jt++]=hn,Ut[jt++]=mn,Ut[jt++]=gl,gl=e;var l=hn;e=mn;var u=32-Et(l)-1;l&=~(1<<u),n+=1;var c=32-Et(t)+u;if(30<c){var d=u-u%5;c=(l&(1<<d)-1).toString(32),l>>=d,u-=d,hn=1<<32-Et(t)+u|n<<u|l,mn=c+e}else hn=1<<c|n<<u|l,mn=e}function Ts(e){e.return!==null&&(vl(e,1),$f(e,1,0))}function ws(e){for(;e===xu;)xu=ea[--ta],ea[ta]=null,Eu=ea[--ta],ea[ta]=null;for(;e===gl;)gl=Ut[--jt],Ut[jt]=null,mn=Ut[--jt],Ut[jt]=null,hn=Ut[--jt],Ut[jt]=null}var ot=null,Be=null,Ae=!1,bl=null,It=!1,Os=Error(s(519));function Sl(e){var t=Error(s(418,""));throw ai(zt(t,e)),Os}function Wf(e){var t=e.stateNode,n=e.type,l=e.memoizedProps;switch(t[lt]=e,t[ht]=l,n){case"dialog":ve("cancel",t),ve("close",t);break;case"iframe":case"object":case"embed":ve("load",t);break;case"video":case"audio":for(n=0;n<Ni.length;n++)ve(Ni[n],t);break;case"source":ve("error",t);break;case"img":case"image":case"link":ve("error",t),ve("load",t);break;case"details":ve("toggle",t);break;case"input":ve("invalid",t),df(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),su(t);break;case"select":ve("invalid",t);break;case"textarea":ve("invalid",t),mf(t,l.value,l.defaultValue,l.children),su(t)}n=l.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||l.suppressHydrationWarning===!0||mm(t.textContent,n)?(l.popover!=null&&(ve("beforetoggle",t),ve("toggle",t)),l.onScroll!=null&&ve("scroll",t),l.onScrollEnd!=null&&ve("scrollend",t),l.onClick!=null&&(t.onclick=nr),t=!0):t=!1,t||Sl(e)}function Pf(e){for(ot=e.return;ot;)switch(ot.tag){case 5:case 13:It=!1;return;case 27:case 3:It=!0;return;default:ot=ot.return}}function ni(e){if(e!==ot)return!1;if(!Ae)return Pf(e),Ae=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||Qc(e.type,e.memoizedProps)),n=!n),n&&Be&&Sl(e),Pf(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(s(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){Be=Qt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}Be=null}}else t===27?(t=Be,Pn(e.type)?(e=Kc,Kc=null,Be=e):Be=t):Be=ot?Qt(e.stateNode.nextSibling):null;return!0}function li(){Be=ot=null,Ae=!1}function If(){var e=bl;return e!==null&&(gt===null?gt=e:gt.push.apply(gt,e),bl=null),e}function ai(e){bl===null?bl=[e]:bl.push(e)}var Ns=Q(null),xl=null,yn=null;function qn(e,t,n){X(Ns,t._currentValue),t._currentValue=n}function pn(e){e._currentValue=Ns.current,$(Ns)}function Cs(e,t,n){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===n)break;e=e.return}}function Ms(e,t,n,l){var u=e.child;for(u!==null&&(u.return=e);u!==null;){var c=u.dependencies;if(c!==null){var d=u.child;c=c.firstContext;e:for(;c!==null;){var y=c;c=u;for(var b=0;b<t.length;b++)if(y.context===t[b]){c.lanes|=n,y=c.alternate,y!==null&&(y.lanes|=n),Cs(c.return,n,e),l||(d=null);break e}c=y.next}}else if(u.tag===18){if(d=u.return,d===null)throw Error(s(341));d.lanes|=n,c=d.alternate,c!==null&&(c.lanes|=n),Cs(d,n,e),d=null}else d=u.child;if(d!==null)d.return=u;else for(d=u;d!==null;){if(d===e){d=null;break}if(u=d.sibling,u!==null){u.return=d.return,d=u;break}d=d.return}u=d}}function ii(e,t,n,l){e=null;for(var u=t,c=!1;u!==null;){if(!c){if((u.flags&524288)!==0)c=!0;else if((u.flags&262144)!==0)break}if(u.tag===10){var d=u.alternate;if(d===null)throw Error(s(387));if(d=d.memoizedProps,d!==null){var y=u.type;At(u.pendingProps.value,d.value)||(e!==null?e.push(y):e=[y])}}else if(u===Se.current){if(d=u.alternate,d===null)throw Error(s(387));d.memoizedState.memoizedState!==u.memoizedState.memoizedState&&(e!==null?e.push(Ui):e=[Ui])}u=u.return}e!==null&&Ms(t,e,n,l),t.flags|=262144}function Au(e){for(e=e.firstContext;e!==null;){if(!At(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function El(e){xl=e,yn=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function at(e){return ed(xl,e)}function Ru(e,t){return xl===null&&El(e),ed(e,t)}function ed(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},yn===null){if(e===null)throw Error(s(308));yn=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else yn=yn.next=t;return n}var tv=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},nv=a.unstable_scheduleCallback,lv=a.unstable_NormalPriority,Ke={$$typeof:Y,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Ds(){return{controller:new tv,data:new Map,refCount:0}}function ui(e){e.refCount--,e.refCount===0&&nv(lv,function(){e.controller.abort()})}var ri=null,zs=0,na=0,la=null;function av(e,t){if(ri===null){var n=ri=[];zs=0,na=Uc(),la={status:"pending",value:void 0,then:function(l){n.push(l)}}}return zs++,t.then(td,td),t}function td(){if(--zs===0&&ri!==null){la!==null&&(la.status="fulfilled");var e=ri;ri=null,na=0,la=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function iv(e,t){var n=[],l={status:"pending",value:null,reason:null,then:function(u){n.push(u)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var u=0;u<n.length;u++)(0,n[u])(t)},function(u){for(l.status="rejected",l.reason=u,u=0;u<n.length;u++)(0,n[u])(void 0)}),l}var nd=j.S;j.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&av(e,t),nd!==null&&nd(e,t)};var Al=Q(null);function _s(){var e=Al.current;return e!==null?e:ze.pooledCache}function Tu(e,t){t===null?X(Al,Al.current):X(Al,t.pool)}function ld(){var e=_s();return e===null?null:{parent:Ke._currentValue,pool:e}}var si=Error(s(460)),ad=Error(s(474)),wu=Error(s(542)),Us={then:function(){}};function id(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Ou(){}function ud(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(Ou,Ou),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,sd(e),e;default:if(typeof t.status=="string")t.then(Ou,Ou);else{if(e=ze,e!==null&&100<e.shellSuspendCounter)throw Error(s(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var u=t;u.status="fulfilled",u.value=l}},function(l){if(t.status==="pending"){var u=t;u.status="rejected",u.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,sd(e),e}throw ci=t,si}}var ci=null;function rd(){if(ci===null)throw Error(s(459));var e=ci;return ci=null,e}function sd(e){if(e===si||e===wu)throw Error(s(483))}var Bn=!1;function js(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Hs(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Ln(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function kn(e,t,n){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(Te&2)!==0){var u=l.pending;return u===null?t.next=t:(t.next=u.next,u.next=t),l.pending=t,t=bu(e),Jf(e,null,n),t}return vu(e,l,t,n),bu(e)}function oi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,n|=l,t.lanes=n,ef(e,n)}}function qs(e,t){var n=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var u=null,c=null;if(n=n.firstBaseUpdate,n!==null){do{var d={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};c===null?u=c=d:c=c.next=d,n=n.next}while(n!==null);c===null?u=c=t:c=c.next=t}else u=c=t;n={baseState:l.baseState,firstBaseUpdate:u,lastBaseUpdate:c,shared:l.shared,callbacks:l.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var Bs=!1;function fi(){if(Bs){var e=la;if(e!==null)throw e}}function di(e,t,n,l){Bs=!1;var u=e.updateQueue;Bn=!1;var c=u.firstBaseUpdate,d=u.lastBaseUpdate,y=u.shared.pending;if(y!==null){u.shared.pending=null;var b=y,N=b.next;b.next=null,d===null?c=N:d.next=N,d=b;var q=e.alternate;q!==null&&(q=q.updateQueue,y=q.lastBaseUpdate,y!==d&&(y===null?q.firstBaseUpdate=N:y.next=N,q.lastBaseUpdate=b))}if(c!==null){var G=u.baseState;d=0,q=N=b=null,y=c;do{var D=y.lane&-536870913,z=D!==y.lane;if(z?(be&D)===D:(l&D)===D){D!==0&&D===na&&(Bs=!0),q!==null&&(q=q.next={lane:0,tag:y.tag,payload:y.payload,callback:null,next:null});e:{var re=e,ae=y;D=t;var Ce=n;switch(ae.tag){case 1:if(re=ae.payload,typeof re=="function"){G=re.call(Ce,G,D);break e}G=re;break e;case 3:re.flags=re.flags&-65537|128;case 0:if(re=ae.payload,D=typeof re=="function"?re.call(Ce,G,D):re,D==null)break e;G=v({},G,D);break e;case 2:Bn=!0}}D=y.callback,D!==null&&(e.flags|=64,z&&(e.flags|=8192),z=u.callbacks,z===null?u.callbacks=[D]:z.push(D))}else z={lane:D,tag:y.tag,payload:y.payload,callback:y.callback,next:null},q===null?(N=q=z,b=G):q=q.next=z,d|=D;if(y=y.next,y===null){if(y=u.shared.pending,y===null)break;z=y,y=z.next,z.next=null,u.lastBaseUpdate=z,u.shared.pending=null}}while(!0);q===null&&(b=G),u.baseState=b,u.firstBaseUpdate=N,u.lastBaseUpdate=q,c===null&&(u.shared.lanes=0),Jn|=d,e.lanes=d,e.memoizedState=G}}function cd(e,t){if(typeof e!="function")throw Error(s(191,e));e.call(t)}function od(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)cd(n[e],t)}var aa=Q(null),Nu=Q(0);function fd(e,t){e=An,X(Nu,e),X(aa,t),An=e|t.baseLanes}function Ls(){X(Nu,An),X(aa,aa.current)}function ks(){An=Nu.current,$(aa),$(Nu)}var Gn=0,he=null,Oe=null,Xe=null,Cu=!1,ia=!1,Rl=!1,Mu=0,hi=0,ua=null,uv=0;function Ye(){throw Error(s(321))}function Gs(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!At(e[n],t[n]))return!1;return!0}function Ys(e,t,n,l,u,c){return Gn=c,he=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,j.H=e===null||e.memoizedState===null?Jd:Fd,Rl=!1,c=n(l,u),Rl=!1,ia&&(c=hd(t,n,l,u)),dd(e),c}function dd(e){j.H=Hu;var t=Oe!==null&&Oe.next!==null;if(Gn=0,Xe=Oe=he=null,Cu=!1,hi=0,ua=null,t)throw Error(s(300));e===null||We||(e=e.dependencies,e!==null&&Au(e)&&(We=!0))}function hd(e,t,n,l){he=e;var u=0;do{if(ia&&(ua=null),hi=0,ia=!1,25<=u)throw Error(s(301));if(u+=1,Xe=Oe=null,e.updateQueue!=null){var c=e.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}j.H=hv,c=t(n,l)}while(ia);return c}function rv(){var e=j.H,t=e.useState()[0];return t=typeof t.then=="function"?mi(t):t,e=e.useState()[0],(Oe!==null?Oe.memoizedState:null)!==e&&(he.flags|=1024),t}function Qs(){var e=Mu!==0;return Mu=0,e}function Vs(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Xs(e){if(Cu){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Cu=!1}Gn=0,Xe=Oe=he=null,ia=!1,hi=Mu=0,ua=null}function yt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Xe===null?he.memoizedState=Xe=e:Xe=Xe.next=e,Xe}function Ze(){if(Oe===null){var e=he.alternate;e=e!==null?e.memoizedState:null}else e=Oe.next;var t=Xe===null?he.memoizedState:Xe.next;if(t!==null)Xe=t,Oe=e;else{if(e===null)throw he.alternate===null?Error(s(467)):Error(s(310));Oe=e,e={memoizedState:Oe.memoizedState,baseState:Oe.baseState,baseQueue:Oe.baseQueue,queue:Oe.queue,next:null},Xe===null?he.memoizedState=Xe=e:Xe=Xe.next=e}return Xe}function Zs(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function mi(e){var t=hi;return hi+=1,ua===null&&(ua=[]),e=ud(ua,e,t),t=he,(Xe===null?t.memoizedState:Xe.next)===null&&(t=t.alternate,j.H=t===null||t.memoizedState===null?Jd:Fd),e}function Du(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return mi(e);if(e.$$typeof===Y)return at(e)}throw Error(s(438,String(e)))}function Ks(e){var t=null,n=he.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var l=he.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(u){return u.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=Zs(),he.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),l=0;l<e;l++)n[l]=je;return t.index++,n}function gn(e,t){return typeof t=="function"?t(e):t}function zu(e){var t=Ze();return Js(t,Oe,e)}function Js(e,t,n){var l=e.queue;if(l===null)throw Error(s(311));l.lastRenderedReducer=n;var u=e.baseQueue,c=l.pending;if(c!==null){if(u!==null){var d=u.next;u.next=c.next,c.next=d}t.baseQueue=u=c,l.pending=null}if(c=e.baseState,u===null)e.memoizedState=c;else{t=u.next;var y=d=null,b=null,N=t,q=!1;do{var G=N.lane&-536870913;if(G!==N.lane?(be&G)===G:(Gn&G)===G){var D=N.revertLane;if(D===0)b!==null&&(b=b.next={lane:0,revertLane:0,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null}),G===na&&(q=!0);else if((Gn&D)===D){N=N.next,D===na&&(q=!0);continue}else G={lane:0,revertLane:N.revertLane,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null},b===null?(y=b=G,d=c):b=b.next=G,he.lanes|=D,Jn|=D;G=N.action,Rl&&n(c,G),c=N.hasEagerState?N.eagerState:n(c,G)}else D={lane:G,revertLane:N.revertLane,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null},b===null?(y=b=D,d=c):b=b.next=D,he.lanes|=G,Jn|=G;N=N.next}while(N!==null&&N!==t);if(b===null?d=c:b.next=y,!At(c,e.memoizedState)&&(We=!0,q&&(n=la,n!==null)))throw n;e.memoizedState=c,e.baseState=d,e.baseQueue=b,l.lastRenderedState=c}return u===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function Fs(e){var t=Ze(),n=t.queue;if(n===null)throw Error(s(311));n.lastRenderedReducer=e;var l=n.dispatch,u=n.pending,c=t.memoizedState;if(u!==null){n.pending=null;var d=u=u.next;do c=e(c,d.action),d=d.next;while(d!==u);At(c,t.memoizedState)||(We=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),n.lastRenderedState=c}return[c,l]}function md(e,t,n){var l=he,u=Ze(),c=Ae;if(c){if(n===void 0)throw Error(s(407));n=n()}else n=t();var d=!At((Oe||u).memoizedState,n);d&&(u.memoizedState=n,We=!0),u=u.queue;var y=gd.bind(null,l,u,e);if(yi(2048,8,y,[e]),u.getSnapshot!==t||d||Xe!==null&&Xe.memoizedState.tag&1){if(l.flags|=2048,ra(9,_u(),pd.bind(null,l,u,n,t),null),ze===null)throw Error(s(349));c||(Gn&124)!==0||yd(l,t,n)}return n}function yd(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=he.updateQueue,t===null?(t=Zs(),he.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function pd(e,t,n,l){t.value=n,t.getSnapshot=l,vd(t)&&bd(e)}function gd(e,t,n){return n(function(){vd(t)&&bd(e)})}function vd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!At(e,n)}catch{return!0}}function bd(e){var t=Pl(e,2);t!==null&&Ct(t,e,2)}function $s(e){var t=yt();if(typeof e=="function"){var n=e;if(e=n(),Rl){Un(!0);try{n()}finally{Un(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:gn,lastRenderedState:e},t}function Sd(e,t,n,l){return e.baseState=n,Js(e,Oe,typeof l=="function"?l:gn)}function sv(e,t,n,l,u){if(ju(e))throw Error(s(485));if(e=t.action,e!==null){var c={payload:u,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(d){c.listeners.push(d)}};j.T!==null?n(!0):c.isTransition=!1,l(c),n=t.pending,n===null?(c.next=t.pending=c,xd(t,c)):(c.next=n.next,t.pending=n.next=c)}}function xd(e,t){var n=t.action,l=t.payload,u=e.state;if(t.isTransition){var c=j.T,d={};j.T=d;try{var y=n(u,l),b=j.S;b!==null&&b(d,y),Ed(e,t,y)}catch(N){Ws(e,t,N)}finally{j.T=c}}else try{c=n(u,l),Ed(e,t,c)}catch(N){Ws(e,t,N)}}function Ed(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(l){Ad(e,t,l)},function(l){return Ws(e,t,l)}):Ad(e,t,n)}function Ad(e,t,n){t.status="fulfilled",t.value=n,Rd(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,xd(e,n)))}function Ws(e,t,n){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=n,Rd(t),t=t.next;while(t!==l)}e.action=null}function Rd(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Td(e,t){return t}function wd(e,t){if(Ae){var n=ze.formState;if(n!==null){e:{var l=he;if(Ae){if(Be){t:{for(var u=Be,c=It;u.nodeType!==8;){if(!c){u=null;break t}if(u=Qt(u.nextSibling),u===null){u=null;break t}}c=u.data,u=c==="F!"||c==="F"?u:null}if(u){Be=Qt(u.nextSibling),l=u.data==="F!";break e}}Sl(l)}l=!1}l&&(t=n[0])}}return n=yt(),n.memoizedState=n.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Td,lastRenderedState:t},n.queue=l,n=Xd.bind(null,he,l),l.dispatch=n,l=$s(!1),c=nc.bind(null,he,!1,l.queue),l=yt(),u={state:t,dispatch:null,action:e,pending:null},l.queue=u,n=sv.bind(null,he,u,c,n),u.dispatch=n,l.memoizedState=e,[t,n,!1]}function Od(e){var t=Ze();return Nd(t,Oe,e)}function Nd(e,t,n){if(t=Js(e,t,Td)[0],e=zu(gn)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=mi(t)}catch(d){throw d===si?wu:d}else l=t;t=Ze();var u=t.queue,c=u.dispatch;return n!==t.memoizedState&&(he.flags|=2048,ra(9,_u(),cv.bind(null,u,n),null)),[l,c,e]}function cv(e,t){e.action=t}function Cd(e){var t=Ze(),n=Oe;if(n!==null)return Nd(t,n,e);Ze(),t=t.memoizedState,n=Ze();var l=n.queue.dispatch;return n.memoizedState=e,[t,l,!1]}function ra(e,t,n,l){return e={tag:e,create:n,deps:l,inst:t,next:null},t=he.updateQueue,t===null&&(t=Zs(),he.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(l=n.next,n.next=e,e.next=l,t.lastEffect=e),e}function _u(){return{destroy:void 0,resource:void 0}}function Md(){return Ze().memoizedState}function Uu(e,t,n,l){var u=yt();l=l===void 0?null:l,he.flags|=e,u.memoizedState=ra(1|t,_u(),n,l)}function yi(e,t,n,l){var u=Ze();l=l===void 0?null:l;var c=u.memoizedState.inst;Oe!==null&&l!==null&&Gs(l,Oe.memoizedState.deps)?u.memoizedState=ra(t,c,n,l):(he.flags|=e,u.memoizedState=ra(1|t,c,n,l))}function Dd(e,t){Uu(8390656,8,e,t)}function zd(e,t){yi(2048,8,e,t)}function _d(e,t){return yi(4,2,e,t)}function Ud(e,t){return yi(4,4,e,t)}function jd(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Hd(e,t,n){n=n!=null?n.concat([e]):null,yi(4,4,jd.bind(null,t,e),n)}function Ps(){}function qd(e,t){var n=Ze();t=t===void 0?null:t;var l=n.memoizedState;return t!==null&&Gs(t,l[1])?l[0]:(n.memoizedState=[e,t],e)}function Bd(e,t){var n=Ze();t=t===void 0?null:t;var l=n.memoizedState;if(t!==null&&Gs(t,l[1]))return l[0];if(l=e(),Rl){Un(!0);try{e()}finally{Un(!1)}}return n.memoizedState=[l,t],l}function Is(e,t,n){return n===void 0||(Gn&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=Gh(),he.lanes|=e,Jn|=e,n)}function Ld(e,t,n,l){return At(n,t)?n:aa.current!==null?(e=Is(e,n,l),At(e,t)||(We=!0),e):(Gn&42)===0?(We=!0,e.memoizedState=n):(e=Gh(),he.lanes|=e,Jn|=e,t)}function kd(e,t,n,l,u){var c=J.p;J.p=c!==0&&8>c?c:8;var d=j.T,y={};j.T=y,nc(e,!1,t,n);try{var b=u(),N=j.S;if(N!==null&&N(y,b),b!==null&&typeof b=="object"&&typeof b.then=="function"){var q=iv(b,l);pi(e,t,q,Nt(e))}else pi(e,t,l,Nt(e))}catch(G){pi(e,t,{then:function(){},status:"rejected",reason:G},Nt())}finally{J.p=c,j.T=d}}function ov(){}function ec(e,t,n,l){if(e.tag!==5)throw Error(s(476));var u=Gd(e).queue;kd(e,u,t,V,n===null?ov:function(){return Yd(e),n(l)})}function Gd(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:V,baseState:V,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:gn,lastRenderedState:V},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:gn,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Yd(e){var t=Gd(e).next.queue;pi(e,t,{},Nt())}function tc(){return at(Ui)}function Qd(){return Ze().memoizedState}function Vd(){return Ze().memoizedState}function fv(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=Nt();e=Ln(n);var l=kn(t,e,n);l!==null&&(Ct(l,t,n),oi(l,t,n)),t={cache:Ds()},e.payload=t;return}t=t.return}}function dv(e,t,n){var l=Nt();n={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},ju(e)?Zd(t,n):(n=xs(e,t,n,l),n!==null&&(Ct(n,e,l),Kd(n,t,l)))}function Xd(e,t,n){var l=Nt();pi(e,t,n,l)}function pi(e,t,n,l){var u={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(ju(e))Zd(t,u);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var d=t.lastRenderedState,y=c(d,n);if(u.hasEagerState=!0,u.eagerState=y,At(y,d))return vu(e,t,u,0),ze===null&&gu(),!1}catch{}finally{}if(n=xs(e,t,u,l),n!==null)return Ct(n,e,l),Kd(n,t,l),!0}return!1}function nc(e,t,n,l){if(l={lane:2,revertLane:Uc(),action:l,hasEagerState:!1,eagerState:null,next:null},ju(e)){if(t)throw Error(s(479))}else t=xs(e,n,l,2),t!==null&&Ct(t,e,2)}function ju(e){var t=e.alternate;return e===he||t!==null&&t===he}function Zd(e,t){ia=Cu=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Kd(e,t,n){if((n&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,n|=l,t.lanes=n,ef(e,n)}}var Hu={readContext:at,use:Du,useCallback:Ye,useContext:Ye,useEffect:Ye,useImperativeHandle:Ye,useLayoutEffect:Ye,useInsertionEffect:Ye,useMemo:Ye,useReducer:Ye,useRef:Ye,useState:Ye,useDebugValue:Ye,useDeferredValue:Ye,useTransition:Ye,useSyncExternalStore:Ye,useId:Ye,useHostTransitionStatus:Ye,useFormState:Ye,useActionState:Ye,useOptimistic:Ye,useMemoCache:Ye,useCacheRefresh:Ye},Jd={readContext:at,use:Du,useCallback:function(e,t){return yt().memoizedState=[e,t===void 0?null:t],e},useContext:at,useEffect:Dd,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,Uu(4194308,4,jd.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Uu(4194308,4,e,t)},useInsertionEffect:function(e,t){Uu(4,2,e,t)},useMemo:function(e,t){var n=yt();t=t===void 0?null:t;var l=e();if(Rl){Un(!0);try{e()}finally{Un(!1)}}return n.memoizedState=[l,t],l},useReducer:function(e,t,n){var l=yt();if(n!==void 0){var u=n(t);if(Rl){Un(!0);try{n(t)}finally{Un(!1)}}}else u=t;return l.memoizedState=l.baseState=u,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:u},l.queue=e,e=e.dispatch=dv.bind(null,he,e),[l.memoizedState,e]},useRef:function(e){var t=yt();return e={current:e},t.memoizedState=e},useState:function(e){e=$s(e);var t=e.queue,n=Xd.bind(null,he,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Ps,useDeferredValue:function(e,t){var n=yt();return Is(n,e,t)},useTransition:function(){var e=$s(!1);return e=kd.bind(null,he,e.queue,!0,!1),yt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var l=he,u=yt();if(Ae){if(n===void 0)throw Error(s(407));n=n()}else{if(n=t(),ze===null)throw Error(s(349));(be&124)!==0||yd(l,t,n)}u.memoizedState=n;var c={value:n,getSnapshot:t};return u.queue=c,Dd(gd.bind(null,l,c,e),[e]),l.flags|=2048,ra(9,_u(),pd.bind(null,l,c,n,t),null),n},useId:function(){var e=yt(),t=ze.identifierPrefix;if(Ae){var n=mn,l=hn;n=(l&~(1<<32-Et(l)-1)).toString(32)+n,t="«"+t+"R"+n,n=Mu++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=uv++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:tc,useFormState:wd,useActionState:wd,useOptimistic:function(e){var t=yt();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=nc.bind(null,he,!0,n),n.dispatch=t,[e,t]},useMemoCache:Ks,useCacheRefresh:function(){return yt().memoizedState=fv.bind(null,he)}},Fd={readContext:at,use:Du,useCallback:qd,useContext:at,useEffect:zd,useImperativeHandle:Hd,useInsertionEffect:_d,useLayoutEffect:Ud,useMemo:Bd,useReducer:zu,useRef:Md,useState:function(){return zu(gn)},useDebugValue:Ps,useDeferredValue:function(e,t){var n=Ze();return Ld(n,Oe.memoizedState,e,t)},useTransition:function(){var e=zu(gn)[0],t=Ze().memoizedState;return[typeof e=="boolean"?e:mi(e),t]},useSyncExternalStore:md,useId:Qd,useHostTransitionStatus:tc,useFormState:Od,useActionState:Od,useOptimistic:function(e,t){var n=Ze();return Sd(n,Oe,e,t)},useMemoCache:Ks,useCacheRefresh:Vd},hv={readContext:at,use:Du,useCallback:qd,useContext:at,useEffect:zd,useImperativeHandle:Hd,useInsertionEffect:_d,useLayoutEffect:Ud,useMemo:Bd,useReducer:Fs,useRef:Md,useState:function(){return Fs(gn)},useDebugValue:Ps,useDeferredValue:function(e,t){var n=Ze();return Oe===null?Is(n,e,t):Ld(n,Oe.memoizedState,e,t)},useTransition:function(){var e=Fs(gn)[0],t=Ze().memoizedState;return[typeof e=="boolean"?e:mi(e),t]},useSyncExternalStore:md,useId:Qd,useHostTransitionStatus:tc,useFormState:Cd,useActionState:Cd,useOptimistic:function(e,t){var n=Ze();return Oe!==null?Sd(n,Oe,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Ks,useCacheRefresh:Vd},sa=null,gi=0;function qu(e){var t=gi;return gi+=1,sa===null&&(sa=[]),ud(sa,e,t)}function vi(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Bu(e,t){throw t.$$typeof===E?Error(s(525)):(e=Object.prototype.toString.call(t),Error(s(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function $d(e){var t=e._init;return t(e._payload)}function Wd(e){function t(R,x){if(e){var w=R.deletions;w===null?(R.deletions=[x],R.flags|=16):w.push(x)}}function n(R,x){if(!e)return null;for(;x!==null;)t(R,x),x=x.sibling;return null}function l(R){for(var x=new Map;R!==null;)R.key!==null?x.set(R.key,R):x.set(R.index,R),R=R.sibling;return x}function u(R,x){return R=dn(R,x),R.index=0,R.sibling=null,R}function c(R,x,w){return R.index=w,e?(w=R.alternate,w!==null?(w=w.index,w<x?(R.flags|=67108866,x):w):(R.flags|=67108866,x)):(R.flags|=1048576,x)}function d(R){return e&&R.alternate===null&&(R.flags|=67108866),R}function y(R,x,w,L){return x===null||x.tag!==6?(x=As(w,R.mode,L),x.return=R,x):(x=u(x,w),x.return=R,x)}function b(R,x,w,L){var I=w.type;return I===A?q(R,x,w.props.children,L,w.key):x!==null&&(x.elementType===I||typeof I=="object"&&I!==null&&I.$$typeof===F&&$d(I)===x.type)?(x=u(x,w.props),vi(x,w),x.return=R,x):(x=Su(w.type,w.key,w.props,null,R.mode,L),vi(x,w),x.return=R,x)}function N(R,x,w,L){return x===null||x.tag!==4||x.stateNode.containerInfo!==w.containerInfo||x.stateNode.implementation!==w.implementation?(x=Rs(w,R.mode,L),x.return=R,x):(x=u(x,w.children||[]),x.return=R,x)}function q(R,x,w,L,I){return x===null||x.tag!==7?(x=pl(w,R.mode,L,I),x.return=R,x):(x=u(x,w),x.return=R,x)}function G(R,x,w){if(typeof x=="string"&&x!==""||typeof x=="number"||typeof x=="bigint")return x=As(""+x,R.mode,w),x.return=R,x;if(typeof x=="object"&&x!==null){switch(x.$$typeof){case T:return w=Su(x.type,x.key,x.props,null,R.mode,w),vi(w,x),w.return=R,w;case B:return x=Rs(x,R.mode,w),x.return=R,x;case F:var L=x._init;return x=L(x._payload),G(R,x,w)}if(He(x)||Ge(x))return x=pl(x,R.mode,w,null),x.return=R,x;if(typeof x.then=="function")return G(R,qu(x),w);if(x.$$typeof===Y)return G(R,Ru(R,x),w);Bu(R,x)}return null}function D(R,x,w,L){var I=x!==null?x.key:null;if(typeof w=="string"&&w!==""||typeof w=="number"||typeof w=="bigint")return I!==null?null:y(R,x,""+w,L);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case T:return w.key===I?b(R,x,w,L):null;case B:return w.key===I?N(R,x,w,L):null;case F:return I=w._init,w=I(w._payload),D(R,x,w,L)}if(He(w)||Ge(w))return I!==null?null:q(R,x,w,L,null);if(typeof w.then=="function")return D(R,x,qu(w),L);if(w.$$typeof===Y)return D(R,x,Ru(R,w),L);Bu(R,w)}return null}function z(R,x,w,L,I){if(typeof L=="string"&&L!==""||typeof L=="number"||typeof L=="bigint")return R=R.get(w)||null,y(x,R,""+L,I);if(typeof L=="object"&&L!==null){switch(L.$$typeof){case T:return R=R.get(L.key===null?w:L.key)||null,b(x,R,L,I);case B:return R=R.get(L.key===null?w:L.key)||null,N(x,R,L,I);case F:var ye=L._init;return L=ye(L._payload),z(R,x,w,L,I)}if(He(L)||Ge(L))return R=R.get(w)||null,q(x,R,L,I,null);if(typeof L.then=="function")return z(R,x,w,qu(L),I);if(L.$$typeof===Y)return z(R,x,w,Ru(x,L),I);Bu(x,L)}return null}function re(R,x,w,L){for(var I=null,ye=null,ne=x,ie=x=0,Ie=null;ne!==null&&ie<w.length;ie++){ne.index>ie?(Ie=ne,ne=null):Ie=ne.sibling;var xe=D(R,ne,w[ie],L);if(xe===null){ne===null&&(ne=Ie);break}e&&ne&&xe.alternate===null&&t(R,ne),x=c(xe,x,ie),ye===null?I=xe:ye.sibling=xe,ye=xe,ne=Ie}if(ie===w.length)return n(R,ne),Ae&&vl(R,ie),I;if(ne===null){for(;ie<w.length;ie++)ne=G(R,w[ie],L),ne!==null&&(x=c(ne,x,ie),ye===null?I=ne:ye.sibling=ne,ye=ne);return Ae&&vl(R,ie),I}for(ne=l(ne);ie<w.length;ie++)Ie=z(ne,R,ie,w[ie],L),Ie!==null&&(e&&Ie.alternate!==null&&ne.delete(Ie.key===null?ie:Ie.key),x=c(Ie,x,ie),ye===null?I=Ie:ye.sibling=Ie,ye=Ie);return e&&ne.forEach(function(ll){return t(R,ll)}),Ae&&vl(R,ie),I}function ae(R,x,w,L){if(w==null)throw Error(s(151));for(var I=null,ye=null,ne=x,ie=x=0,Ie=null,xe=w.next();ne!==null&&!xe.done;ie++,xe=w.next()){ne.index>ie?(Ie=ne,ne=null):Ie=ne.sibling;var ll=D(R,ne,xe.value,L);if(ll===null){ne===null&&(ne=Ie);break}e&&ne&&ll.alternate===null&&t(R,ne),x=c(ll,x,ie),ye===null?I=ll:ye.sibling=ll,ye=ll,ne=Ie}if(xe.done)return n(R,ne),Ae&&vl(R,ie),I;if(ne===null){for(;!xe.done;ie++,xe=w.next())xe=G(R,xe.value,L),xe!==null&&(x=c(xe,x,ie),ye===null?I=xe:ye.sibling=xe,ye=xe);return Ae&&vl(R,ie),I}for(ne=l(ne);!xe.done;ie++,xe=w.next())xe=z(ne,R,ie,xe.value,L),xe!==null&&(e&&xe.alternate!==null&&ne.delete(xe.key===null?ie:xe.key),x=c(xe,x,ie),ye===null?I=xe:ye.sibling=xe,ye=xe);return e&&ne.forEach(function(m0){return t(R,m0)}),Ae&&vl(R,ie),I}function Ce(R,x,w,L){if(typeof w=="object"&&w!==null&&w.type===A&&w.key===null&&(w=w.props.children),typeof w=="object"&&w!==null){switch(w.$$typeof){case T:e:{for(var I=w.key;x!==null;){if(x.key===I){if(I=w.type,I===A){if(x.tag===7){n(R,x.sibling),L=u(x,w.props.children),L.return=R,R=L;break e}}else if(x.elementType===I||typeof I=="object"&&I!==null&&I.$$typeof===F&&$d(I)===x.type){n(R,x.sibling),L=u(x,w.props),vi(L,w),L.return=R,R=L;break e}n(R,x);break}else t(R,x);x=x.sibling}w.type===A?(L=pl(w.props.children,R.mode,L,w.key),L.return=R,R=L):(L=Su(w.type,w.key,w.props,null,R.mode,L),vi(L,w),L.return=R,R=L)}return d(R);case B:e:{for(I=w.key;x!==null;){if(x.key===I)if(x.tag===4&&x.stateNode.containerInfo===w.containerInfo&&x.stateNode.implementation===w.implementation){n(R,x.sibling),L=u(x,w.children||[]),L.return=R,R=L;break e}else{n(R,x);break}else t(R,x);x=x.sibling}L=Rs(w,R.mode,L),L.return=R,R=L}return d(R);case F:return I=w._init,w=I(w._payload),Ce(R,x,w,L)}if(He(w))return re(R,x,w,L);if(Ge(w)){if(I=Ge(w),typeof I!="function")throw Error(s(150));return w=I.call(w),ae(R,x,w,L)}if(typeof w.then=="function")return Ce(R,x,qu(w),L);if(w.$$typeof===Y)return Ce(R,x,Ru(R,w),L);Bu(R,w)}return typeof w=="string"&&w!==""||typeof w=="number"||typeof w=="bigint"?(w=""+w,x!==null&&x.tag===6?(n(R,x.sibling),L=u(x,w),L.return=R,R=L):(n(R,x),L=As(w,R.mode,L),L.return=R,R=L),d(R)):n(R,x)}return function(R,x,w,L){try{gi=0;var I=Ce(R,x,w,L);return sa=null,I}catch(ne){if(ne===si||ne===wu)throw ne;var ye=Rt(29,ne,null,R.mode);return ye.lanes=L,ye.return=R,ye}finally{}}}var ca=Wd(!0),Pd=Wd(!1),Ht=Q(null),en=null;function Yn(e){var t=e.alternate;X(Je,Je.current&1),X(Ht,e),en===null&&(t===null||aa.current!==null||t.memoizedState!==null)&&(en=e)}function Id(e){if(e.tag===22){if(X(Je,Je.current),X(Ht,e),en===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(en=e)}}else Qn()}function Qn(){X(Je,Je.current),X(Ht,Ht.current)}function vn(e){$(Ht),en===e&&(en=null),$(Je)}var Je=Q(0);function Lu(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||Zc(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function lc(e,t,n,l){t=e.memoizedState,n=n(l,t),n=n==null?t:v({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var ac={enqueueSetState:function(e,t,n){e=e._reactInternals;var l=Nt(),u=Ln(l);u.payload=t,n!=null&&(u.callback=n),t=kn(e,u,l),t!==null&&(Ct(t,e,l),oi(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var l=Nt(),u=Ln(l);u.tag=1,u.payload=t,n!=null&&(u.callback=n),t=kn(e,u,l),t!==null&&(Ct(t,e,l),oi(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Nt(),l=Ln(n);l.tag=2,t!=null&&(l.callback=t),t=kn(e,l,n),t!==null&&(Ct(t,e,n),oi(t,e,n))}};function eh(e,t,n,l,u,c,d){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,c,d):t.prototype&&t.prototype.isPureReactComponent?!ei(n,l)||!ei(u,c):!0}function th(e,t,n,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,l),t.state!==e&&ac.enqueueReplaceState(t,t.state,null)}function Tl(e,t){var n=t;if("ref"in t){n={};for(var l in t)l!=="ref"&&(n[l]=t[l])}if(e=e.defaultProps){n===t&&(n=v({},n));for(var u in e)n[u]===void 0&&(n[u]=e[u])}return n}var ku=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function nh(e){ku(e)}function lh(e){console.error(e)}function ah(e){ku(e)}function Gu(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function ih(e,t,n){try{var l=e.onCaughtError;l(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(u){setTimeout(function(){throw u})}}function ic(e,t,n){return n=Ln(n),n.tag=3,n.payload={element:null},n.callback=function(){Gu(e,t)},n}function uh(e){return e=Ln(e),e.tag=3,e}function rh(e,t,n,l){var u=n.type.getDerivedStateFromError;if(typeof u=="function"){var c=l.value;e.payload=function(){return u(c)},e.callback=function(){ih(t,n,l)}}var d=n.stateNode;d!==null&&typeof d.componentDidCatch=="function"&&(e.callback=function(){ih(t,n,l),typeof u!="function"&&(Fn===null?Fn=new Set([this]):Fn.add(this));var y=l.stack;this.componentDidCatch(l.value,{componentStack:y!==null?y:""})})}function mv(e,t,n,l,u){if(n.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=n.alternate,t!==null&&ii(t,n,u,!0),n=Ht.current,n!==null){switch(n.tag){case 13:return en===null?Cc():n.alternate===null&&Le===0&&(Le=3),n.flags&=-257,n.flags|=65536,n.lanes=u,l===Us?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([l]):t.add(l),Dc(e,l,u)),!1;case 22:return n.flags|=65536,l===Us?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([l]):n.add(l)),Dc(e,l,u)),!1}throw Error(s(435,n.tag))}return Dc(e,l,u),Cc(),!1}if(Ae)return t=Ht.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=u,l!==Os&&(e=Error(s(422),{cause:l}),ai(zt(e,n)))):(l!==Os&&(t=Error(s(423),{cause:l}),ai(zt(t,n))),e=e.current.alternate,e.flags|=65536,u&=-u,e.lanes|=u,l=zt(l,n),u=ic(e.stateNode,l,u),qs(e,u),Le!==4&&(Le=2)),!1;var c=Error(s(520),{cause:l});if(c=zt(c,n),Ti===null?Ti=[c]:Ti.push(c),Le!==4&&(Le=2),t===null)return!0;l=zt(l,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=u&-u,n.lanes|=e,e=ic(n.stateNode,l,e),qs(n,e),!1;case 1:if(t=n.type,c=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(Fn===null||!Fn.has(c))))return n.flags|=65536,u&=-u,n.lanes|=u,u=uh(u),rh(u,e,n,l),qs(n,u),!1}n=n.return}while(n!==null);return!1}var sh=Error(s(461)),We=!1;function et(e,t,n,l){t.child=e===null?Pd(t,null,n,l):ca(t,e.child,n,l)}function ch(e,t,n,l,u){n=n.render;var c=t.ref;if("ref"in l){var d={};for(var y in l)y!=="ref"&&(d[y]=l[y])}else d=l;return El(t),l=Ys(e,t,n,d,c,u),y=Qs(),e!==null&&!We?(Vs(e,t,u),bn(e,t,u)):(Ae&&y&&Ts(t),t.flags|=1,et(e,t,l,u),t.child)}function oh(e,t,n,l,u){if(e===null){var c=n.type;return typeof c=="function"&&!Es(c)&&c.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=c,fh(e,t,c,l,u)):(e=Su(n.type,null,l,t,t.mode,u),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,!hc(e,u)){var d=c.memoizedProps;if(n=n.compare,n=n!==null?n:ei,n(d,l)&&e.ref===t.ref)return bn(e,t,u)}return t.flags|=1,e=dn(c,l),e.ref=t.ref,e.return=t,t.child=e}function fh(e,t,n,l,u){if(e!==null){var c=e.memoizedProps;if(ei(c,l)&&e.ref===t.ref)if(We=!1,t.pendingProps=l=c,hc(e,u))(e.flags&131072)!==0&&(We=!0);else return t.lanes=e.lanes,bn(e,t,u)}return uc(e,t,n,l,u)}function dh(e,t,n){var l=t.pendingProps,u=l.children,c=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=c!==null?c.baseLanes|n:n,e!==null){for(u=t.child=e.child,c=0;u!==null;)c=c|u.lanes|u.childLanes,u=u.sibling;t.childLanes=c&~l}else t.childLanes=0,t.child=null;return hh(e,t,l,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Tu(t,c!==null?c.cachePool:null),c!==null?fd(t,c):Ls(),Id(t);else return t.lanes=t.childLanes=536870912,hh(e,t,c!==null?c.baseLanes|n:n,n)}else c!==null?(Tu(t,c.cachePool),fd(t,c),Qn(),t.memoizedState=null):(e!==null&&Tu(t,null),Ls(),Qn());return et(e,t,u,n),t.child}function hh(e,t,n,l){var u=_s();return u=u===null?null:{parent:Ke._currentValue,pool:u},t.memoizedState={baseLanes:n,cachePool:u},e!==null&&Tu(t,null),Ls(),Id(t),e!==null&&ii(e,t,l,!0),null}function Yu(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(s(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function uc(e,t,n,l,u){return El(t),n=Ys(e,t,n,l,void 0,u),l=Qs(),e!==null&&!We?(Vs(e,t,u),bn(e,t,u)):(Ae&&l&&Ts(t),t.flags|=1,et(e,t,n,u),t.child)}function mh(e,t,n,l,u,c){return El(t),t.updateQueue=null,n=hd(t,l,n,u),dd(e),l=Qs(),e!==null&&!We?(Vs(e,t,c),bn(e,t,c)):(Ae&&l&&Ts(t),t.flags|=1,et(e,t,n,c),t.child)}function yh(e,t,n,l,u){if(El(t),t.stateNode===null){var c=Il,d=n.contextType;typeof d=="object"&&d!==null&&(c=at(d)),c=new n(l,c),t.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=ac,t.stateNode=c,c._reactInternals=t,c=t.stateNode,c.props=l,c.state=t.memoizedState,c.refs={},js(t),d=n.contextType,c.context=typeof d=="object"&&d!==null?at(d):Il,c.state=t.memoizedState,d=n.getDerivedStateFromProps,typeof d=="function"&&(lc(t,n,d,l),c.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(d=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),d!==c.state&&ac.enqueueReplaceState(c,c.state,null),di(t,l,c,u),fi(),c.state=t.memoizedState),typeof c.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){c=t.stateNode;var y=t.memoizedProps,b=Tl(n,y);c.props=b;var N=c.context,q=n.contextType;d=Il,typeof q=="object"&&q!==null&&(d=at(q));var G=n.getDerivedStateFromProps;q=typeof G=="function"||typeof c.getSnapshotBeforeUpdate=="function",y=t.pendingProps!==y,q||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(y||N!==d)&&th(t,c,l,d),Bn=!1;var D=t.memoizedState;c.state=D,di(t,l,c,u),fi(),N=t.memoizedState,y||D!==N||Bn?(typeof G=="function"&&(lc(t,n,G,l),N=t.memoizedState),(b=Bn||eh(t,n,b,l,D,N,d))?(q||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(t.flags|=4194308)):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=N),c.props=l,c.state=N,c.context=d,l=b):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{c=t.stateNode,Hs(e,t),d=t.memoizedProps,q=Tl(n,d),c.props=q,G=t.pendingProps,D=c.context,N=n.contextType,b=Il,typeof N=="object"&&N!==null&&(b=at(N)),y=n.getDerivedStateFromProps,(N=typeof y=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(d!==G||D!==b)&&th(t,c,l,b),Bn=!1,D=t.memoizedState,c.state=D,di(t,l,c,u),fi();var z=t.memoizedState;d!==G||D!==z||Bn||e!==null&&e.dependencies!==null&&Au(e.dependencies)?(typeof y=="function"&&(lc(t,n,y,l),z=t.memoizedState),(q=Bn||eh(t,n,q,l,D,z,b)||e!==null&&e.dependencies!==null&&Au(e.dependencies))?(N||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(l,z,b),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(l,z,b)),typeof c.componentDidUpdate=="function"&&(t.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof c.componentDidUpdate!="function"||d===e.memoizedProps&&D===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||d===e.memoizedProps&&D===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=z),c.props=l,c.state=z,c.context=b,l=q):(typeof c.componentDidUpdate!="function"||d===e.memoizedProps&&D===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||d===e.memoizedProps&&D===e.memoizedState||(t.flags|=1024),l=!1)}return c=l,Yu(e,t),l=(t.flags&128)!==0,c||l?(c=t.stateNode,n=l&&typeof n.getDerivedStateFromError!="function"?null:c.render(),t.flags|=1,e!==null&&l?(t.child=ca(t,e.child,null,u),t.child=ca(t,null,n,u)):et(e,t,n,u),t.memoizedState=c.state,e=t.child):e=bn(e,t,u),e}function ph(e,t,n,l){return li(),t.flags|=256,et(e,t,n,l),t.child}var rc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function sc(e){return{baseLanes:e,cachePool:ld()}}function cc(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=qt),e}function gh(e,t,n){var l=t.pendingProps,u=!1,c=(t.flags&128)!==0,d;if((d=c)||(d=e!==null&&e.memoizedState===null?!1:(Je.current&2)!==0),d&&(u=!0,t.flags&=-129),d=(t.flags&32)!==0,t.flags&=-33,e===null){if(Ae){if(u?Yn(t):Qn(),Ae){var y=Be,b;if(b=y){e:{for(b=y,y=It;b.nodeType!==8;){if(!y){y=null;break e}if(b=Qt(b.nextSibling),b===null){y=null;break e}}y=b}y!==null?(t.memoizedState={dehydrated:y,treeContext:gl!==null?{id:hn,overflow:mn}:null,retryLane:536870912,hydrationErrors:null},b=Rt(18,null,null,0),b.stateNode=y,b.return=t,t.child=b,ot=t,Be=null,b=!0):b=!1}b||Sl(t)}if(y=t.memoizedState,y!==null&&(y=y.dehydrated,y!==null))return Zc(y)?t.lanes=32:t.lanes=536870912,null;vn(t)}return y=l.children,l=l.fallback,u?(Qn(),u=t.mode,y=Qu({mode:"hidden",children:y},u),l=pl(l,u,n,null),y.return=t,l.return=t,y.sibling=l,t.child=y,u=t.child,u.memoizedState=sc(n),u.childLanes=cc(e,d,n),t.memoizedState=rc,l):(Yn(t),oc(t,y))}if(b=e.memoizedState,b!==null&&(y=b.dehydrated,y!==null)){if(c)t.flags&256?(Yn(t),t.flags&=-257,t=fc(e,t,n)):t.memoizedState!==null?(Qn(),t.child=e.child,t.flags|=128,t=null):(Qn(),u=l.fallback,y=t.mode,l=Qu({mode:"visible",children:l.children},y),u=pl(u,y,n,null),u.flags|=2,l.return=t,u.return=t,l.sibling=u,t.child=l,ca(t,e.child,null,n),l=t.child,l.memoizedState=sc(n),l.childLanes=cc(e,d,n),t.memoizedState=rc,t=u);else if(Yn(t),Zc(y)){if(d=y.nextSibling&&y.nextSibling.dataset,d)var N=d.dgst;d=N,l=Error(s(419)),l.stack="",l.digest=d,ai({value:l,source:null,stack:null}),t=fc(e,t,n)}else if(We||ii(e,t,n,!1),d=(n&e.childLanes)!==0,We||d){if(d=ze,d!==null&&(l=n&-n,l=(l&42)!==0?1:Kr(l),l=(l&(d.suspendedLanes|n))!==0?0:l,l!==0&&l!==b.retryLane))throw b.retryLane=l,Pl(e,l),Ct(d,e,l),sh;y.data==="$?"||Cc(),t=fc(e,t,n)}else y.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=b.treeContext,Be=Qt(y.nextSibling),ot=t,Ae=!0,bl=null,It=!1,e!==null&&(Ut[jt++]=hn,Ut[jt++]=mn,Ut[jt++]=gl,hn=e.id,mn=e.overflow,gl=t),t=oc(t,l.children),t.flags|=4096);return t}return u?(Qn(),u=l.fallback,y=t.mode,b=e.child,N=b.sibling,l=dn(b,{mode:"hidden",children:l.children}),l.subtreeFlags=b.subtreeFlags&65011712,N!==null?u=dn(N,u):(u=pl(u,y,n,null),u.flags|=2),u.return=t,l.return=t,l.sibling=u,t.child=l,l=u,u=t.child,y=e.child.memoizedState,y===null?y=sc(n):(b=y.cachePool,b!==null?(N=Ke._currentValue,b=b.parent!==N?{parent:N,pool:N}:b):b=ld(),y={baseLanes:y.baseLanes|n,cachePool:b}),u.memoizedState=y,u.childLanes=cc(e,d,n),t.memoizedState=rc,l):(Yn(t),n=e.child,e=n.sibling,n=dn(n,{mode:"visible",children:l.children}),n.return=t,n.sibling=null,e!==null&&(d=t.deletions,d===null?(t.deletions=[e],t.flags|=16):d.push(e)),t.child=n,t.memoizedState=null,n)}function oc(e,t){return t=Qu({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Qu(e,t){return e=Rt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function fc(e,t,n){return ca(t,e.child,null,n),e=oc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function vh(e,t,n){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),Cs(e.return,t,n)}function dc(e,t,n,l,u){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:u}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=l,c.tail=n,c.tailMode=u)}function bh(e,t,n){var l=t.pendingProps,u=l.revealOrder,c=l.tail;if(et(e,t,l.children,n),l=Je.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&vh(e,n,t);else if(e.tag===19)vh(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(X(Je,l),u){case"forwards":for(n=t.child,u=null;n!==null;)e=n.alternate,e!==null&&Lu(e)===null&&(u=n),n=n.sibling;n=u,n===null?(u=t.child,t.child=null):(u=n.sibling,n.sibling=null),dc(t,!1,u,n,c);break;case"backwards":for(n=null,u=t.child,t.child=null;u!==null;){if(e=u.alternate,e!==null&&Lu(e)===null){t.child=u;break}e=u.sibling,u.sibling=n,n=u,u=e}dc(t,!0,n,null,c);break;case"together":dc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function bn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Jn|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(ii(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(s(153));if(t.child!==null){for(e=t.child,n=dn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=dn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function hc(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Au(e)))}function yv(e,t,n){switch(t.tag){case 3:Me(t,t.stateNode.containerInfo),qn(t,Ke,e.memoizedState.cache),li();break;case 27:case 5:Dn(t);break;case 4:Me(t,t.stateNode.containerInfo);break;case 10:qn(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(Yn(t),t.flags|=128,null):(n&t.child.childLanes)!==0?gh(e,t,n):(Yn(t),e=bn(e,t,n),e!==null?e.sibling:null);Yn(t);break;case 19:var u=(e.flags&128)!==0;if(l=(n&t.childLanes)!==0,l||(ii(e,t,n,!1),l=(n&t.childLanes)!==0),u){if(l)return bh(e,t,n);t.flags|=128}if(u=t.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),X(Je,Je.current),l)break;return null;case 22:case 23:return t.lanes=0,dh(e,t,n);case 24:qn(t,Ke,e.memoizedState.cache)}return bn(e,t,n)}function Sh(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)We=!0;else{if(!hc(e,n)&&(t.flags&128)===0)return We=!1,yv(e,t,n);We=(e.flags&131072)!==0}else We=!1,Ae&&(t.flags&1048576)!==0&&$f(t,Eu,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,u=l._init;if(l=u(l._payload),t.type=l,typeof l=="function")Es(l)?(e=Tl(l,e),t.tag=1,t=yh(null,t,l,e,n)):(t.tag=0,t=uc(null,t,l,e,n));else{if(l!=null){if(u=l.$$typeof,u===P){t.tag=11,t=ch(null,t,l,e,n);break e}else if(u===fe){t.tag=14,t=oh(null,t,l,e,n);break e}}throw t=kt(l)||l,Error(s(306,t,""))}}return t;case 0:return uc(e,t,t.type,t.pendingProps,n);case 1:return l=t.type,u=Tl(l,t.pendingProps),yh(e,t,l,u,n);case 3:e:{if(Me(t,t.stateNode.containerInfo),e===null)throw Error(s(387));l=t.pendingProps;var c=t.memoizedState;u=c.element,Hs(e,t),di(t,l,null,n);var d=t.memoizedState;if(l=d.cache,qn(t,Ke,l),l!==c.cache&&Ms(t,[Ke],n,!0),fi(),l=d.element,c.isDehydrated)if(c={element:l,isDehydrated:!1,cache:d.cache},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){t=ph(e,t,l,n);break e}else if(l!==u){u=zt(Error(s(424)),t),ai(u),t=ph(e,t,l,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Be=Qt(e.firstChild),ot=t,Ae=!0,bl=null,It=!0,n=Pd(t,null,l,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(li(),l===u){t=bn(e,t,n);break e}et(e,t,l,n)}t=t.child}return t;case 26:return Yu(e,t),e===null?(n=Rm(t.type,null,t.pendingProps,null))?t.memoizedState=n:Ae||(n=t.type,e=t.pendingProps,l=lr(se.current).createElement(n),l[lt]=t,l[ht]=e,nt(l,n,e),$e(l),t.stateNode=l):t.memoizedState=Rm(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Dn(t),e===null&&Ae&&(l=t.stateNode=xm(t.type,t.pendingProps,se.current),ot=t,It=!0,u=Be,Pn(t.type)?(Kc=u,Be=Qt(l.firstChild)):Be=u),et(e,t,t.pendingProps.children,n),Yu(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Ae&&((u=l=Be)&&(l=Qv(l,t.type,t.pendingProps,It),l!==null?(t.stateNode=l,ot=t,Be=Qt(l.firstChild),It=!1,u=!0):u=!1),u||Sl(t)),Dn(t),u=t.type,c=t.pendingProps,d=e!==null?e.memoizedProps:null,l=c.children,Qc(u,c)?l=null:d!==null&&Qc(u,d)&&(t.flags|=32),t.memoizedState!==null&&(u=Ys(e,t,rv,null,null,n),Ui._currentValue=u),Yu(e,t),et(e,t,l,n),t.child;case 6:return e===null&&Ae&&((e=n=Be)&&(n=Vv(n,t.pendingProps,It),n!==null?(t.stateNode=n,ot=t,Be=null,e=!0):e=!1),e||Sl(t)),null;case 13:return gh(e,t,n);case 4:return Me(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=ca(t,null,l,n):et(e,t,l,n),t.child;case 11:return ch(e,t,t.type,t.pendingProps,n);case 7:return et(e,t,t.pendingProps,n),t.child;case 8:return et(e,t,t.pendingProps.children,n),t.child;case 12:return et(e,t,t.pendingProps.children,n),t.child;case 10:return l=t.pendingProps,qn(t,t.type,l.value),et(e,t,l.children,n),t.child;case 9:return u=t.type._context,l=t.pendingProps.children,El(t),u=at(u),l=l(u),t.flags|=1,et(e,t,l,n),t.child;case 14:return oh(e,t,t.type,t.pendingProps,n);case 15:return fh(e,t,t.type,t.pendingProps,n);case 19:return bh(e,t,n);case 31:return l=t.pendingProps,n=t.mode,l={mode:l.mode,children:l.children},e===null?(n=Qu(l,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=dn(e.child,l),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return dh(e,t,n);case 24:return El(t),l=at(Ke),e===null?(u=_s(),u===null&&(u=ze,c=Ds(),u.pooledCache=c,c.refCount++,c!==null&&(u.pooledCacheLanes|=n),u=c),t.memoizedState={parent:l,cache:u},js(t),qn(t,Ke,u)):((e.lanes&n)!==0&&(Hs(e,t),di(t,null,null,n),fi()),u=e.memoizedState,c=t.memoizedState,u.parent!==l?(u={parent:l,cache:l},t.memoizedState=u,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=u),qn(t,Ke,l)):(l=c.cache,qn(t,Ke,l),l!==u.cache&&Ms(t,[Ke],n,!0))),et(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(s(156,t.tag))}function Sn(e){e.flags|=4}function xh(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Cm(t)){if(t=Ht.current,t!==null&&((be&4194048)===be?en!==null:(be&62914560)!==be&&(be&536870912)===0||t!==en))throw ci=Us,ad;e.flags|=8192}}function Vu(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Po():536870912,e.lanes|=t,ha|=t)}function bi(e,t){if(!Ae)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function qe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,l=0;if(t)for(var u=e.child;u!==null;)n|=u.lanes|u.childLanes,l|=u.subtreeFlags&65011712,l|=u.flags&65011712,u.return=e,u=u.sibling;else for(u=e.child;u!==null;)n|=u.lanes|u.childLanes,l|=u.subtreeFlags,l|=u.flags,u.return=e,u=u.sibling;return e.subtreeFlags|=l,e.childLanes=n,t}function pv(e,t,n){var l=t.pendingProps;switch(ws(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return qe(t),null;case 1:return qe(t),null;case 3:return n=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),pn(Ke),St(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(ni(t)?Sn(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,If())),qe(t),null;case 26:return n=t.memoizedState,e===null?(Sn(t),n!==null?(qe(t),xh(t,n)):(qe(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Sn(t),qe(t),xh(t,n)):(qe(t),t.flags&=-16777217):(e.memoizedProps!==l&&Sn(t),qe(t),t.flags&=-16777217),null;case 27:zn(t),n=se.current;var u=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&Sn(t);else{if(!l){if(t.stateNode===null)throw Error(s(166));return qe(t),null}e=W.current,ni(t)?Wf(t):(e=xm(u,l,n),t.stateNode=e,Sn(t))}return qe(t),null;case 5:if(zn(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&Sn(t);else{if(!l){if(t.stateNode===null)throw Error(s(166));return qe(t),null}if(e=W.current,ni(t))Wf(t);else{switch(u=lr(se.current),e){case 1:e=u.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=u.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=u.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=u.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=u.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?u.createElement("select",{is:l.is}):u.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?u.createElement(n,{is:l.is}):u.createElement(n)}}e[lt]=t,e[ht]=l;e:for(u=t.child;u!==null;){if(u.tag===5||u.tag===6)e.appendChild(u.stateNode);else if(u.tag!==4&&u.tag!==27&&u.child!==null){u.child.return=u,u=u.child;continue}if(u===t)break e;for(;u.sibling===null;){if(u.return===null||u.return===t)break e;u=u.return}u.sibling.return=u.return,u=u.sibling}t.stateNode=e;e:switch(nt(e,n,l),n){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Sn(t)}}return qe(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&Sn(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(s(166));if(e=se.current,ni(t)){if(e=t.stateNode,n=t.memoizedProps,l=null,u=ot,u!==null)switch(u.tag){case 27:case 5:l=u.memoizedProps}e[lt]=t,e=!!(e.nodeValue===n||l!==null&&l.suppressHydrationWarning===!0||mm(e.nodeValue,n)),e||Sl(t)}else e=lr(e).createTextNode(l),e[lt]=t,t.stateNode=e}return qe(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(u=ni(t),l!==null&&l.dehydrated!==null){if(e===null){if(!u)throw Error(s(318));if(u=t.memoizedState,u=u!==null?u.dehydrated:null,!u)throw Error(s(317));u[lt]=t}else li(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;qe(t),u=!1}else u=If(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=u),u=!0;if(!u)return t.flags&256?(vn(t),t):(vn(t),null)}if(vn(t),(t.flags&128)!==0)return t.lanes=n,t;if(n=l!==null,e=e!==null&&e.memoizedState!==null,n){l=t.child,u=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(u=l.alternate.memoizedState.cachePool.pool);var c=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(c=l.memoizedState.cachePool.pool),c!==u&&(l.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),Vu(t,t.updateQueue),qe(t),null;case 4:return St(),e===null&&Bc(t.stateNode.containerInfo),qe(t),null;case 10:return pn(t.type),qe(t),null;case 19:if($(Je),u=t.memoizedState,u===null)return qe(t),null;if(l=(t.flags&128)!==0,c=u.rendering,c===null)if(l)bi(u,!1);else{if(Le!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(c=Lu(e),c!==null){for(t.flags|=128,bi(u,!1),e=c.updateQueue,t.updateQueue=e,Vu(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)Ff(n,e),n=n.sibling;return X(Je,Je.current&1|2),t.child}e=e.sibling}u.tail!==null&&Pt()>Ku&&(t.flags|=128,l=!0,bi(u,!1),t.lanes=4194304)}else{if(!l)if(e=Lu(c),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,Vu(t,e),bi(u,!0),u.tail===null&&u.tailMode==="hidden"&&!c.alternate&&!Ae)return qe(t),null}else 2*Pt()-u.renderingStartTime>Ku&&n!==536870912&&(t.flags|=128,l=!0,bi(u,!1),t.lanes=4194304);u.isBackwards?(c.sibling=t.child,t.child=c):(e=u.last,e!==null?e.sibling=c:t.child=c,u.last=c)}return u.tail!==null?(t=u.tail,u.rendering=t,u.tail=t.sibling,u.renderingStartTime=Pt(),t.sibling=null,e=Je.current,X(Je,l?e&1|2:e&1),t):(qe(t),null);case 22:case 23:return vn(t),ks(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(n&536870912)!==0&&(t.flags&128)===0&&(qe(t),t.subtreeFlags&6&&(t.flags|=8192)):qe(t),n=t.updateQueue,n!==null&&Vu(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==n&&(t.flags|=2048),e!==null&&$(Al),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),pn(Ke),qe(t),null;case 25:return null;case 30:return null}throw Error(s(156,t.tag))}function gv(e,t){switch(ws(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return pn(Ke),St(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return zn(t),null;case 13:if(vn(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(s(340));li()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return $(Je),null;case 4:return St(),null;case 10:return pn(t.type),null;case 22:case 23:return vn(t),ks(),e!==null&&$(Al),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return pn(Ke),null;case 25:return null;default:return null}}function Eh(e,t){switch(ws(t),t.tag){case 3:pn(Ke),St();break;case 26:case 27:case 5:zn(t);break;case 4:St();break;case 13:vn(t);break;case 19:$(Je);break;case 10:pn(t.type);break;case 22:case 23:vn(t),ks(),e!==null&&$(Al);break;case 24:pn(Ke)}}function Si(e,t){try{var n=t.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var u=l.next;n=u;do{if((n.tag&e)===e){l=void 0;var c=n.create,d=n.inst;l=c(),d.destroy=l}n=n.next}while(n!==u)}}catch(y){De(t,t.return,y)}}function Vn(e,t,n){try{var l=t.updateQueue,u=l!==null?l.lastEffect:null;if(u!==null){var c=u.next;l=c;do{if((l.tag&e)===e){var d=l.inst,y=d.destroy;if(y!==void 0){d.destroy=void 0,u=t;var b=n,N=y;try{N()}catch(q){De(u,b,q)}}}l=l.next}while(l!==c)}}catch(q){De(t,t.return,q)}}function Ah(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{od(t,n)}catch(l){De(e,e.return,l)}}}function Rh(e,t,n){n.props=Tl(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(l){De(e,t,l)}}function xi(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof n=="function"?e.refCleanup=n(l):n.current=l}}catch(u){De(e,t,u)}}function tn(e,t){var n=e.ref,l=e.refCleanup;if(n!==null)if(typeof l=="function")try{l()}catch(u){De(e,t,u)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(u){De(e,t,u)}else n.current=null}function Th(e){var t=e.type,n=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&l.focus();break e;case"img":n.src?l.src=n.src:n.srcSet&&(l.srcset=n.srcSet)}}catch(u){De(e,e.return,u)}}function mc(e,t,n){try{var l=e.stateNode;Bv(l,e.type,n,t),l[ht]=t}catch(u){De(e,e.return,u)}}function wh(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Pn(e.type)||e.tag===4}function yc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||wh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Pn(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function pc(e,t,n){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=nr));else if(l!==4&&(l===27&&Pn(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(pc(e,t,n),e=e.sibling;e!==null;)pc(e,t,n),e=e.sibling}function Xu(e,t,n){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(l!==4&&(l===27&&Pn(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(Xu(e,t,n),e=e.sibling;e!==null;)Xu(e,t,n),e=e.sibling}function Oh(e){var t=e.stateNode,n=e.memoizedProps;try{for(var l=e.type,u=t.attributes;u.length;)t.removeAttributeNode(u[0]);nt(t,l,n),t[lt]=e,t[ht]=n}catch(c){De(e,e.return,c)}}var xn=!1,Qe=!1,gc=!1,Nh=typeof WeakSet=="function"?WeakSet:Set,Pe=null;function vv(e,t){if(e=e.containerInfo,Gc=cr,e=Lf(e),ys(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var u=l.anchorOffset,c=l.focusNode;l=l.focusOffset;try{n.nodeType,c.nodeType}catch{n=null;break e}var d=0,y=-1,b=-1,N=0,q=0,G=e,D=null;t:for(;;){for(var z;G!==n||u!==0&&G.nodeType!==3||(y=d+u),G!==c||l!==0&&G.nodeType!==3||(b=d+l),G.nodeType===3&&(d+=G.nodeValue.length),(z=G.firstChild)!==null;)D=G,G=z;for(;;){if(G===e)break t;if(D===n&&++N===u&&(y=d),D===c&&++q===l&&(b=d),(z=G.nextSibling)!==null)break;G=D,D=G.parentNode}G=z}n=y===-1||b===-1?null:{start:y,end:b}}else n=null}n=n||{start:0,end:0}}else n=null;for(Yc={focusedElem:e,selectionRange:n},cr=!1,Pe=t;Pe!==null;)if(t=Pe,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Pe=e;else for(;Pe!==null;){switch(t=Pe,c=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&c!==null){e=void 0,n=t,u=c.memoizedProps,c=c.memoizedState,l=n.stateNode;try{var re=Tl(n.type,u,n.elementType===n.type);e=l.getSnapshotBeforeUpdate(re,c),l.__reactInternalSnapshotBeforeUpdate=e}catch(ae){De(n,n.return,ae)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)Xc(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Xc(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(s(163))}if(e=t.sibling,e!==null){e.return=t.return,Pe=e;break}Pe=t.return}}function Ch(e,t,n){var l=n.flags;switch(n.tag){case 0:case 11:case 15:Xn(e,n),l&4&&Si(5,n);break;case 1:if(Xn(e,n),l&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(d){De(n,n.return,d)}else{var u=Tl(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(u,t,e.__reactInternalSnapshotBeforeUpdate)}catch(d){De(n,n.return,d)}}l&64&&Ah(n),l&512&&xi(n,n.return);break;case 3:if(Xn(e,n),l&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{od(e,t)}catch(d){De(n,n.return,d)}}break;case 27:t===null&&l&4&&Oh(n);case 26:case 5:Xn(e,n),t===null&&l&4&&Th(n),l&512&&xi(n,n.return);break;case 12:Xn(e,n);break;case 13:Xn(e,n),l&4&&zh(e,n),l&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=Ov.bind(null,n),Xv(e,n))));break;case 22:if(l=n.memoizedState!==null||xn,!l){t=t!==null&&t.memoizedState!==null||Qe,u=xn;var c=Qe;xn=l,(Qe=t)&&!c?Zn(e,n,(n.subtreeFlags&8772)!==0):Xn(e,n),xn=u,Qe=c}break;case 30:break;default:Xn(e,n)}}function Mh(e){var t=e.alternate;t!==null&&(e.alternate=null,Mh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&$r(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var _e=null,pt=!1;function En(e,t,n){for(n=n.child;n!==null;)Dh(e,t,n),n=n.sibling}function Dh(e,t,n){if(xt&&typeof xt.onCommitFiberUnmount=="function")try{xt.onCommitFiberUnmount(Ya,n)}catch{}switch(n.tag){case 26:Qe||tn(n,t),En(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Qe||tn(n,t);var l=_e,u=pt;Pn(n.type)&&(_e=n.stateNode,pt=!1),En(e,t,n),Mi(n.stateNode),_e=l,pt=u;break;case 5:Qe||tn(n,t);case 6:if(l=_e,u=pt,_e=null,En(e,t,n),_e=l,pt=u,_e!==null)if(pt)try{(_e.nodeType===9?_e.body:_e.nodeName==="HTML"?_e.ownerDocument.body:_e).removeChild(n.stateNode)}catch(c){De(n,t,c)}else try{_e.removeChild(n.stateNode)}catch(c){De(n,t,c)}break;case 18:_e!==null&&(pt?(e=_e,bm(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),Bi(e)):bm(_e,n.stateNode));break;case 4:l=_e,u=pt,_e=n.stateNode.containerInfo,pt=!0,En(e,t,n),_e=l,pt=u;break;case 0:case 11:case 14:case 15:Qe||Vn(2,n,t),Qe||Vn(4,n,t),En(e,t,n);break;case 1:Qe||(tn(n,t),l=n.stateNode,typeof l.componentWillUnmount=="function"&&Rh(n,t,l)),En(e,t,n);break;case 21:En(e,t,n);break;case 22:Qe=(l=Qe)||n.memoizedState!==null,En(e,t,n),Qe=l;break;default:En(e,t,n)}}function zh(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Bi(e)}catch(n){De(t,t.return,n)}}function bv(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Nh),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Nh),t;default:throw Error(s(435,e.tag))}}function vc(e,t){var n=bv(e);t.forEach(function(l){var u=Nv.bind(null,e,l);n.has(l)||(n.add(l),l.then(u,u))})}function Tt(e,t){var n=t.deletions;if(n!==null)for(var l=0;l<n.length;l++){var u=n[l],c=e,d=t,y=d;e:for(;y!==null;){switch(y.tag){case 27:if(Pn(y.type)){_e=y.stateNode,pt=!1;break e}break;case 5:_e=y.stateNode,pt=!1;break e;case 3:case 4:_e=y.stateNode.containerInfo,pt=!0;break e}y=y.return}if(_e===null)throw Error(s(160));Dh(c,d,u),_e=null,pt=!1,c=u.alternate,c!==null&&(c.return=null),u.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)_h(t,e),t=t.sibling}var Yt=null;function _h(e,t){var n=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Tt(t,e),wt(e),l&4&&(Vn(3,e,e.return),Si(3,e),Vn(5,e,e.return));break;case 1:Tt(t,e),wt(e),l&512&&(Qe||n===null||tn(n,n.return)),l&64&&xn&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?l:n.concat(l))));break;case 26:var u=Yt;if(Tt(t,e),wt(e),l&512&&(Qe||n===null||tn(n,n.return)),l&4){var c=n!==null?n.memoizedState:null;if(l=e.memoizedState,n===null)if(l===null)if(e.stateNode===null){e:{l=e.type,n=e.memoizedProps,u=u.ownerDocument||u;t:switch(l){case"title":c=u.getElementsByTagName("title")[0],(!c||c[Xa]||c[lt]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=u.createElement(l),u.head.insertBefore(c,u.querySelector("head > title"))),nt(c,l,n),c[lt]=e,$e(c),l=c;break e;case"link":var d=Om("link","href",u).get(l+(n.href||""));if(d){for(var y=0;y<d.length;y++)if(c=d[y],c.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&c.getAttribute("rel")===(n.rel==null?null:n.rel)&&c.getAttribute("title")===(n.title==null?null:n.title)&&c.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){d.splice(y,1);break t}}c=u.createElement(l),nt(c,l,n),u.head.appendChild(c);break;case"meta":if(d=Om("meta","content",u).get(l+(n.content||""))){for(y=0;y<d.length;y++)if(c=d[y],c.getAttribute("content")===(n.content==null?null:""+n.content)&&c.getAttribute("name")===(n.name==null?null:n.name)&&c.getAttribute("property")===(n.property==null?null:n.property)&&c.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&c.getAttribute("charset")===(n.charSet==null?null:n.charSet)){d.splice(y,1);break t}}c=u.createElement(l),nt(c,l,n),u.head.appendChild(c);break;default:throw Error(s(468,l))}c[lt]=e,$e(c),l=c}e.stateNode=l}else Nm(u,e.type,e.stateNode);else e.stateNode=wm(u,l,e.memoizedProps);else c!==l?(c===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):c.count--,l===null?Nm(u,e.type,e.stateNode):wm(u,l,e.memoizedProps)):l===null&&e.stateNode!==null&&mc(e,e.memoizedProps,n.memoizedProps)}break;case 27:Tt(t,e),wt(e),l&512&&(Qe||n===null||tn(n,n.return)),n!==null&&l&4&&mc(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Tt(t,e),wt(e),l&512&&(Qe||n===null||tn(n,n.return)),e.flags&32){u=e.stateNode;try{Xl(u,"")}catch(z){De(e,e.return,z)}}l&4&&e.stateNode!=null&&(u=e.memoizedProps,mc(e,u,n!==null?n.memoizedProps:u)),l&1024&&(gc=!0);break;case 6:if(Tt(t,e),wt(e),l&4){if(e.stateNode===null)throw Error(s(162));l=e.memoizedProps,n=e.stateNode;try{n.nodeValue=l}catch(z){De(e,e.return,z)}}break;case 3:if(ur=null,u=Yt,Yt=ar(t.containerInfo),Tt(t,e),Yt=u,wt(e),l&4&&n!==null&&n.memoizedState.isDehydrated)try{Bi(t.containerInfo)}catch(z){De(e,e.return,z)}gc&&(gc=!1,Uh(e));break;case 4:l=Yt,Yt=ar(e.stateNode.containerInfo),Tt(t,e),wt(e),Yt=l;break;case 12:Tt(t,e),wt(e);break;case 13:Tt(t,e),wt(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Rc=Pt()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,vc(e,l)));break;case 22:u=e.memoizedState!==null;var b=n!==null&&n.memoizedState!==null,N=xn,q=Qe;if(xn=N||u,Qe=q||b,Tt(t,e),Qe=q,xn=N,wt(e),l&8192)e:for(t=e.stateNode,t._visibility=u?t._visibility&-2:t._visibility|1,u&&(n===null||b||xn||Qe||wl(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){b=n=t;try{if(c=b.stateNode,u)d=c.style,typeof d.setProperty=="function"?d.setProperty("display","none","important"):d.display="none";else{y=b.stateNode;var G=b.memoizedProps.style,D=G!=null&&G.hasOwnProperty("display")?G.display:null;y.style.display=D==null||typeof D=="boolean"?"":(""+D).trim()}}catch(z){De(b,b.return,z)}}}else if(t.tag===6){if(n===null){b=t;try{b.stateNode.nodeValue=u?"":b.memoizedProps}catch(z){De(b,b.return,z)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(n=l.retryQueue,n!==null&&(l.retryQueue=null,vc(e,n))));break;case 19:Tt(t,e),wt(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,vc(e,l)));break;case 30:break;case 21:break;default:Tt(t,e),wt(e)}}function wt(e){var t=e.flags;if(t&2){try{for(var n,l=e.return;l!==null;){if(wh(l)){n=l;break}l=l.return}if(n==null)throw Error(s(160));switch(n.tag){case 27:var u=n.stateNode,c=yc(e);Xu(e,c,u);break;case 5:var d=n.stateNode;n.flags&32&&(Xl(d,""),n.flags&=-33);var y=yc(e);Xu(e,y,d);break;case 3:case 4:var b=n.stateNode.containerInfo,N=yc(e);pc(e,N,b);break;default:throw Error(s(161))}}catch(q){De(e,e.return,q)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Uh(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Uh(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Xn(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Ch(e,t.alternate,t),t=t.sibling}function wl(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Vn(4,t,t.return),wl(t);break;case 1:tn(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&Rh(t,t.return,n),wl(t);break;case 27:Mi(t.stateNode);case 26:case 5:tn(t,t.return),wl(t);break;case 22:t.memoizedState===null&&wl(t);break;case 30:wl(t);break;default:wl(t)}e=e.sibling}}function Zn(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,u=e,c=t,d=c.flags;switch(c.tag){case 0:case 11:case 15:Zn(u,c,n),Si(4,c);break;case 1:if(Zn(u,c,n),l=c,u=l.stateNode,typeof u.componentDidMount=="function")try{u.componentDidMount()}catch(N){De(l,l.return,N)}if(l=c,u=l.updateQueue,u!==null){var y=l.stateNode;try{var b=u.shared.hiddenCallbacks;if(b!==null)for(u.shared.hiddenCallbacks=null,u=0;u<b.length;u++)cd(b[u],y)}catch(N){De(l,l.return,N)}}n&&d&64&&Ah(c),xi(c,c.return);break;case 27:Oh(c);case 26:case 5:Zn(u,c,n),n&&l===null&&d&4&&Th(c),xi(c,c.return);break;case 12:Zn(u,c,n);break;case 13:Zn(u,c,n),n&&d&4&&zh(u,c);break;case 22:c.memoizedState===null&&Zn(u,c,n),xi(c,c.return);break;case 30:break;default:Zn(u,c,n)}t=t.sibling}}function bc(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&ui(n))}function Sc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&ui(e))}function nn(e,t,n,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)jh(e,t,n,l),t=t.sibling}function jh(e,t,n,l){var u=t.flags;switch(t.tag){case 0:case 11:case 15:nn(e,t,n,l),u&2048&&Si(9,t);break;case 1:nn(e,t,n,l);break;case 3:nn(e,t,n,l),u&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&ui(e)));break;case 12:if(u&2048){nn(e,t,n,l),e=t.stateNode;try{var c=t.memoizedProps,d=c.id,y=c.onPostCommit;typeof y=="function"&&y(d,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(b){De(t,t.return,b)}}else nn(e,t,n,l);break;case 13:nn(e,t,n,l);break;case 23:break;case 22:c=t.stateNode,d=t.alternate,t.memoizedState!==null?c._visibility&2?nn(e,t,n,l):Ei(e,t):c._visibility&2?nn(e,t,n,l):(c._visibility|=2,oa(e,t,n,l,(t.subtreeFlags&10256)!==0)),u&2048&&bc(d,t);break;case 24:nn(e,t,n,l),u&2048&&Sc(t.alternate,t);break;default:nn(e,t,n,l)}}function oa(e,t,n,l,u){for(u=u&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var c=e,d=t,y=n,b=l,N=d.flags;switch(d.tag){case 0:case 11:case 15:oa(c,d,y,b,u),Si(8,d);break;case 23:break;case 22:var q=d.stateNode;d.memoizedState!==null?q._visibility&2?oa(c,d,y,b,u):Ei(c,d):(q._visibility|=2,oa(c,d,y,b,u)),u&&N&2048&&bc(d.alternate,d);break;case 24:oa(c,d,y,b,u),u&&N&2048&&Sc(d.alternate,d);break;default:oa(c,d,y,b,u)}t=t.sibling}}function Ei(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,l=t,u=l.flags;switch(l.tag){case 22:Ei(n,l),u&2048&&bc(l.alternate,l);break;case 24:Ei(n,l),u&2048&&Sc(l.alternate,l);break;default:Ei(n,l)}t=t.sibling}}var Ai=8192;function fa(e){if(e.subtreeFlags&Ai)for(e=e.child;e!==null;)Hh(e),e=e.sibling}function Hh(e){switch(e.tag){case 26:fa(e),e.flags&Ai&&e.memoizedState!==null&&a0(Yt,e.memoizedState,e.memoizedProps);break;case 5:fa(e);break;case 3:case 4:var t=Yt;Yt=ar(e.stateNode.containerInfo),fa(e),Yt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Ai,Ai=16777216,fa(e),Ai=t):fa(e));break;default:fa(e)}}function qh(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Ri(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var l=t[n];Pe=l,Lh(l,e)}qh(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Bh(e),e=e.sibling}function Bh(e){switch(e.tag){case 0:case 11:case 15:Ri(e),e.flags&2048&&Vn(9,e,e.return);break;case 3:Ri(e);break;case 12:Ri(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Zu(e)):Ri(e);break;default:Ri(e)}}function Zu(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var l=t[n];Pe=l,Lh(l,e)}qh(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Vn(8,t,t.return),Zu(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,Zu(t));break;default:Zu(t)}e=e.sibling}}function Lh(e,t){for(;Pe!==null;){var n=Pe;switch(n.tag){case 0:case 11:case 15:Vn(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var l=n.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:ui(n.memoizedState.cache)}if(l=n.child,l!==null)l.return=n,Pe=l;else e:for(n=e;Pe!==null;){l=Pe;var u=l.sibling,c=l.return;if(Mh(l),l===n){Pe=null;break e}if(u!==null){u.return=c,Pe=u;break e}Pe=c}}}var Sv={getCacheForType:function(e){var t=at(Ke),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},xv=typeof WeakMap=="function"?WeakMap:Map,Te=0,ze=null,ge=null,be=0,we=0,Ot=null,Kn=!1,da=!1,xc=!1,An=0,Le=0,Jn=0,Ol=0,Ec=0,qt=0,ha=0,Ti=null,gt=null,Ac=!1,Rc=0,Ku=1/0,Ju=null,Fn=null,tt=0,$n=null,ma=null,ya=0,Tc=0,wc=null,kh=null,wi=0,Oc=null;function Nt(){if((Te&2)!==0&&be!==0)return be&-be;if(j.T!==null){var e=na;return e!==0?e:Uc()}return tf()}function Gh(){qt===0&&(qt=(be&536870912)===0||Ae?Wo():536870912);var e=Ht.current;return e!==null&&(e.flags|=32),qt}function Ct(e,t,n){(e===ze&&(we===2||we===9)||e.cancelPendingCommit!==null)&&(pa(e,0),Wn(e,be,qt,!1)),Va(e,n),((Te&2)===0||e!==ze)&&(e===ze&&((Te&2)===0&&(Ol|=n),Le===4&&Wn(e,be,qt,!1)),ln(e))}function Yh(e,t,n){if((Te&6)!==0)throw Error(s(327));var l=!n&&(t&124)===0&&(t&e.expiredLanes)===0||Qa(e,t),u=l?Rv(e,t):Mc(e,t,!0),c=l;do{if(u===0){da&&!l&&Wn(e,t,0,!1);break}else{if(n=e.current.alternate,c&&!Ev(n)){u=Mc(e,t,!1),c=!1;continue}if(u===2){if(c=t,e.errorRecoveryDisabledLanes&c)var d=0;else d=e.pendingLanes&-536870913,d=d!==0?d:d&536870912?536870912:0;if(d!==0){t=d;e:{var y=e;u=Ti;var b=y.current.memoizedState.isDehydrated;if(b&&(pa(y,d).flags|=256),d=Mc(y,d,!1),d!==2){if(xc&&!b){y.errorRecoveryDisabledLanes|=c,Ol|=c,u=4;break e}c=gt,gt=u,c!==null&&(gt===null?gt=c:gt.push.apply(gt,c))}u=d}if(c=!1,u!==2)continue}}if(u===1){pa(e,0),Wn(e,t,0,!0);break}e:{switch(l=e,c=u,c){case 0:case 1:throw Error(s(345));case 4:if((t&4194048)!==t)break;case 6:Wn(l,t,qt,!Kn);break e;case 2:gt=null;break;case 3:case 5:break;default:throw Error(s(329))}if((t&62914560)===t&&(u=Rc+300-Pt(),10<u)){if(Wn(l,t,qt,!Kn),iu(l,0,!0)!==0)break e;l.timeoutHandle=gm(Qh.bind(null,l,n,gt,Ju,Ac,t,qt,Ol,ha,Kn,c,2,-0,0),u);break e}Qh(l,n,gt,Ju,Ac,t,qt,Ol,ha,Kn,c,0,-0,0)}}break}while(!0);ln(e)}function Qh(e,t,n,l,u,c,d,y,b,N,q,G,D,z){if(e.timeoutHandle=-1,G=t.subtreeFlags,(G&8192||(G&16785408)===16785408)&&(_i={stylesheets:null,count:0,unsuspend:l0},Hh(t),G=i0(),G!==null)){e.cancelPendingCommit=G($h.bind(null,e,t,c,n,l,u,d,y,b,q,1,D,z)),Wn(e,c,d,!N);return}$h(e,t,c,n,l,u,d,y,b)}function Ev(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var l=0;l<n.length;l++){var u=n[l],c=u.getSnapshot;u=u.value;try{if(!At(c(),u))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Wn(e,t,n,l){t&=~Ec,t&=~Ol,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var u=t;0<u;){var c=31-Et(u),d=1<<c;l[c]=-1,u&=~d}n!==0&&Io(e,n,t)}function Fu(){return(Te&6)===0?(Oi(0),!1):!0}function Nc(){if(ge!==null){if(we===0)var e=ge.return;else e=ge,yn=xl=null,Xs(e),sa=null,gi=0,e=ge;for(;e!==null;)Eh(e.alternate,e),e=e.return;ge=null}}function pa(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,kv(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),Nc(),ze=e,ge=n=dn(e.current,null),be=t,we=0,Ot=null,Kn=!1,da=Qa(e,t),xc=!1,ha=qt=Ec=Ol=Jn=Le=0,gt=Ti=null,Ac=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var u=31-Et(l),c=1<<u;t|=e[u],l&=~c}return An=t,gu(),n}function Vh(e,t){he=null,j.H=Hu,t===si||t===wu?(t=rd(),we=3):t===ad?(t=rd(),we=4):we=t===sh?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,Ot=t,ge===null&&(Le=1,Gu(e,zt(t,e.current)))}function Xh(){var e=j.H;return j.H=Hu,e===null?Hu:e}function Zh(){var e=j.A;return j.A=Sv,e}function Cc(){Le=4,Kn||(be&4194048)!==be&&Ht.current!==null||(da=!0),(Jn&134217727)===0&&(Ol&134217727)===0||ze===null||Wn(ze,be,qt,!1)}function Mc(e,t,n){var l=Te;Te|=2;var u=Xh(),c=Zh();(ze!==e||be!==t)&&(Ju=null,pa(e,t)),t=!1;var d=Le;e:do try{if(we!==0&&ge!==null){var y=ge,b=Ot;switch(we){case 8:Nc(),d=6;break e;case 3:case 2:case 9:case 6:Ht.current===null&&(t=!0);var N=we;if(we=0,Ot=null,ga(e,y,b,N),n&&da){d=0;break e}break;default:N=we,we=0,Ot=null,ga(e,y,b,N)}}Av(),d=Le;break}catch(q){Vh(e,q)}while(!0);return t&&e.shellSuspendCounter++,yn=xl=null,Te=l,j.H=u,j.A=c,ge===null&&(ze=null,be=0,gu()),d}function Av(){for(;ge!==null;)Kh(ge)}function Rv(e,t){var n=Te;Te|=2;var l=Xh(),u=Zh();ze!==e||be!==t?(Ju=null,Ku=Pt()+500,pa(e,t)):da=Qa(e,t);e:do try{if(we!==0&&ge!==null){t=ge;var c=Ot;t:switch(we){case 1:we=0,Ot=null,ga(e,t,c,1);break;case 2:case 9:if(id(c)){we=0,Ot=null,Jh(t);break}t=function(){we!==2&&we!==9||ze!==e||(we=7),ln(e)},c.then(t,t);break e;case 3:we=7;break e;case 4:we=5;break e;case 7:id(c)?(we=0,Ot=null,Jh(t)):(we=0,Ot=null,ga(e,t,c,7));break;case 5:var d=null;switch(ge.tag){case 26:d=ge.memoizedState;case 5:case 27:var y=ge;if(!d||Cm(d)){we=0,Ot=null;var b=y.sibling;if(b!==null)ge=b;else{var N=y.return;N!==null?(ge=N,$u(N)):ge=null}break t}}we=0,Ot=null,ga(e,t,c,5);break;case 6:we=0,Ot=null,ga(e,t,c,6);break;case 8:Nc(),Le=6;break e;default:throw Error(s(462))}}Tv();break}catch(q){Vh(e,q)}while(!0);return yn=xl=null,j.H=l,j.A=u,Te=n,ge!==null?0:(ze=null,be=0,gu(),Le)}function Tv(){for(;ge!==null&&!Kp();)Kh(ge)}function Kh(e){var t=Sh(e.alternate,e,An);e.memoizedProps=e.pendingProps,t===null?$u(e):ge=t}function Jh(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=mh(n,t,t.pendingProps,t.type,void 0,be);break;case 11:t=mh(n,t,t.pendingProps,t.type.render,t.ref,be);break;case 5:Xs(t);default:Eh(n,t),t=ge=Ff(t,An),t=Sh(n,t,An)}e.memoizedProps=e.pendingProps,t===null?$u(e):ge=t}function ga(e,t,n,l){yn=xl=null,Xs(t),sa=null,gi=0;var u=t.return;try{if(mv(e,u,t,n,be)){Le=1,Gu(e,zt(n,e.current)),ge=null;return}}catch(c){if(u!==null)throw ge=u,c;Le=1,Gu(e,zt(n,e.current)),ge=null;return}t.flags&32768?(Ae||l===1?e=!0:da||(be&536870912)!==0?e=!1:(Kn=e=!0,(l===2||l===9||l===3||l===6)&&(l=Ht.current,l!==null&&l.tag===13&&(l.flags|=16384))),Fh(t,e)):$u(t)}function $u(e){var t=e;do{if((t.flags&32768)!==0){Fh(t,Kn);return}e=t.return;var n=pv(t.alternate,t,An);if(n!==null){ge=n;return}if(t=t.sibling,t!==null){ge=t;return}ge=t=e}while(t!==null);Le===0&&(Le=5)}function Fh(e,t){do{var n=gv(e.alternate,e);if(n!==null){n.flags&=32767,ge=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){ge=e;return}ge=e=n}while(e!==null);Le=6,ge=null}function $h(e,t,n,l,u,c,d,y,b){e.cancelPendingCommit=null;do Wu();while(tt!==0);if((Te&6)!==0)throw Error(s(327));if(t!==null){if(t===e.current)throw Error(s(177));if(c=t.lanes|t.childLanes,c|=Ss,lg(e,n,c,d,y,b),e===ze&&(ge=ze=null,be=0),ma=t,$n=e,ya=n,Tc=c,wc=u,kh=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Cv(nu,function(){return tm(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=j.T,j.T=null,u=J.p,J.p=2,d=Te,Te|=4;try{vv(e,t,n)}finally{Te=d,J.p=u,j.T=l}}tt=1,Wh(),Ph(),Ih()}}function Wh(){if(tt===1){tt=0;var e=$n,t=ma,n=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||n){n=j.T,j.T=null;var l=J.p;J.p=2;var u=Te;Te|=4;try{_h(t,e);var c=Yc,d=Lf(e.containerInfo),y=c.focusedElem,b=c.selectionRange;if(d!==y&&y&&y.ownerDocument&&Bf(y.ownerDocument.documentElement,y)){if(b!==null&&ys(y)){var N=b.start,q=b.end;if(q===void 0&&(q=N),"selectionStart"in y)y.selectionStart=N,y.selectionEnd=Math.min(q,y.value.length);else{var G=y.ownerDocument||document,D=G&&G.defaultView||window;if(D.getSelection){var z=D.getSelection(),re=y.textContent.length,ae=Math.min(b.start,re),Ce=b.end===void 0?ae:Math.min(b.end,re);!z.extend&&ae>Ce&&(d=Ce,Ce=ae,ae=d);var R=qf(y,ae),x=qf(y,Ce);if(R&&x&&(z.rangeCount!==1||z.anchorNode!==R.node||z.anchorOffset!==R.offset||z.focusNode!==x.node||z.focusOffset!==x.offset)){var w=G.createRange();w.setStart(R.node,R.offset),z.removeAllRanges(),ae>Ce?(z.addRange(w),z.extend(x.node,x.offset)):(w.setEnd(x.node,x.offset),z.addRange(w))}}}}for(G=[],z=y;z=z.parentNode;)z.nodeType===1&&G.push({element:z,left:z.scrollLeft,top:z.scrollTop});for(typeof y.focus=="function"&&y.focus(),y=0;y<G.length;y++){var L=G[y];L.element.scrollLeft=L.left,L.element.scrollTop=L.top}}cr=!!Gc,Yc=Gc=null}finally{Te=u,J.p=l,j.T=n}}e.current=t,tt=2}}function Ph(){if(tt===2){tt=0;var e=$n,t=ma,n=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||n){n=j.T,j.T=null;var l=J.p;J.p=2;var u=Te;Te|=4;try{Ch(e,t.alternate,t)}finally{Te=u,J.p=l,j.T=n}}tt=3}}function Ih(){if(tt===4||tt===3){tt=0,Jp();var e=$n,t=ma,n=ya,l=kh;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?tt=5:(tt=0,ma=$n=null,em(e,e.pendingLanes));var u=e.pendingLanes;if(u===0&&(Fn=null),Jr(n),t=t.stateNode,xt&&typeof xt.onCommitFiberRoot=="function")try{xt.onCommitFiberRoot(Ya,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=j.T,u=J.p,J.p=2,j.T=null;try{for(var c=e.onRecoverableError,d=0;d<l.length;d++){var y=l[d];c(y.value,{componentStack:y.stack})}}finally{j.T=t,J.p=u}}(ya&3)!==0&&Wu(),ln(e),u=e.pendingLanes,(n&4194090)!==0&&(u&42)!==0?e===Oc?wi++:(wi=0,Oc=e):wi=0,Oi(0)}}function em(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,ui(t)))}function Wu(e){return Wh(),Ph(),Ih(),tm()}function tm(){if(tt!==5)return!1;var e=$n,t=Tc;Tc=0;var n=Jr(ya),l=j.T,u=J.p;try{J.p=32>n?32:n,j.T=null,n=wc,wc=null;var c=$n,d=ya;if(tt=0,ma=$n=null,ya=0,(Te&6)!==0)throw Error(s(331));var y=Te;if(Te|=4,Bh(c.current),jh(c,c.current,d,n),Te=y,Oi(0,!1),xt&&typeof xt.onPostCommitFiberRoot=="function")try{xt.onPostCommitFiberRoot(Ya,c)}catch{}return!0}finally{J.p=u,j.T=l,em(e,t)}}function nm(e,t,n){t=zt(n,t),t=ic(e.stateNode,t,2),e=kn(e,t,2),e!==null&&(Va(e,2),ln(e))}function De(e,t,n){if(e.tag===3)nm(e,e,n);else for(;t!==null;){if(t.tag===3){nm(t,e,n);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(Fn===null||!Fn.has(l))){e=zt(n,e),n=uh(2),l=kn(t,n,2),l!==null&&(rh(n,l,t,e),Va(l,2),ln(l));break}}t=t.return}}function Dc(e,t,n){var l=e.pingCache;if(l===null){l=e.pingCache=new xv;var u=new Set;l.set(t,u)}else u=l.get(t),u===void 0&&(u=new Set,l.set(t,u));u.has(n)||(xc=!0,u.add(n),e=wv.bind(null,e,t,n),t.then(e,e))}function wv(e,t,n){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,ze===e&&(be&n)===n&&(Le===4||Le===3&&(be&62914560)===be&&300>Pt()-Rc?(Te&2)===0&&pa(e,0):Ec|=n,ha===be&&(ha=0)),ln(e)}function lm(e,t){t===0&&(t=Po()),e=Pl(e,t),e!==null&&(Va(e,t),ln(e))}function Ov(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),lm(e,n)}function Nv(e,t){var n=0;switch(e.tag){case 13:var l=e.stateNode,u=e.memoizedState;u!==null&&(n=u.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(s(314))}l!==null&&l.delete(t),lm(e,n)}function Cv(e,t){return Vr(e,t)}var Pu=null,va=null,zc=!1,Iu=!1,_c=!1,Nl=0;function ln(e){e!==va&&e.next===null&&(va===null?Pu=va=e:va=va.next=e),Iu=!0,zc||(zc=!0,Dv())}function Oi(e,t){if(!_c&&Iu){_c=!0;do for(var n=!1,l=Pu;l!==null;){if(e!==0){var u=l.pendingLanes;if(u===0)var c=0;else{var d=l.suspendedLanes,y=l.pingedLanes;c=(1<<31-Et(42|e)+1)-1,c&=u&~(d&~y),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(n=!0,rm(l,c))}else c=be,c=iu(l,l===ze?c:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(c&3)===0||Qa(l,c)||(n=!0,rm(l,c));l=l.next}while(n);_c=!1}}function Mv(){am()}function am(){Iu=zc=!1;var e=0;Nl!==0&&(Lv()&&(e=Nl),Nl=0);for(var t=Pt(),n=null,l=Pu;l!==null;){var u=l.next,c=im(l,t);c===0?(l.next=null,n===null?Pu=u:n.next=u,u===null&&(va=n)):(n=l,(e!==0||(c&3)!==0)&&(Iu=!0)),l=u}Oi(e)}function im(e,t){for(var n=e.suspendedLanes,l=e.pingedLanes,u=e.expirationTimes,c=e.pendingLanes&-62914561;0<c;){var d=31-Et(c),y=1<<d,b=u[d];b===-1?((y&n)===0||(y&l)!==0)&&(u[d]=ng(y,t)):b<=t&&(e.expiredLanes|=y),c&=~y}if(t=ze,n=be,n=iu(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,n===0||e===t&&(we===2||we===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&Xr(l),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||Qa(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(l!==null&&Xr(l),Jr(n)){case 2:case 8:n=Fo;break;case 32:n=nu;break;case 268435456:n=$o;break;default:n=nu}return l=um.bind(null,e),n=Vr(n,l),e.callbackPriority=t,e.callbackNode=n,t}return l!==null&&l!==null&&Xr(l),e.callbackPriority=2,e.callbackNode=null,2}function um(e,t){if(tt!==0&&tt!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(Wu()&&e.callbackNode!==n)return null;var l=be;return l=iu(e,e===ze?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(Yh(e,l,t),im(e,Pt()),e.callbackNode!=null&&e.callbackNode===n?um.bind(null,e):null)}function rm(e,t){if(Wu())return null;Yh(e,t,!0)}function Dv(){Gv(function(){(Te&6)!==0?Vr(Jo,Mv):am()})}function Uc(){return Nl===0&&(Nl=Wo()),Nl}function sm(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:ou(""+e)}function cm(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function zv(e,t,n,l,u){if(t==="submit"&&n&&n.stateNode===u){var c=sm((u[ht]||null).action),d=l.submitter;d&&(t=(t=d[ht]||null)?sm(t.formAction):d.getAttribute("formAction"),t!==null&&(c=t,d=null));var y=new mu("action","action",null,l,u);e.push({event:y,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(Nl!==0){var b=d?cm(u,d):new FormData(u);ec(n,{pending:!0,data:b,method:u.method,action:c},null,b)}}else typeof c=="function"&&(y.preventDefault(),b=d?cm(u,d):new FormData(u),ec(n,{pending:!0,data:b,method:u.method,action:c},c,b))},currentTarget:u}]})}}for(var jc=0;jc<bs.length;jc++){var Hc=bs[jc],_v=Hc.toLowerCase(),Uv=Hc[0].toUpperCase()+Hc.slice(1);Gt(_v,"on"+Uv)}Gt(Yf,"onAnimationEnd"),Gt(Qf,"onAnimationIteration"),Gt(Vf,"onAnimationStart"),Gt("dblclick","onDoubleClick"),Gt("focusin","onFocus"),Gt("focusout","onBlur"),Gt(Wg,"onTransitionRun"),Gt(Pg,"onTransitionStart"),Gt(Ig,"onTransitionCancel"),Gt(Xf,"onTransitionEnd"),Yl("onMouseEnter",["mouseout","mouseover"]),Yl("onMouseLeave",["mouseout","mouseover"]),Yl("onPointerEnter",["pointerout","pointerover"]),Yl("onPointerLeave",["pointerout","pointerover"]),dl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),dl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),dl("onBeforeInput",["compositionend","keypress","textInput","paste"]),dl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),dl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),dl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ni="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),jv=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ni));function om(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var l=e[n],u=l.event;l=l.listeners;e:{var c=void 0;if(t)for(var d=l.length-1;0<=d;d--){var y=l[d],b=y.instance,N=y.currentTarget;if(y=y.listener,b!==c&&u.isPropagationStopped())break e;c=y,u.currentTarget=N;try{c(u)}catch(q){ku(q)}u.currentTarget=null,c=b}else for(d=0;d<l.length;d++){if(y=l[d],b=y.instance,N=y.currentTarget,y=y.listener,b!==c&&u.isPropagationStopped())break e;c=y,u.currentTarget=N;try{c(u)}catch(q){ku(q)}u.currentTarget=null,c=b}}}}function ve(e,t){var n=t[Fr];n===void 0&&(n=t[Fr]=new Set);var l=e+"__bubble";n.has(l)||(fm(t,e,2,!1),n.add(l))}function qc(e,t,n){var l=0;t&&(l|=4),fm(n,e,l,t)}var er="_reactListening"+Math.random().toString(36).slice(2);function Bc(e){if(!e[er]){e[er]=!0,lf.forEach(function(n){n!=="selectionchange"&&(jv.has(n)||qc(n,!1,e),qc(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[er]||(t[er]=!0,qc("selectionchange",!1,t))}}function fm(e,t,n,l){switch(jm(t)){case 2:var u=s0;break;case 8:u=c0;break;default:u=Pc}n=u.bind(null,t,n,e),u=void 0,!us||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(u=!0),l?u!==void 0?e.addEventListener(t,n,{capture:!0,passive:u}):e.addEventListener(t,n,!0):u!==void 0?e.addEventListener(t,n,{passive:u}):e.addEventListener(t,n,!1)}function Lc(e,t,n,l,u){var c=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var d=l.tag;if(d===3||d===4){var y=l.stateNode.containerInfo;if(y===u)break;if(d===4)for(d=l.return;d!==null;){var b=d.tag;if((b===3||b===4)&&d.stateNode.containerInfo===u)return;d=d.return}for(;y!==null;){if(d=Ll(y),d===null)return;if(b=d.tag,b===5||b===6||b===26||b===27){l=c=d;continue e}y=y.parentNode}}l=l.return}vf(function(){var N=c,q=as(n),G=[];e:{var D=Zf.get(e);if(D!==void 0){var z=mu,re=e;switch(e){case"keypress":if(du(n)===0)break e;case"keydown":case"keyup":z=Cg;break;case"focusin":re="focus",z=os;break;case"focusout":re="blur",z=os;break;case"beforeblur":case"afterblur":z=os;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":z=xf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":z=gg;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":z=zg;break;case Yf:case Qf:case Vf:z=Sg;break;case Xf:z=Ug;break;case"scroll":case"scrollend":z=yg;break;case"wheel":z=Hg;break;case"copy":case"cut":case"paste":z=Eg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":z=Af;break;case"toggle":case"beforetoggle":z=Bg}var ae=(t&4)!==0,Ce=!ae&&(e==="scroll"||e==="scrollend"),R=ae?D!==null?D+"Capture":null:D;ae=[];for(var x=N,w;x!==null;){var L=x;if(w=L.stateNode,L=L.tag,L!==5&&L!==26&&L!==27||w===null||R===null||(L=Ka(x,R),L!=null&&ae.push(Ci(x,L,w))),Ce)break;x=x.return}0<ae.length&&(D=new z(D,re,null,n,q),G.push({event:D,listeners:ae}))}}if((t&7)===0){e:{if(D=e==="mouseover"||e==="pointerover",z=e==="mouseout"||e==="pointerout",D&&n!==ls&&(re=n.relatedTarget||n.fromElement)&&(Ll(re)||re[Bl]))break e;if((z||D)&&(D=q.window===q?q:(D=q.ownerDocument)?D.defaultView||D.parentWindow:window,z?(re=n.relatedTarget||n.toElement,z=N,re=re?Ll(re):null,re!==null&&(Ce=f(re),ae=re.tag,re!==Ce||ae!==5&&ae!==27&&ae!==6)&&(re=null)):(z=null,re=N),z!==re)){if(ae=xf,L="onMouseLeave",R="onMouseEnter",x="mouse",(e==="pointerout"||e==="pointerover")&&(ae=Af,L="onPointerLeave",R="onPointerEnter",x="pointer"),Ce=z==null?D:Za(z),w=re==null?D:Za(re),D=new ae(L,x+"leave",z,n,q),D.target=Ce,D.relatedTarget=w,L=null,Ll(q)===N&&(ae=new ae(R,x+"enter",re,n,q),ae.target=w,ae.relatedTarget=Ce,L=ae),Ce=L,z&&re)t:{for(ae=z,R=re,x=0,w=ae;w;w=ba(w))x++;for(w=0,L=R;L;L=ba(L))w++;for(;0<x-w;)ae=ba(ae),x--;for(;0<w-x;)R=ba(R),w--;for(;x--;){if(ae===R||R!==null&&ae===R.alternate)break t;ae=ba(ae),R=ba(R)}ae=null}else ae=null;z!==null&&dm(G,D,z,ae,!1),re!==null&&Ce!==null&&dm(G,Ce,re,ae,!0)}}e:{if(D=N?Za(N):window,z=D.nodeName&&D.nodeName.toLowerCase(),z==="select"||z==="input"&&D.type==="file")var I=Df;else if(Cf(D))if(zf)I=Jg;else{I=Zg;var ye=Xg}else z=D.nodeName,!z||z.toLowerCase()!=="input"||D.type!=="checkbox"&&D.type!=="radio"?N&&ns(N.elementType)&&(I=Df):I=Kg;if(I&&(I=I(e,N))){Mf(G,I,n,q);break e}ye&&ye(e,D,N),e==="focusout"&&N&&D.type==="number"&&N.memoizedProps.value!=null&&ts(D,"number",D.value)}switch(ye=N?Za(N):window,e){case"focusin":(Cf(ye)||ye.contentEditable==="true")&&(Fl=ye,ps=N,ti=null);break;case"focusout":ti=ps=Fl=null;break;case"mousedown":gs=!0;break;case"contextmenu":case"mouseup":case"dragend":gs=!1,kf(G,n,q);break;case"selectionchange":if($g)break;case"keydown":case"keyup":kf(G,n,q)}var ne;if(ds)e:{switch(e){case"compositionstart":var ie="onCompositionStart";break e;case"compositionend":ie="onCompositionEnd";break e;case"compositionupdate":ie="onCompositionUpdate";break e}ie=void 0}else Jl?Of(e,n)&&(ie="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(ie="onCompositionStart");ie&&(Rf&&n.locale!=="ko"&&(Jl||ie!=="onCompositionStart"?ie==="onCompositionEnd"&&Jl&&(ne=bf()):(Hn=q,rs="value"in Hn?Hn.value:Hn.textContent,Jl=!0)),ye=tr(N,ie),0<ye.length&&(ie=new Ef(ie,e,null,n,q),G.push({event:ie,listeners:ye}),ne?ie.data=ne:(ne=Nf(n),ne!==null&&(ie.data=ne)))),(ne=kg?Gg(e,n):Yg(e,n))&&(ie=tr(N,"onBeforeInput"),0<ie.length&&(ye=new Ef("onBeforeInput","beforeinput",null,n,q),G.push({event:ye,listeners:ie}),ye.data=ne)),zv(G,e,N,n,q)}om(G,t)})}function Ci(e,t,n){return{instance:e,listener:t,currentTarget:n}}function tr(e,t){for(var n=t+"Capture",l=[];e!==null;){var u=e,c=u.stateNode;if(u=u.tag,u!==5&&u!==26&&u!==27||c===null||(u=Ka(e,n),u!=null&&l.unshift(Ci(e,u,c)),u=Ka(e,t),u!=null&&l.push(Ci(e,u,c))),e.tag===3)return l;e=e.return}return[]}function ba(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function dm(e,t,n,l,u){for(var c=t._reactName,d=[];n!==null&&n!==l;){var y=n,b=y.alternate,N=y.stateNode;if(y=y.tag,b!==null&&b===l)break;y!==5&&y!==26&&y!==27||N===null||(b=N,u?(N=Ka(n,c),N!=null&&d.unshift(Ci(n,N,b))):u||(N=Ka(n,c),N!=null&&d.push(Ci(n,N,b)))),n=n.return}d.length!==0&&e.push({event:t,listeners:d})}var Hv=/\r\n?/g,qv=/\u0000|\uFFFD/g;function hm(e){return(typeof e=="string"?e:""+e).replace(Hv,`
`).replace(qv,"")}function mm(e,t){return t=hm(t),hm(e)===t}function nr(){}function Ne(e,t,n,l,u,c){switch(n){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||Xl(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&Xl(e,""+l);break;case"className":ru(e,"class",l);break;case"tabIndex":ru(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":ru(e,n,l);break;case"style":pf(e,l,c);break;case"data":if(t!=="object"){ru(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(n);break}l=ou(""+l),e.setAttribute(n,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(n==="formAction"?(t!=="input"&&Ne(e,t,"name",u.name,u,null),Ne(e,t,"formEncType",u.formEncType,u,null),Ne(e,t,"formMethod",u.formMethod,u,null),Ne(e,t,"formTarget",u.formTarget,u,null)):(Ne(e,t,"encType",u.encType,u,null),Ne(e,t,"method",u.method,u,null),Ne(e,t,"target",u.target,u,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(n);break}l=ou(""+l),e.setAttribute(n,l);break;case"onClick":l!=null&&(e.onclick=nr);break;case"onScroll":l!=null&&ve("scroll",e);break;case"onScrollEnd":l!=null&&ve("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(s(61));if(n=l.__html,n!=null){if(u.children!=null)throw Error(s(60));e.innerHTML=n}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}n=ou(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,""+l):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":l===!0?e.setAttribute(n,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,l):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(n,l):e.removeAttribute(n);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(n):e.setAttribute(n,l);break;case"popover":ve("beforetoggle",e),ve("toggle",e),uu(e,"popover",l);break;case"xlinkActuate":on(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":on(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":on(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":on(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":on(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":on(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":on(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":on(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":on(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":uu(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=hg.get(n)||n,uu(e,n,l))}}function kc(e,t,n,l,u,c){switch(n){case"style":pf(e,l,c);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(s(61));if(n=l.__html,n!=null){if(u.children!=null)throw Error(s(60));e.innerHTML=n}}break;case"children":typeof l=="string"?Xl(e,l):(typeof l=="number"||typeof l=="bigint")&&Xl(e,""+l);break;case"onScroll":l!=null&&ve("scroll",e);break;case"onScrollEnd":l!=null&&ve("scrollend",e);break;case"onClick":l!=null&&(e.onclick=nr);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!af.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(u=n.endsWith("Capture"),t=n.slice(2,u?n.length-7:void 0),c=e[ht]||null,c=c!=null?c[n]:null,typeof c=="function"&&e.removeEventListener(t,c,u),typeof l=="function")){typeof c!="function"&&c!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,l,u);break e}n in e?e[n]=l:l===!0?e.setAttribute(n,""):uu(e,n,l)}}}function nt(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ve("error",e),ve("load",e);var l=!1,u=!1,c;for(c in n)if(n.hasOwnProperty(c)){var d=n[c];if(d!=null)switch(c){case"src":l=!0;break;case"srcSet":u=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:Ne(e,t,c,d,n,null)}}u&&Ne(e,t,"srcSet",n.srcSet,n,null),l&&Ne(e,t,"src",n.src,n,null);return;case"input":ve("invalid",e);var y=c=d=u=null,b=null,N=null;for(l in n)if(n.hasOwnProperty(l)){var q=n[l];if(q!=null)switch(l){case"name":u=q;break;case"type":d=q;break;case"checked":b=q;break;case"defaultChecked":N=q;break;case"value":c=q;break;case"defaultValue":y=q;break;case"children":case"dangerouslySetInnerHTML":if(q!=null)throw Error(s(137,t));break;default:Ne(e,t,l,q,n,null)}}df(e,c,y,b,N,d,u,!1),su(e);return;case"select":ve("invalid",e),l=d=c=null;for(u in n)if(n.hasOwnProperty(u)&&(y=n[u],y!=null))switch(u){case"value":c=y;break;case"defaultValue":d=y;break;case"multiple":l=y;default:Ne(e,t,u,y,n,null)}t=c,n=d,e.multiple=!!l,t!=null?Vl(e,!!l,t,!1):n!=null&&Vl(e,!!l,n,!0);return;case"textarea":ve("invalid",e),c=u=l=null;for(d in n)if(n.hasOwnProperty(d)&&(y=n[d],y!=null))switch(d){case"value":l=y;break;case"defaultValue":u=y;break;case"children":c=y;break;case"dangerouslySetInnerHTML":if(y!=null)throw Error(s(91));break;default:Ne(e,t,d,y,n,null)}mf(e,l,u,c),su(e);return;case"option":for(b in n)if(n.hasOwnProperty(b)&&(l=n[b],l!=null))switch(b){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Ne(e,t,b,l,n,null)}return;case"dialog":ve("beforetoggle",e),ve("toggle",e),ve("cancel",e),ve("close",e);break;case"iframe":case"object":ve("load",e);break;case"video":case"audio":for(l=0;l<Ni.length;l++)ve(Ni[l],e);break;case"image":ve("error",e),ve("load",e);break;case"details":ve("toggle",e);break;case"embed":case"source":case"link":ve("error",e),ve("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(N in n)if(n.hasOwnProperty(N)&&(l=n[N],l!=null))switch(N){case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:Ne(e,t,N,l,n,null)}return;default:if(ns(t)){for(q in n)n.hasOwnProperty(q)&&(l=n[q],l!==void 0&&kc(e,t,q,l,n,void 0));return}}for(y in n)n.hasOwnProperty(y)&&(l=n[y],l!=null&&Ne(e,t,y,l,n,null))}function Bv(e,t,n,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var u=null,c=null,d=null,y=null,b=null,N=null,q=null;for(z in n){var G=n[z];if(n.hasOwnProperty(z)&&G!=null)switch(z){case"checked":break;case"value":break;case"defaultValue":b=G;default:l.hasOwnProperty(z)||Ne(e,t,z,null,l,G)}}for(var D in l){var z=l[D];if(G=n[D],l.hasOwnProperty(D)&&(z!=null||G!=null))switch(D){case"type":c=z;break;case"name":u=z;break;case"checked":N=z;break;case"defaultChecked":q=z;break;case"value":d=z;break;case"defaultValue":y=z;break;case"children":case"dangerouslySetInnerHTML":if(z!=null)throw Error(s(137,t));break;default:z!==G&&Ne(e,t,D,z,l,G)}}es(e,d,y,b,N,q,c,u);return;case"select":z=d=y=D=null;for(c in n)if(b=n[c],n.hasOwnProperty(c)&&b!=null)switch(c){case"value":break;case"multiple":z=b;default:l.hasOwnProperty(c)||Ne(e,t,c,null,l,b)}for(u in l)if(c=l[u],b=n[u],l.hasOwnProperty(u)&&(c!=null||b!=null))switch(u){case"value":D=c;break;case"defaultValue":y=c;break;case"multiple":d=c;default:c!==b&&Ne(e,t,u,c,l,b)}t=y,n=d,l=z,D!=null?Vl(e,!!n,D,!1):!!l!=!!n&&(t!=null?Vl(e,!!n,t,!0):Vl(e,!!n,n?[]:"",!1));return;case"textarea":z=D=null;for(y in n)if(u=n[y],n.hasOwnProperty(y)&&u!=null&&!l.hasOwnProperty(y))switch(y){case"value":break;case"children":break;default:Ne(e,t,y,null,l,u)}for(d in l)if(u=l[d],c=n[d],l.hasOwnProperty(d)&&(u!=null||c!=null))switch(d){case"value":D=u;break;case"defaultValue":z=u;break;case"children":break;case"dangerouslySetInnerHTML":if(u!=null)throw Error(s(91));break;default:u!==c&&Ne(e,t,d,u,l,c)}hf(e,D,z);return;case"option":for(var re in n)if(D=n[re],n.hasOwnProperty(re)&&D!=null&&!l.hasOwnProperty(re))switch(re){case"selected":e.selected=!1;break;default:Ne(e,t,re,null,l,D)}for(b in l)if(D=l[b],z=n[b],l.hasOwnProperty(b)&&D!==z&&(D!=null||z!=null))switch(b){case"selected":e.selected=D&&typeof D!="function"&&typeof D!="symbol";break;default:Ne(e,t,b,D,l,z)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ae in n)D=n[ae],n.hasOwnProperty(ae)&&D!=null&&!l.hasOwnProperty(ae)&&Ne(e,t,ae,null,l,D);for(N in l)if(D=l[N],z=n[N],l.hasOwnProperty(N)&&D!==z&&(D!=null||z!=null))switch(N){case"children":case"dangerouslySetInnerHTML":if(D!=null)throw Error(s(137,t));break;default:Ne(e,t,N,D,l,z)}return;default:if(ns(t)){for(var Ce in n)D=n[Ce],n.hasOwnProperty(Ce)&&D!==void 0&&!l.hasOwnProperty(Ce)&&kc(e,t,Ce,void 0,l,D);for(q in l)D=l[q],z=n[q],!l.hasOwnProperty(q)||D===z||D===void 0&&z===void 0||kc(e,t,q,D,l,z);return}}for(var R in n)D=n[R],n.hasOwnProperty(R)&&D!=null&&!l.hasOwnProperty(R)&&Ne(e,t,R,null,l,D);for(G in l)D=l[G],z=n[G],!l.hasOwnProperty(G)||D===z||D==null&&z==null||Ne(e,t,G,D,l,z)}var Gc=null,Yc=null;function lr(e){return e.nodeType===9?e:e.ownerDocument}function ym(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function pm(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Qc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Vc=null;function Lv(){var e=window.event;return e&&e.type==="popstate"?e===Vc?!1:(Vc=e,!0):(Vc=null,!1)}var gm=typeof setTimeout=="function"?setTimeout:void 0,kv=typeof clearTimeout=="function"?clearTimeout:void 0,vm=typeof Promise=="function"?Promise:void 0,Gv=typeof queueMicrotask=="function"?queueMicrotask:typeof vm<"u"?function(e){return vm.resolve(null).then(e).catch(Yv)}:gm;function Yv(e){setTimeout(function(){throw e})}function Pn(e){return e==="head"}function bm(e,t){var n=t,l=0,u=0;do{var c=n.nextSibling;if(e.removeChild(n),c&&c.nodeType===8)if(n=c.data,n==="/$"){if(0<l&&8>l){n=l;var d=e.ownerDocument;if(n&1&&Mi(d.documentElement),n&2&&Mi(d.body),n&4)for(n=d.head,Mi(n),d=n.firstChild;d;){var y=d.nextSibling,b=d.nodeName;d[Xa]||b==="SCRIPT"||b==="STYLE"||b==="LINK"&&d.rel.toLowerCase()==="stylesheet"||n.removeChild(d),d=y}}if(u===0){e.removeChild(c),Bi(t);return}u--}else n==="$"||n==="$?"||n==="$!"?u++:l=n.charCodeAt(0)-48;else l=0;n=c}while(n);Bi(t)}function Xc(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Xc(n),$r(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function Qv(e,t,n,l){for(;e.nodeType===1;){var u=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[Xa])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(c=e.getAttribute("rel"),c==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(c!==u.rel||e.getAttribute("href")!==(u.href==null||u.href===""?null:u.href)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin)||e.getAttribute("title")!==(u.title==null?null:u.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(c=e.getAttribute("src"),(c!==(u.src==null?null:u.src)||e.getAttribute("type")!==(u.type==null?null:u.type)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin))&&c&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var c=u.name==null?null:""+u.name;if(u.type==="hidden"&&e.getAttribute("name")===c)return e}else return e;if(e=Qt(e.nextSibling),e===null)break}return null}function Vv(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=Qt(e.nextSibling),e===null))return null;return e}function Zc(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function Xv(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var l=function(){t(),n.removeEventListener("DOMContentLoaded",l)};n.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function Qt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Kc=null;function Sm(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function xm(e,t,n){switch(t=lr(n),e){case"html":if(e=t.documentElement,!e)throw Error(s(452));return e;case"head":if(e=t.head,!e)throw Error(s(453));return e;case"body":if(e=t.body,!e)throw Error(s(454));return e;default:throw Error(s(451))}}function Mi(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);$r(e)}var Bt=new Map,Em=new Set;function ar(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Rn=J.d;J.d={f:Zv,r:Kv,D:Jv,C:Fv,L:$v,m:Wv,X:Iv,S:Pv,M:e0};function Zv(){var e=Rn.f(),t=Fu();return e||t}function Kv(e){var t=kl(e);t!==null&&t.tag===5&&t.type==="form"?Yd(t):Rn.r(e)}var Sa=typeof document>"u"?null:document;function Am(e,t,n){var l=Sa;if(l&&typeof t=="string"&&t){var u=Dt(t);u='link[rel="'+e+'"][href="'+u+'"]',typeof n=="string"&&(u+='[crossorigin="'+n+'"]'),Em.has(u)||(Em.add(u),e={rel:e,crossOrigin:n,href:t},l.querySelector(u)===null&&(t=l.createElement("link"),nt(t,"link",e),$e(t),l.head.appendChild(t)))}}function Jv(e){Rn.D(e),Am("dns-prefetch",e,null)}function Fv(e,t){Rn.C(e,t),Am("preconnect",e,t)}function $v(e,t,n){Rn.L(e,t,n);var l=Sa;if(l&&e&&t){var u='link[rel="preload"][as="'+Dt(t)+'"]';t==="image"&&n&&n.imageSrcSet?(u+='[imagesrcset="'+Dt(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(u+='[imagesizes="'+Dt(n.imageSizes)+'"]')):u+='[href="'+Dt(e)+'"]';var c=u;switch(t){case"style":c=xa(e);break;case"script":c=Ea(e)}Bt.has(c)||(e=v({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),Bt.set(c,e),l.querySelector(u)!==null||t==="style"&&l.querySelector(Di(c))||t==="script"&&l.querySelector(zi(c))||(t=l.createElement("link"),nt(t,"link",e),$e(t),l.head.appendChild(t)))}}function Wv(e,t){Rn.m(e,t);var n=Sa;if(n&&e){var l=t&&typeof t.as=="string"?t.as:"script",u='link[rel="modulepreload"][as="'+Dt(l)+'"][href="'+Dt(e)+'"]',c=u;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=Ea(e)}if(!Bt.has(c)&&(e=v({rel:"modulepreload",href:e},t),Bt.set(c,e),n.querySelector(u)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(zi(c)))return}l=n.createElement("link"),nt(l,"link",e),$e(l),n.head.appendChild(l)}}}function Pv(e,t,n){Rn.S(e,t,n);var l=Sa;if(l&&e){var u=Gl(l).hoistableStyles,c=xa(e);t=t||"default";var d=u.get(c);if(!d){var y={loading:0,preload:null};if(d=l.querySelector(Di(c)))y.loading=5;else{e=v({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Bt.get(c))&&Jc(e,n);var b=d=l.createElement("link");$e(b),nt(b,"link",e),b._p=new Promise(function(N,q){b.onload=N,b.onerror=q}),b.addEventListener("load",function(){y.loading|=1}),b.addEventListener("error",function(){y.loading|=2}),y.loading|=4,ir(d,t,l)}d={type:"stylesheet",instance:d,count:1,state:y},u.set(c,d)}}}function Iv(e,t){Rn.X(e,t);var n=Sa;if(n&&e){var l=Gl(n).hoistableScripts,u=Ea(e),c=l.get(u);c||(c=n.querySelector(zi(u)),c||(e=v({src:e,async:!0},t),(t=Bt.get(u))&&Fc(e,t),c=n.createElement("script"),$e(c),nt(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},l.set(u,c))}}function e0(e,t){Rn.M(e,t);var n=Sa;if(n&&e){var l=Gl(n).hoistableScripts,u=Ea(e),c=l.get(u);c||(c=n.querySelector(zi(u)),c||(e=v({src:e,async:!0,type:"module"},t),(t=Bt.get(u))&&Fc(e,t),c=n.createElement("script"),$e(c),nt(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},l.set(u,c))}}function Rm(e,t,n,l){var u=(u=se.current)?ar(u):null;if(!u)throw Error(s(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=xa(n.href),n=Gl(u).hoistableStyles,l=n.get(t),l||(l={type:"style",instance:null,count:0,state:null},n.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=xa(n.href);var c=Gl(u).hoistableStyles,d=c.get(e);if(d||(u=u.ownerDocument||u,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,d),(c=u.querySelector(Di(e)))&&!c._p&&(d.instance=c,d.state.loading=5),Bt.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Bt.set(e,n),c||t0(u,e,n,d.state))),t&&l===null)throw Error(s(528,""));return d}if(t&&l!==null)throw Error(s(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Ea(n),n=Gl(u).hoistableScripts,l=n.get(t),l||(l={type:"script",instance:null,count:0,state:null},n.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(s(444,e))}}function xa(e){return'href="'+Dt(e)+'"'}function Di(e){return'link[rel="stylesheet"]['+e+"]"}function Tm(e){return v({},e,{"data-precedence":e.precedence,precedence:null})}function t0(e,t,n,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),nt(t,"link",n),$e(t),e.head.appendChild(t))}function Ea(e){return'[src="'+Dt(e)+'"]'}function zi(e){return"script[async]"+e}function wm(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+Dt(n.href)+'"]');if(l)return t.instance=l,$e(l),l;var u=v({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),$e(l),nt(l,"style",u),ir(l,n.precedence,e),t.instance=l;case"stylesheet":u=xa(n.href);var c=e.querySelector(Di(u));if(c)return t.state.loading|=4,t.instance=c,$e(c),c;l=Tm(n),(u=Bt.get(u))&&Jc(l,u),c=(e.ownerDocument||e).createElement("link"),$e(c);var d=c;return d._p=new Promise(function(y,b){d.onload=y,d.onerror=b}),nt(c,"link",l),t.state.loading|=4,ir(c,n.precedence,e),t.instance=c;case"script":return c=Ea(n.src),(u=e.querySelector(zi(c)))?(t.instance=u,$e(u),u):(l=n,(u=Bt.get(c))&&(l=v({},n),Fc(l,u)),e=e.ownerDocument||e,u=e.createElement("script"),$e(u),nt(u,"link",l),e.head.appendChild(u),t.instance=u);case"void":return null;default:throw Error(s(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,ir(l,n.precedence,e));return t.instance}function ir(e,t,n){for(var l=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),u=l.length?l[l.length-1]:null,c=u,d=0;d<l.length;d++){var y=l[d];if(y.dataset.precedence===t)c=y;else if(c!==u)break}c?c.parentNode.insertBefore(e,c.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function Jc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Fc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var ur=null;function Om(e,t,n){if(ur===null){var l=new Map,u=ur=new Map;u.set(n,l)}else u=ur,l=u.get(n),l||(l=new Map,u.set(n,l));if(l.has(e))return l;for(l.set(e,null),n=n.getElementsByTagName(e),u=0;u<n.length;u++){var c=n[u];if(!(c[Xa]||c[lt]||e==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var d=c.getAttribute(t)||"";d=e+d;var y=l.get(d);y?y.push(c):l.set(d,[c])}}return l}function Nm(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function n0(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Cm(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var _i=null;function l0(){}function a0(e,t,n){if(_i===null)throw Error(s(475));var l=_i;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var u=xa(n.href),c=e.querySelector(Di(u));if(c){e=c._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=rr.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=c,$e(c);return}c=e.ownerDocument||e,n=Tm(n),(u=Bt.get(u))&&Jc(n,u),c=c.createElement("link"),$e(c);var d=c;d._p=new Promise(function(y,b){d.onload=y,d.onerror=b}),nt(c,"link",n),t.instance=c}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=rr.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function i0(){if(_i===null)throw Error(s(475));var e=_i;return e.stylesheets&&e.count===0&&$c(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&$c(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function rr(){if(this.count--,this.count===0){if(this.stylesheets)$c(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var sr=null;function $c(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,sr=new Map,t.forEach(u0,e),sr=null,rr.call(e))}function u0(e,t){if(!(t.state.loading&4)){var n=sr.get(e);if(n)var l=n.get(null);else{n=new Map,sr.set(e,n);for(var u=e.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<u.length;c++){var d=u[c];(d.nodeName==="LINK"||d.getAttribute("media")!=="not all")&&(n.set(d.dataset.precedence,d),l=d)}l&&n.set(null,l)}u=t.instance,d=u.getAttribute("data-precedence"),c=n.get(d)||l,c===l&&n.set(null,u),n.set(d,u),this.count++,l=rr.bind(this),u.addEventListener("load",l),u.addEventListener("error",l),c?c.parentNode.insertBefore(u,c.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(u,e.firstChild)),t.state.loading|=4}}var Ui={$$typeof:Y,Provider:null,Consumer:null,_currentValue:V,_currentValue2:V,_threadCount:0};function r0(e,t,n,l,u,c,d,y){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Zr(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Zr(0),this.hiddenUpdates=Zr(null),this.identifierPrefix=l,this.onUncaughtError=u,this.onCaughtError=c,this.onRecoverableError=d,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=y,this.incompleteTransitions=new Map}function Mm(e,t,n,l,u,c,d,y,b,N,q,G){return e=new r0(e,t,n,d,y,b,N,G),t=1,c===!0&&(t|=24),c=Rt(3,null,null,t),e.current=c,c.stateNode=e,t=Ds(),t.refCount++,e.pooledCache=t,t.refCount++,c.memoizedState={element:l,isDehydrated:n,cache:t},js(c),e}function Dm(e){return e?(e=Il,e):Il}function zm(e,t,n,l,u,c){u=Dm(u),l.context===null?l.context=u:l.pendingContext=u,l=Ln(t),l.payload={element:n},c=c===void 0?null:c,c!==null&&(l.callback=c),n=kn(e,l,t),n!==null&&(Ct(n,e,t),oi(n,e,t))}function _m(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Wc(e,t){_m(e,t),(e=e.alternate)&&_m(e,t)}function Um(e){if(e.tag===13){var t=Pl(e,67108864);t!==null&&Ct(t,e,67108864),Wc(e,67108864)}}var cr=!0;function s0(e,t,n,l){var u=j.T;j.T=null;var c=J.p;try{J.p=2,Pc(e,t,n,l)}finally{J.p=c,j.T=u}}function c0(e,t,n,l){var u=j.T;j.T=null;var c=J.p;try{J.p=8,Pc(e,t,n,l)}finally{J.p=c,j.T=u}}function Pc(e,t,n,l){if(cr){var u=Ic(l);if(u===null)Lc(e,t,l,or,n),Hm(e,l);else if(f0(u,e,t,n,l))l.stopPropagation();else if(Hm(e,l),t&4&&-1<o0.indexOf(e)){for(;u!==null;){var c=kl(u);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var d=fl(c.pendingLanes);if(d!==0){var y=c;for(y.pendingLanes|=2,y.entangledLanes|=2;d;){var b=1<<31-Et(d);y.entanglements[1]|=b,d&=~b}ln(c),(Te&6)===0&&(Ku=Pt()+500,Oi(0))}}break;case 13:y=Pl(c,2),y!==null&&Ct(y,c,2),Fu(),Wc(c,2)}if(c=Ic(l),c===null&&Lc(e,t,l,or,n),c===u)break;u=c}u!==null&&l.stopPropagation()}else Lc(e,t,l,null,n)}}function Ic(e){return e=as(e),eo(e)}var or=null;function eo(e){if(or=null,e=Ll(e),e!==null){var t=f(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=h(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return or=e,null}function jm(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Fp()){case Jo:return 2;case Fo:return 8;case nu:case $p:return 32;case $o:return 268435456;default:return 32}default:return 32}}var to=!1,In=null,el=null,tl=null,ji=new Map,Hi=new Map,nl=[],o0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Hm(e,t){switch(e){case"focusin":case"focusout":In=null;break;case"dragenter":case"dragleave":el=null;break;case"mouseover":case"mouseout":tl=null;break;case"pointerover":case"pointerout":ji.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Hi.delete(t.pointerId)}}function qi(e,t,n,l,u,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:n,eventSystemFlags:l,nativeEvent:c,targetContainers:[u]},t!==null&&(t=kl(t),t!==null&&Um(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,u!==null&&t.indexOf(u)===-1&&t.push(u),e)}function f0(e,t,n,l,u){switch(t){case"focusin":return In=qi(In,e,t,n,l,u),!0;case"dragenter":return el=qi(el,e,t,n,l,u),!0;case"mouseover":return tl=qi(tl,e,t,n,l,u),!0;case"pointerover":var c=u.pointerId;return ji.set(c,qi(ji.get(c)||null,e,t,n,l,u)),!0;case"gotpointercapture":return c=u.pointerId,Hi.set(c,qi(Hi.get(c)||null,e,t,n,l,u)),!0}return!1}function qm(e){var t=Ll(e.target);if(t!==null){var n=f(t);if(n!==null){if(t=n.tag,t===13){if(t=h(n),t!==null){e.blockedOn=t,ag(e.priority,function(){if(n.tag===13){var l=Nt();l=Kr(l);var u=Pl(n,l);u!==null&&Ct(u,n,l),Wc(n,l)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function fr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ic(e.nativeEvent);if(n===null){n=e.nativeEvent;var l=new n.constructor(n.type,n);ls=l,n.target.dispatchEvent(l),ls=null}else return t=kl(n),t!==null&&Um(t),e.blockedOn=n,!1;t.shift()}return!0}function Bm(e,t,n){fr(e)&&n.delete(t)}function d0(){to=!1,In!==null&&fr(In)&&(In=null),el!==null&&fr(el)&&(el=null),tl!==null&&fr(tl)&&(tl=null),ji.forEach(Bm),Hi.forEach(Bm)}function dr(e,t){e.blockedOn===t&&(e.blockedOn=null,to||(to=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,d0)))}var hr=null;function Lm(e){hr!==e&&(hr=e,a.unstable_scheduleCallback(a.unstable_NormalPriority,function(){hr===e&&(hr=null);for(var t=0;t<e.length;t+=3){var n=e[t],l=e[t+1],u=e[t+2];if(typeof l!="function"){if(eo(l||n)===null)continue;break}var c=kl(n);c!==null&&(e.splice(t,3),t-=3,ec(c,{pending:!0,data:u,method:n.method,action:l},l,u))}}))}function Bi(e){function t(b){return dr(b,e)}In!==null&&dr(In,e),el!==null&&dr(el,e),tl!==null&&dr(tl,e),ji.forEach(t),Hi.forEach(t);for(var n=0;n<nl.length;n++){var l=nl[n];l.blockedOn===e&&(l.blockedOn=null)}for(;0<nl.length&&(n=nl[0],n.blockedOn===null);)qm(n),n.blockedOn===null&&nl.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(l=0;l<n.length;l+=3){var u=n[l],c=n[l+1],d=u[ht]||null;if(typeof c=="function")d||Lm(n);else if(d){var y=null;if(c&&c.hasAttribute("formAction")){if(u=c,d=c[ht]||null)y=d.formAction;else if(eo(u)!==null)continue}else y=d.action;typeof y=="function"?n[l+1]=y:(n.splice(l,3),l-=3),Lm(n)}}}function no(e){this._internalRoot=e}mr.prototype.render=no.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(s(409));var n=t.current,l=Nt();zm(n,l,e,t,null,null)},mr.prototype.unmount=no.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;zm(e.current,2,null,e,null,null),Fu(),t[Bl]=null}};function mr(e){this._internalRoot=e}mr.prototype.unstable_scheduleHydration=function(e){if(e){var t=tf();e={blockedOn:null,target:e,priority:t};for(var n=0;n<nl.length&&t!==0&&t<nl[n].priority;n++);nl.splice(n,0,e),n===0&&qm(e)}};var km=i.version;if(km!=="19.1.0")throw Error(s(527,km,"19.1.0"));J.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(s(188)):(e=Object.keys(e).join(","),Error(s(268,e)));return e=p(t),e=e!==null?m(e):null,e=e===null?null:e.stateNode,e};var h0={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:j,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var yr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!yr.isDisabled&&yr.supportsFiber)try{Ya=yr.inject(h0),xt=yr}catch{}}return ki.createRoot=function(e,t){if(!o(e))throw Error(s(299));var n=!1,l="",u=nh,c=lh,d=ah,y=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(u=t.onUncaughtError),t.onCaughtError!==void 0&&(c=t.onCaughtError),t.onRecoverableError!==void 0&&(d=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(y=t.unstable_transitionCallbacks)),t=Mm(e,1,!1,null,null,n,l,u,c,d,y,null),e[Bl]=t.current,Bc(e),new no(t)},ki.hydrateRoot=function(e,t,n){if(!o(e))throw Error(s(299));var l=!1,u="",c=nh,d=lh,y=ah,b=null,N=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(u=n.identifierPrefix),n.onUncaughtError!==void 0&&(c=n.onUncaughtError),n.onCaughtError!==void 0&&(d=n.onCaughtError),n.onRecoverableError!==void 0&&(y=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(b=n.unstable_transitionCallbacks),n.formState!==void 0&&(N=n.formState)),t=Mm(e,1,!0,t,n??null,l,u,c,d,y,b,N),t.context=Dm(null),n=t.current,l=Nt(),l=Kr(l),u=Ln(l),u.callback=null,kn(n,u,l),n=l,t.current.lanes=n,Va(t,n),ln(t),e[Bl]=t.current,Bc(e),new mr(t)},ki.version="19.1.0",ki}var Wm;function A0(){if(Wm)return uo.exports;Wm=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(i){console.error(i)}}return a(),uo.exports=E0(),uo.exports}var R0=A0(),Gi={},Pm;function T0(){if(Pm)return Gi;Pm=1,Object.defineProperty(Gi,"__esModule",{value:!0}),Gi.parse=h,Gi.serialize=m;const a=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,i=/^[\u0021-\u003A\u003C-\u007E]*$/,r=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,s=/^[\u0020-\u003A\u003D-\u007E]*$/,o=Object.prototype.toString,f=(()=>{const T=function(){};return T.prototype=Object.create(null),T})();function h(T,B){const A=new f,M=T.length;if(M<2)return A;const C=(B==null?void 0:B.decode)||v;let H=0;do{const Z=T.indexOf("=",H);if(Z===-1)break;const Y=T.indexOf(";",H),P=Y===-1?M:Y;if(Z>P){H=T.lastIndexOf(";",Z-1)+1;continue}const K=g(T,H,Z),ue=p(T,Z,K),fe=T.slice(K,ue);if(A[fe]===void 0){let F=g(T,Z+1,P),le=p(T,P,F);const je=C(T.slice(F,le));A[fe]=je}H=P+1}while(H<M);return A}function g(T,B,A){do{const M=T.charCodeAt(B);if(M!==32&&M!==9)return B}while(++B<A);return A}function p(T,B,A){for(;B>A;){const M=T.charCodeAt(--B);if(M!==32&&M!==9)return B+1}return A}function m(T,B,A){const M=(A==null?void 0:A.encode)||encodeURIComponent;if(!a.test(T))throw new TypeError(`argument name is invalid: ${T}`);const C=M(B);if(!i.test(C))throw new TypeError(`argument val is invalid: ${B}`);let H=T+"="+C;if(!A)return H;if(A.maxAge!==void 0){if(!Number.isInteger(A.maxAge))throw new TypeError(`option maxAge is invalid: ${A.maxAge}`);H+="; Max-Age="+A.maxAge}if(A.domain){if(!r.test(A.domain))throw new TypeError(`option domain is invalid: ${A.domain}`);H+="; Domain="+A.domain}if(A.path){if(!s.test(A.path))throw new TypeError(`option path is invalid: ${A.path}`);H+="; Path="+A.path}if(A.expires){if(!E(A.expires)||!Number.isFinite(A.expires.valueOf()))throw new TypeError(`option expires is invalid: ${A.expires}`);H+="; Expires="+A.expires.toUTCString()}if(A.httpOnly&&(H+="; HttpOnly"),A.secure&&(H+="; Secure"),A.partitioned&&(H+="; Partitioned"),A.priority)switch(typeof A.priority=="string"?A.priority.toLowerCase():void 0){case"low":H+="; Priority=Low";break;case"medium":H+="; Priority=Medium";break;case"high":H+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${A.priority}`)}if(A.sameSite)switch(typeof A.sameSite=="string"?A.sameSite.toLowerCase():A.sameSite){case!0:case"strict":H+="; SameSite=Strict";break;case"lax":H+="; SameSite=Lax";break;case"none":H+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${A.sameSite}`)}return H}function v(T){if(T.indexOf("%")===-1)return T;try{return decodeURIComponent(T)}catch{return T}}function E(T){return o.call(T)==="[object Date]"}return Gi}T0();var Im="popstate";function w0(a={}){function i(s,o){let{pathname:f,search:h,hash:g}=s.location;return So("",{pathname:f,search:h,hash:g},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function r(s,o){return typeof o=="string"?o:Xi(o)}return N0(i,r,null,a)}function Ue(a,i){if(a===!1||a===null||typeof a>"u")throw new Error(i)}function Kt(a,i){if(!a){typeof console<"u"&&console.warn(i);try{throw new Error(i)}catch{}}}function O0(){return Math.random().toString(36).substring(2,10)}function ey(a,i){return{usr:a.state,key:a.key,idx:i}}function So(a,i,r=null,s){return{pathname:typeof a=="string"?a:a.pathname,search:"",hash:"",...typeof i=="string"?ja(i):i,state:r,key:i&&i.key||s||O0()}}function Xi({pathname:a="/",search:i="",hash:r=""}){return i&&i!=="?"&&(a+=i.charAt(0)==="?"?i:"?"+i),r&&r!=="#"&&(a+=r.charAt(0)==="#"?r:"#"+r),a}function ja(a){let i={};if(a){let r=a.indexOf("#");r>=0&&(i.hash=a.substring(r),a=a.substring(0,r));let s=a.indexOf("?");s>=0&&(i.search=a.substring(s),a=a.substring(0,s)),a&&(i.pathname=a)}return i}function N0(a,i,r,s={}){let{window:o=document.defaultView,v5Compat:f=!1}=s,h=o.history,g="POP",p=null,m=v();m==null&&(m=0,h.replaceState({...h.state,idx:m},""));function v(){return(h.state||{idx:null}).idx}function E(){g="POP";let C=v(),H=C==null?null:C-m;m=C,p&&p({action:g,location:M.location,delta:H})}function T(C,H){g="PUSH";let Z=So(M.location,C,H);m=v()+1;let Y=ey(Z,m),P=M.createHref(Z);try{h.pushState(Y,"",P)}catch(K){if(K instanceof DOMException&&K.name==="DataCloneError")throw K;o.location.assign(P)}f&&p&&p({action:g,location:M.location,delta:1})}function B(C,H){g="REPLACE";let Z=So(M.location,C,H);m=v();let Y=ey(Z,m),P=M.createHref(Z);h.replaceState(Y,"",P),f&&p&&p({action:g,location:M.location,delta:0})}function A(C){return C0(C)}let M={get action(){return g},get location(){return a(o,h)},listen(C){if(p)throw new Error("A history only accepts one active listener");return o.addEventListener(Im,E),p=C,()=>{o.removeEventListener(Im,E),p=null}},createHref(C){return i(o,C)},createURL:A,encodeLocation(C){let H=A(C);return{pathname:H.pathname,search:H.search,hash:H.hash}},push:T,replace:B,go(C){return h.go(C)}};return M}function C0(a,i=!1){let r="http://localhost";typeof window<"u"&&(r=window.location.origin!=="null"?window.location.origin:window.location.href),Ue(r,"No window.location.(origin|href) available to create URL");let s=typeof a=="string"?a:Xi(a);return s=s.replace(/ $/,"%20"),!i&&s.startsWith("//")&&(s=r+s),new URL(s,r)}function ky(a,i,r="/"){return M0(a,i,r,!1)}function M0(a,i,r,s){let o=typeof i=="string"?ja(i):i,f=Mn(o.pathname||"/",r);if(f==null)return null;let h=Gy(a);D0(h);let g=null;for(let p=0;g==null&&p<h.length;++p){let m=Y0(f);g=k0(h[p],m,s)}return g}function Gy(a,i=[],r=[],s=""){let o=(f,h,g)=>{let p={relativePath:g===void 0?f.path||"":g,caseSensitive:f.caseSensitive===!0,childrenIndex:h,route:f};p.relativePath.startsWith("/")&&(Ue(p.relativePath.startsWith(s),`Absolute route path "${p.relativePath}" nested under path "${s}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),p.relativePath=p.relativePath.slice(s.length));let m=Cn([s,p.relativePath]),v=r.concat(p);f.children&&f.children.length>0&&(Ue(f.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${m}".`),Gy(f.children,i,v,m)),!(f.path==null&&!f.index)&&i.push({path:m,score:B0(m,f.index),routesMeta:v})};return a.forEach((f,h)=>{var g;if(f.path===""||!((g=f.path)!=null&&g.includes("?")))o(f,h);else for(let p of Yy(f.path))o(f,h,p)}),i}function Yy(a){let i=a.split("/");if(i.length===0)return[];let[r,...s]=i,o=r.endsWith("?"),f=r.replace(/\?$/,"");if(s.length===0)return o?[f,""]:[f];let h=Yy(s.join("/")),g=[];return g.push(...h.map(p=>p===""?f:[f,p].join("/"))),o&&g.push(...h),g.map(p=>a.startsWith("/")&&p===""?"/":p)}function D0(a){a.sort((i,r)=>i.score!==r.score?r.score-i.score:L0(i.routesMeta.map(s=>s.childrenIndex),r.routesMeta.map(s=>s.childrenIndex)))}var z0=/^:[\w-]+$/,_0=3,U0=2,j0=1,H0=10,q0=-2,ty=a=>a==="*";function B0(a,i){let r=a.split("/"),s=r.length;return r.some(ty)&&(s+=q0),i&&(s+=U0),r.filter(o=>!ty(o)).reduce((o,f)=>o+(z0.test(f)?_0:f===""?j0:H0),s)}function L0(a,i){return a.length===i.length&&a.slice(0,-1).every((s,o)=>s===i[o])?a[a.length-1]-i[i.length-1]:0}function k0(a,i,r=!1){let{routesMeta:s}=a,o={},f="/",h=[];for(let g=0;g<s.length;++g){let p=s[g],m=g===s.length-1,v=f==="/"?i:i.slice(f.length)||"/",E=Or({path:p.relativePath,caseSensitive:p.caseSensitive,end:m},v),T=p.route;if(!E&&m&&r&&!s[s.length-1].route.index&&(E=Or({path:p.relativePath,caseSensitive:p.caseSensitive,end:!1},v)),!E)return null;Object.assign(o,E.params),h.push({params:o,pathname:Cn([f,E.pathname]),pathnameBase:Z0(Cn([f,E.pathnameBase])),route:T}),E.pathnameBase!=="/"&&(f=Cn([f,E.pathnameBase]))}return h}function Or(a,i){typeof a=="string"&&(a={path:a,caseSensitive:!1,end:!0});let[r,s]=G0(a.path,a.caseSensitive,a.end),o=i.match(r);if(!o)return null;let f=o[0],h=f.replace(/(.)\/+$/,"$1"),g=o.slice(1);return{params:s.reduce((m,{paramName:v,isOptional:E},T)=>{if(v==="*"){let A=g[T]||"";h=f.slice(0,f.length-A.length).replace(/(.)\/+$/,"$1")}const B=g[T];return E&&!B?m[v]=void 0:m[v]=(B||"").replace(/%2F/g,"/"),m},{}),pathname:f,pathnameBase:h,pattern:a}}function G0(a,i=!1,r=!0){Kt(a==="*"||!a.endsWith("*")||a.endsWith("/*"),`Route path "${a}" will be treated as if it were "${a.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${a.replace(/\*$/,"/*")}".`);let s=[],o="^"+a.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(h,g,p)=>(s.push({paramName:g,isOptional:p!=null}),p?"/?([^\\/]+)?":"/([^\\/]+)"));return a.endsWith("*")?(s.push({paramName:"*"}),o+=a==="*"||a==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?o+="\\/*$":a!==""&&a!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,i?void 0:"i"),s]}function Y0(a){try{return a.split("/").map(i=>decodeURIComponent(i).replace(/\//g,"%2F")).join("/")}catch(i){return Kt(!1,`The URL path "${a}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${i}).`),a}}function Mn(a,i){if(i==="/")return a;if(!a.toLowerCase().startsWith(i.toLowerCase()))return null;let r=i.endsWith("/")?i.length-1:i.length,s=a.charAt(r);return s&&s!=="/"?null:a.slice(r)||"/"}function Q0(a,i="/"){let{pathname:r,search:s="",hash:o=""}=typeof a=="string"?ja(a):a;return{pathname:r?r.startsWith("/")?r:V0(r,i):i,search:K0(s),hash:J0(o)}}function V0(a,i){let r=i.replace(/\/+$/,"").split("/");return a.split("/").forEach(o=>{o===".."?r.length>1&&r.pop():o!=="."&&r.push(o)}),r.length>1?r.join("/"):"/"}function oo(a,i,r,s){return`Cannot include a '${a}' character in a manually specified \`to.${i}\` field [${JSON.stringify(s)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function X0(a){return a.filter((i,r)=>r===0||i.route.path&&i.route.path.length>0)}function _o(a){let i=X0(a);return i.map((r,s)=>s===i.length-1?r.pathname:r.pathnameBase)}function Uo(a,i,r,s=!1){let o;typeof a=="string"?o=ja(a):(o={...a},Ue(!o.pathname||!o.pathname.includes("?"),oo("?","pathname","search",o)),Ue(!o.pathname||!o.pathname.includes("#"),oo("#","pathname","hash",o)),Ue(!o.search||!o.search.includes("#"),oo("#","search","hash",o)));let f=a===""||o.pathname==="",h=f?"/":o.pathname,g;if(h==null)g=r;else{let E=i.length-1;if(!s&&h.startsWith("..")){let T=h.split("/");for(;T[0]==="..";)T.shift(),E-=1;o.pathname=T.join("/")}g=E>=0?i[E]:"/"}let p=Q0(o,g),m=h&&h!=="/"&&h.endsWith("/"),v=(f||h===".")&&r.endsWith("/");return!p.pathname.endsWith("/")&&(m||v)&&(p.pathname+="/"),p}var Cn=a=>a.join("/").replace(/\/\/+/g,"/"),Z0=a=>a.replace(/\/+$/,"").replace(/^\/*/,"/"),K0=a=>!a||a==="?"?"":a.startsWith("?")?a:"?"+a,J0=a=>!a||a==="#"?"":a.startsWith("#")?a:"#"+a;function F0(a){return a!=null&&typeof a.status=="number"&&typeof a.statusText=="string"&&typeof a.internal=="boolean"&&"data"in a}var Qy=["POST","PUT","PATCH","DELETE"];new Set(Qy);var $0=["GET",...Qy];new Set($0);var Ha=_.createContext(null);Ha.displayName="DataRouter";var zr=_.createContext(null);zr.displayName="DataRouterState";var Vy=_.createContext({isTransitioning:!1});Vy.displayName="ViewTransition";var W0=_.createContext(new Map);W0.displayName="Fetchers";var P0=_.createContext(null);P0.displayName="Await";var Jt=_.createContext(null);Jt.displayName="Navigation";var Wi=_.createContext(null);Wi.displayName="Location";var cn=_.createContext({outlet:null,matches:[],isDataRoute:!1});cn.displayName="Route";var jo=_.createContext(null);jo.displayName="RouteError";function I0(a,{relative:i}={}){Ue(qa(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:s}=_.useContext(Jt),{hash:o,pathname:f,search:h}=Ii(a,{relative:i}),g=f;return r!=="/"&&(g=f==="/"?r:Cn([r,f])),s.createHref({pathname:g,search:h,hash:o})}function qa(){return _.useContext(Wi)!=null}function Ft(){return Ue(qa(),"useLocation() may be used only in the context of a <Router> component."),_.useContext(Wi).location}var Xy="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Zy(a){_.useContext(Jt).static||_.useLayoutEffect(a)}function Pi(){let{isDataRoute:a}=_.useContext(cn);return a?db():eb()}function eb(){Ue(qa(),"useNavigate() may be used only in the context of a <Router> component.");let a=_.useContext(Ha),{basename:i,navigator:r}=_.useContext(Jt),{matches:s}=_.useContext(cn),{pathname:o}=Ft(),f=JSON.stringify(_o(s)),h=_.useRef(!1);return Zy(()=>{h.current=!0}),_.useCallback((p,m={})=>{if(Kt(h.current,Xy),!h.current)return;if(typeof p=="number"){r.go(p);return}let v=Uo(p,JSON.parse(f),o,m.relative==="path");a==null&&i!=="/"&&(v.pathname=v.pathname==="/"?i:Cn([i,v.pathname])),(m.replace?r.replace:r.push)(v,m.state,m)},[i,r,f,o,a])}_.createContext(null);function Ii(a,{relative:i}={}){let{matches:r}=_.useContext(cn),{pathname:s}=Ft(),o=JSON.stringify(_o(r));return _.useMemo(()=>Uo(a,JSON.parse(o),s,i==="path"),[a,o,s,i])}function tb(a,i){return Ky(a,i)}function Ky(a,i,r,s){var Z;Ue(qa(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:o,static:f}=_.useContext(Jt),{matches:h}=_.useContext(cn),g=h[h.length-1],p=g?g.params:{},m=g?g.pathname:"/",v=g?g.pathnameBase:"/",E=g&&g.route;{let Y=E&&E.path||"";Jy(m,!E||Y.endsWith("*")||Y.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${m}" (under <Route path="${Y}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${Y}"> to <Route path="${Y==="/"?"*":`${Y}/*`}">.`)}let T=Ft(),B;if(i){let Y=typeof i=="string"?ja(i):i;Ue(v==="/"||((Z=Y.pathname)==null?void 0:Z.startsWith(v)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${v}" but pathname "${Y.pathname}" was given in the \`location\` prop.`),B=Y}else B=T;let A=B.pathname||"/",M=A;if(v!=="/"){let Y=v.replace(/^\//,"").split("/");M="/"+A.replace(/^\//,"").split("/").slice(Y.length).join("/")}let C=!f&&r&&r.matches&&r.matches.length>0?r.matches:ky(a,{pathname:M});Kt(E||C!=null,`No routes matched location "${B.pathname}${B.search}${B.hash}" `),Kt(C==null||C[C.length-1].route.element!==void 0||C[C.length-1].route.Component!==void 0||C[C.length-1].route.lazy!==void 0,`Matched leaf route at location "${B.pathname}${B.search}${B.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let H=ub(C&&C.map(Y=>Object.assign({},Y,{params:Object.assign({},p,Y.params),pathname:Cn([v,o.encodeLocation?o.encodeLocation(Y.pathname).pathname:Y.pathname]),pathnameBase:Y.pathnameBase==="/"?v:Cn([v,o.encodeLocation?o.encodeLocation(Y.pathnameBase).pathname:Y.pathnameBase])})),h,r,s);return i&&H?_.createElement(Wi.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...B},navigationType:"POP"}},H):H}function nb(){let a=fb(),i=F0(a)?`${a.status} ${a.statusText}`:a instanceof Error?a.message:JSON.stringify(a),r=a instanceof Error?a.stack:null,s="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:s},f={padding:"2px 4px",backgroundColor:s},h=null;return console.error("Error handled by React Router default ErrorBoundary:",a),h=_.createElement(_.Fragment,null,_.createElement("p",null,"💿 Hey developer 👋"),_.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",_.createElement("code",{style:f},"ErrorBoundary")," or"," ",_.createElement("code",{style:f},"errorElement")," prop on your route.")),_.createElement(_.Fragment,null,_.createElement("h2",null,"Unexpected Application Error!"),_.createElement("h3",{style:{fontStyle:"italic"}},i),r?_.createElement("pre",{style:o},r):null,h)}var lb=_.createElement(nb,null),ab=class extends _.Component{constructor(a){super(a),this.state={location:a.location,revalidation:a.revalidation,error:a.error}}static getDerivedStateFromError(a){return{error:a}}static getDerivedStateFromProps(a,i){return i.location!==a.location||i.revalidation!=="idle"&&a.revalidation==="idle"?{error:a.error,location:a.location,revalidation:a.revalidation}:{error:a.error!==void 0?a.error:i.error,location:i.location,revalidation:a.revalidation||i.revalidation}}componentDidCatch(a,i){console.error("React Router caught the following error during render",a,i)}render(){return this.state.error!==void 0?_.createElement(cn.Provider,{value:this.props.routeContext},_.createElement(jo.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function ib({routeContext:a,match:i,children:r}){let s=_.useContext(Ha);return s&&s.static&&s.staticContext&&(i.route.errorElement||i.route.ErrorBoundary)&&(s.staticContext._deepestRenderedBoundaryId=i.route.id),_.createElement(cn.Provider,{value:a},r)}function ub(a,i=[],r=null,s=null){if(a==null){if(!r)return null;if(r.errors)a=r.matches;else if(i.length===0&&!r.initialized&&r.matches.length>0)a=r.matches;else return null}let o=a,f=r==null?void 0:r.errors;if(f!=null){let p=o.findIndex(m=>m.route.id&&(f==null?void 0:f[m.route.id])!==void 0);Ue(p>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(f).join(",")}`),o=o.slice(0,Math.min(o.length,p+1))}let h=!1,g=-1;if(r)for(let p=0;p<o.length;p++){let m=o[p];if((m.route.HydrateFallback||m.route.hydrateFallbackElement)&&(g=p),m.route.id){let{loaderData:v,errors:E}=r,T=m.route.loader&&!v.hasOwnProperty(m.route.id)&&(!E||E[m.route.id]===void 0);if(m.route.lazy||T){h=!0,g>=0?o=o.slice(0,g+1):o=[o[0]];break}}}return o.reduceRight((p,m,v)=>{let E,T=!1,B=null,A=null;r&&(E=f&&m.route.id?f[m.route.id]:void 0,B=m.route.errorElement||lb,h&&(g<0&&v===0?(Jy("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),T=!0,A=null):g===v&&(T=!0,A=m.route.hydrateFallbackElement||null)));let M=i.concat(o.slice(0,v+1)),C=()=>{let H;return E?H=B:T?H=A:m.route.Component?H=_.createElement(m.route.Component,null):m.route.element?H=m.route.element:H=p,_.createElement(ib,{match:m,routeContext:{outlet:p,matches:M,isDataRoute:r!=null},children:H})};return r&&(m.route.ErrorBoundary||m.route.errorElement||v===0)?_.createElement(ab,{location:r.location,revalidation:r.revalidation,component:B,error:E,children:C(),routeContext:{outlet:null,matches:M,isDataRoute:!0}}):C()},null)}function Ho(a){return`${a} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function rb(a){let i=_.useContext(Ha);return Ue(i,Ho(a)),i}function sb(a){let i=_.useContext(zr);return Ue(i,Ho(a)),i}function cb(a){let i=_.useContext(cn);return Ue(i,Ho(a)),i}function qo(a){let i=cb(a),r=i.matches[i.matches.length-1];return Ue(r.route.id,`${a} can only be used on routes that contain a unique "id"`),r.route.id}function ob(){return qo("useRouteId")}function fb(){var s;let a=_.useContext(jo),i=sb("useRouteError"),r=qo("useRouteError");return a!==void 0?a:(s=i.errors)==null?void 0:s[r]}function db(){let{router:a}=rb("useNavigate"),i=qo("useNavigate"),r=_.useRef(!1);return Zy(()=>{r.current=!0}),_.useCallback(async(o,f={})=>{Kt(r.current,Xy),r.current&&(typeof o=="number"?a.navigate(o):await a.navigate(o,{fromRouteId:i,...f}))},[a,i])}var ny={};function Jy(a,i,r){!i&&!ny[a]&&(ny[a]=!0,Kt(!1,r))}_.memo(hb);function hb({routes:a,future:i,state:r}){return Ky(a,void 0,r,i)}function Fy({to:a,replace:i,state:r,relative:s}){Ue(qa(),"<Navigate> may be used only in the context of a <Router> component.");let{static:o}=_.useContext(Jt);Kt(!o,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:f}=_.useContext(cn),{pathname:h}=Ft(),g=Pi(),p=Uo(a,_o(f),h,s==="path"),m=JSON.stringify(p);return _.useEffect(()=>{g(JSON.parse(m),{replace:i,state:r,relative:s})},[g,m,s,i,r]),null}function Ra(a){Ue(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function mb({basename:a="/",children:i=null,location:r,navigationType:s="POP",navigator:o,static:f=!1}){Ue(!qa(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let h=a.replace(/^\/*/,"/"),g=_.useMemo(()=>({basename:h,navigator:o,static:f,future:{}}),[h,o,f]);typeof r=="string"&&(r=ja(r));let{pathname:p="/",search:m="",hash:v="",state:E=null,key:T="default"}=r,B=_.useMemo(()=>{let A=Mn(p,h);return A==null?null:{location:{pathname:A,search:m,hash:v,state:E,key:T},navigationType:s}},[h,p,m,v,E,T,s]);return Kt(B!=null,`<Router basename="${h}"> is not able to match the URL "${p}${m}${v}" because it does not start with the basename, so the <Router> won't render anything.`),B==null?null:_.createElement(Jt.Provider,{value:g},_.createElement(Wi.Provider,{children:i,value:B}))}function yb({children:a,location:i}){return tb(xo(a),i)}function xo(a,i=[]){let r=[];return _.Children.forEach(a,(s,o)=>{if(!_.isValidElement(s))return;let f=[...i,o];if(s.type===_.Fragment){r.push.apply(r,xo(s.props.children,f));return}Ue(s.type===Ra,`[${typeof s.type=="string"?s.type:s.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),Ue(!s.props.index||!s.props.children,"An index route cannot have child routes.");let h={id:s.props.id||f.join("-"),caseSensitive:s.props.caseSensitive,element:s.props.element,Component:s.props.Component,index:s.props.index,path:s.props.path,loader:s.props.loader,action:s.props.action,hydrateFallbackElement:s.props.hydrateFallbackElement,HydrateFallback:s.props.HydrateFallback,errorElement:s.props.errorElement,ErrorBoundary:s.props.ErrorBoundary,hasErrorBoundary:s.props.hasErrorBoundary===!0||s.props.ErrorBoundary!=null||s.props.errorElement!=null,shouldRevalidate:s.props.shouldRevalidate,handle:s.props.handle,lazy:s.props.lazy};s.props.children&&(h.children=xo(s.props.children,f)),r.push(h)}),r}var Er="get",Ar="application/x-www-form-urlencoded";function _r(a){return a!=null&&typeof a.tagName=="string"}function pb(a){return _r(a)&&a.tagName.toLowerCase()==="button"}function gb(a){return _r(a)&&a.tagName.toLowerCase()==="form"}function vb(a){return _r(a)&&a.tagName.toLowerCase()==="input"}function bb(a){return!!(a.metaKey||a.altKey||a.ctrlKey||a.shiftKey)}function Sb(a,i){return a.button===0&&(!i||i==="_self")&&!bb(a)}var gr=null;function xb(){if(gr===null)try{new FormData(document.createElement("form"),0),gr=!1}catch{gr=!0}return gr}var Eb=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function fo(a){return a!=null&&!Eb.has(a)?(Kt(!1,`"${a}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Ar}"`),null):a}function Ab(a,i){let r,s,o,f,h;if(gb(a)){let g=a.getAttribute("action");s=g?Mn(g,i):null,r=a.getAttribute("method")||Er,o=fo(a.getAttribute("enctype"))||Ar,f=new FormData(a)}else if(pb(a)||vb(a)&&(a.type==="submit"||a.type==="image")){let g=a.form;if(g==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let p=a.getAttribute("formaction")||g.getAttribute("action");if(s=p?Mn(p,i):null,r=a.getAttribute("formmethod")||g.getAttribute("method")||Er,o=fo(a.getAttribute("formenctype"))||fo(g.getAttribute("enctype"))||Ar,f=new FormData(g,a),!xb()){let{name:m,type:v,value:E}=a;if(v==="image"){let T=m?`${m}.`:"";f.append(`${T}x`,"0"),f.append(`${T}y`,"0")}else m&&f.append(m,E)}}else{if(_r(a))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=Er,s=null,o=Ar,h=a}return f&&o==="text/plain"&&(h=f,f=void 0),{action:s,method:r.toLowerCase(),encType:o,formData:f,body:h}}function Bo(a,i){if(a===!1||a===null||typeof a>"u")throw new Error(i)}async function Rb(a,i){if(a.id in i)return i[a.id];try{let r=await import(a.module);return i[a.id]=r,r}catch(r){return console.error(`Error loading route module \`${a.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Tb(a){return a==null?!1:a.href==null?a.rel==="preload"&&typeof a.imageSrcSet=="string"&&typeof a.imageSizes=="string":typeof a.rel=="string"&&typeof a.href=="string"}async function wb(a,i,r){let s=await Promise.all(a.map(async o=>{let f=i.routes[o.route.id];if(f){let h=await Rb(f,r);return h.links?h.links():[]}return[]}));return Mb(s.flat(1).filter(Tb).filter(o=>o.rel==="stylesheet"||o.rel==="preload").map(o=>o.rel==="stylesheet"?{...o,rel:"prefetch",as:"style"}:{...o,rel:"prefetch"}))}function ly(a,i,r,s,o,f){let h=(p,m)=>r[m]?p.route.id!==r[m].route.id:!0,g=(p,m)=>{var v;return r[m].pathname!==p.pathname||((v=r[m].route.path)==null?void 0:v.endsWith("*"))&&r[m].params["*"]!==p.params["*"]};return f==="assets"?i.filter((p,m)=>h(p,m)||g(p,m)):f==="data"?i.filter((p,m)=>{var E;let v=s.routes[p.route.id];if(!v||!v.hasLoader)return!1;if(h(p,m)||g(p,m))return!0;if(p.route.shouldRevalidate){let T=p.route.shouldRevalidate({currentUrl:new URL(o.pathname+o.search+o.hash,window.origin),currentParams:((E=r[0])==null?void 0:E.params)||{},nextUrl:new URL(a,window.origin),nextParams:p.params,defaultShouldRevalidate:!0});if(typeof T=="boolean")return T}return!0}):[]}function Ob(a,i,{includeHydrateFallback:r}={}){return Nb(a.map(s=>{let o=i.routes[s.route.id];if(!o)return[];let f=[o.module];return o.clientActionModule&&(f=f.concat(o.clientActionModule)),o.clientLoaderModule&&(f=f.concat(o.clientLoaderModule)),r&&o.hydrateFallbackModule&&(f=f.concat(o.hydrateFallbackModule)),o.imports&&(f=f.concat(o.imports)),f}).flat(1))}function Nb(a){return[...new Set(a)]}function Cb(a){let i={},r=Object.keys(a).sort();for(let s of r)i[s]=a[s];return i}function Mb(a,i){let r=new Set;return new Set(i),a.reduce((s,o)=>{let f=JSON.stringify(Cb(o));return r.has(f)||(r.add(f),s.push({key:f,link:o})),s},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var Db=new Set([100,101,204,205]);function zb(a,i){let r=typeof a=="string"?new URL(a,typeof window>"u"?"server://singlefetch/":window.location.origin):a;return r.pathname==="/"?r.pathname="_root.data":i&&Mn(r.pathname,i)==="/"?r.pathname=`${i.replace(/\/$/,"")}/_root.data`:r.pathname=`${r.pathname.replace(/\/$/,"")}.data`,r}function $y(){let a=_.useContext(Ha);return Bo(a,"You must render this element inside a <DataRouterContext.Provider> element"),a}function _b(){let a=_.useContext(zr);return Bo(a,"You must render this element inside a <DataRouterStateContext.Provider> element"),a}var Lo=_.createContext(void 0);Lo.displayName="FrameworkContext";function Wy(){let a=_.useContext(Lo);return Bo(a,"You must render this element inside a <HydratedRouter> element"),a}function Ub(a,i){let r=_.useContext(Lo),[s,o]=_.useState(!1),[f,h]=_.useState(!1),{onFocus:g,onBlur:p,onMouseEnter:m,onMouseLeave:v,onTouchStart:E}=i,T=_.useRef(null);_.useEffect(()=>{if(a==="render"&&h(!0),a==="viewport"){let M=H=>{H.forEach(Z=>{h(Z.isIntersecting)})},C=new IntersectionObserver(M,{threshold:.5});return T.current&&C.observe(T.current),()=>{C.disconnect()}}},[a]),_.useEffect(()=>{if(s){let M=setTimeout(()=>{h(!0)},100);return()=>{clearTimeout(M)}}},[s]);let B=()=>{o(!0)},A=()=>{o(!1),h(!1)};return r?a!=="intent"?[f,T,{}]:[f,T,{onFocus:Yi(g,B),onBlur:Yi(p,A),onMouseEnter:Yi(m,B),onMouseLeave:Yi(v,A),onTouchStart:Yi(E,B)}]:[!1,T,{}]}function Yi(a,i){return r=>{a&&a(r),r.defaultPrevented||i(r)}}function jb({page:a,...i}){let{router:r}=$y(),s=_.useMemo(()=>ky(r.routes,a,r.basename),[r.routes,a,r.basename]);return s?_.createElement(qb,{page:a,matches:s,...i}):null}function Hb(a){let{manifest:i,routeModules:r}=Wy(),[s,o]=_.useState([]);return _.useEffect(()=>{let f=!1;return wb(a,i,r).then(h=>{f||o(h)}),()=>{f=!0}},[a,i,r]),s}function qb({page:a,matches:i,...r}){let s=Ft(),{manifest:o,routeModules:f}=Wy(),{basename:h}=$y(),{loaderData:g,matches:p}=_b(),m=_.useMemo(()=>ly(a,i,p,o,s,"data"),[a,i,p,o,s]),v=_.useMemo(()=>ly(a,i,p,o,s,"assets"),[a,i,p,o,s]),E=_.useMemo(()=>{if(a===s.pathname+s.search+s.hash)return[];let A=new Set,M=!1;if(i.forEach(H=>{var Y;let Z=o.routes[H.route.id];!Z||!Z.hasLoader||(!m.some(P=>P.route.id===H.route.id)&&H.route.id in g&&((Y=f[H.route.id])!=null&&Y.shouldRevalidate)||Z.hasClientLoader?M=!0:A.add(H.route.id))}),A.size===0)return[];let C=zb(a,h);return M&&A.size>0&&C.searchParams.set("_routes",i.filter(H=>A.has(H.route.id)).map(H=>H.route.id).join(",")),[C.pathname+C.search]},[h,g,s,o,m,i,a,f]),T=_.useMemo(()=>Ob(v,o),[v,o]),B=Hb(v);return _.createElement(_.Fragment,null,E.map(A=>_.createElement("link",{key:A,rel:"prefetch",as:"fetch",href:A,...r})),T.map(A=>_.createElement("link",{key:A,rel:"modulepreload",href:A,...r})),B.map(({key:A,link:M})=>_.createElement("link",{key:A,...M})))}function Bb(...a){return i=>{a.forEach(r=>{typeof r=="function"?r(i):r!=null&&(r.current=i)})}}var Py=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Py&&(window.__reactRouterVersion="7.6.0")}catch{}function Lb({basename:a,children:i,window:r}){let s=_.useRef();s.current==null&&(s.current=w0({window:r,v5Compat:!0}));let o=s.current,[f,h]=_.useState({action:o.action,location:o.location}),g=_.useCallback(p=>{_.startTransition(()=>h(p))},[h]);return _.useLayoutEffect(()=>o.listen(g),[o,g]),_.createElement(mb,{basename:a,children:i,location:f.location,navigationType:f.action,navigator:o})}var Iy=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Ta=_.forwardRef(function({onClick:i,discover:r="render",prefetch:s="none",relative:o,reloadDocument:f,replace:h,state:g,target:p,to:m,preventScrollReset:v,viewTransition:E,...T},B){let{basename:A}=_.useContext(Jt),M=typeof m=="string"&&Iy.test(m),C,H=!1;if(typeof m=="string"&&M&&(C=m,Py))try{let le=new URL(window.location.href),je=m.startsWith("//")?new URL(le.protocol+m):new URL(m),ct=Mn(je.pathname,A);je.origin===le.origin&&ct!=null?m=ct+je.search+je.hash:H=!0}catch{Kt(!1,`<Link to="${m}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let Z=I0(m,{relative:o}),[Y,P,K]=Ub(s,T),ue=Qb(m,{replace:h,state:g,target:p,preventScrollReset:v,relative:o,viewTransition:E});function fe(le){i&&i(le),le.defaultPrevented||ue(le)}let F=_.createElement("a",{...T,...K,href:C||Z,onClick:H||f?i:fe,ref:Bb(B,P),target:p,"data-discover":!M&&r==="render"?"true":void 0});return Y&&!M?_.createElement(_.Fragment,null,F,_.createElement(jb,{page:Z})):F});Ta.displayName="Link";var kb=_.forwardRef(function({"aria-current":i="page",caseSensitive:r=!1,className:s="",end:o=!1,style:f,to:h,viewTransition:g,children:p,...m},v){let E=Ii(h,{relative:m.relative}),T=Ft(),B=_.useContext(zr),{navigator:A,basename:M}=_.useContext(Jt),C=B!=null&&Jb(E)&&g===!0,H=A.encodeLocation?A.encodeLocation(E).pathname:E.pathname,Z=T.pathname,Y=B&&B.navigation&&B.navigation.location?B.navigation.location.pathname:null;r||(Z=Z.toLowerCase(),Y=Y?Y.toLowerCase():null,H=H.toLowerCase()),Y&&M&&(Y=Mn(Y,M)||Y);const P=H!=="/"&&H.endsWith("/")?H.length-1:H.length;let K=Z===H||!o&&Z.startsWith(H)&&Z.charAt(P)==="/",ue=Y!=null&&(Y===H||!o&&Y.startsWith(H)&&Y.charAt(H.length)==="/"),fe={isActive:K,isPending:ue,isTransitioning:C},F=K?i:void 0,le;typeof s=="function"?le=s(fe):le=[s,K?"active":null,ue?"pending":null,C?"transitioning":null].filter(Boolean).join(" ");let je=typeof f=="function"?f(fe):f;return _.createElement(Ta,{...m,"aria-current":F,className:le,ref:v,style:je,to:h,viewTransition:g},typeof p=="function"?p(fe):p)});kb.displayName="NavLink";var Gb=_.forwardRef(({discover:a="render",fetcherKey:i,navigate:r,reloadDocument:s,replace:o,state:f,method:h=Er,action:g,onSubmit:p,relative:m,preventScrollReset:v,viewTransition:E,...T},B)=>{let A=Zb(),M=Kb(g,{relative:m}),C=h.toLowerCase()==="get"?"get":"post",H=typeof g=="string"&&Iy.test(g),Z=Y=>{if(p&&p(Y),Y.defaultPrevented)return;Y.preventDefault();let P=Y.nativeEvent.submitter,K=(P==null?void 0:P.getAttribute("formmethod"))||h;A(P||Y.currentTarget,{fetcherKey:i,method:K,navigate:r,replace:o,state:f,relative:m,preventScrollReset:v,viewTransition:E})};return _.createElement("form",{ref:B,method:C,action:M,onSubmit:s?p:Z,...T,"data-discover":!H&&a==="render"?"true":void 0})});Gb.displayName="Form";function Yb(a){return`${a} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function ep(a){let i=_.useContext(Ha);return Ue(i,Yb(a)),i}function Qb(a,{target:i,replace:r,state:s,preventScrollReset:o,relative:f,viewTransition:h}={}){let g=Pi(),p=Ft(),m=Ii(a,{relative:f});return _.useCallback(v=>{if(Sb(v,i)){v.preventDefault();let E=r!==void 0?r:Xi(p)===Xi(m);g(a,{replace:E,state:s,preventScrollReset:o,relative:f,viewTransition:h})}},[p,g,m,r,s,i,a,o,f,h])}var Vb=0,Xb=()=>`__${String(++Vb)}__`;function Zb(){let{router:a}=ep("useSubmit"),{basename:i}=_.useContext(Jt),r=ob();return _.useCallback(async(s,o={})=>{let{action:f,method:h,encType:g,formData:p,body:m}=Ab(s,i);if(o.navigate===!1){let v=o.fetcherKey||Xb();await a.fetch(v,r,o.action||f,{preventScrollReset:o.preventScrollReset,formData:p,body:m,formMethod:o.method||h,formEncType:o.encType||g,flushSync:o.flushSync})}else await a.navigate(o.action||f,{preventScrollReset:o.preventScrollReset,formData:p,body:m,formMethod:o.method||h,formEncType:o.encType||g,replace:o.replace,state:o.state,fromRouteId:r,flushSync:o.flushSync,viewTransition:o.viewTransition})},[a,i,r])}function Kb(a,{relative:i}={}){let{basename:r}=_.useContext(Jt),s=_.useContext(cn);Ue(s,"useFormAction must be used inside a RouteContext");let[o]=s.matches.slice(-1),f={...Ii(a||".",{relative:i})},h=Ft();if(a==null){f.search=h.search;let g=new URLSearchParams(f.search),p=g.getAll("index");if(p.some(v=>v==="")){g.delete("index"),p.filter(E=>E).forEach(E=>g.append("index",E));let v=g.toString();f.search=v?`?${v}`:""}}return(!a||a===".")&&o.route.index&&(f.search=f.search?f.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(f.pathname=f.pathname==="/"?r:Cn([r,f.pathname])),Xi(f)}function Jb(a,i={}){let r=_.useContext(Vy);Ue(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:s}=ep("useViewTransitionState"),o=Ii(a,{relative:i.relative});if(!r.isTransitioning)return!1;let f=Mn(r.currentLocation.pathname,s)||r.currentLocation.pathname,h=Mn(r.nextLocation.pathname,s)||r.nextLocation.pathname;return Or(o.pathname,h)!=null||Or(o.pathname,f)!=null}[...Db];var Ur=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(a){return this.listeners.add(a),this.onSubscribe(),()=>{this.listeners.delete(a),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},jr=typeof window>"u"||"Deno"in globalThis;function Vt(){}function Fb(a,i){return typeof a=="function"?a(i):a}function $b(a){return typeof a=="number"&&a>=0&&a!==1/0}function Wb(a,i){return Math.max(a+(i||0)-Date.now(),0)}function ay(a,i){return typeof a=="function"?a(i):a}function Pb(a,i){return typeof a=="function"?a(i):a}function iy(a,i){const{type:r="all",exact:s,fetchStatus:o,predicate:f,queryKey:h,stale:g}=a;if(h){if(s){if(i.queryHash!==ko(h,i.options))return!1}else if(!Ki(i.queryKey,h))return!1}if(r!=="all"){const p=i.isActive();if(r==="active"&&!p||r==="inactive"&&p)return!1}return!(typeof g=="boolean"&&i.isStale()!==g||o&&o!==i.state.fetchStatus||f&&!f(i))}function uy(a,i){const{exact:r,status:s,predicate:o,mutationKey:f}=a;if(f){if(!i.options.mutationKey)return!1;if(r){if(Zi(i.options.mutationKey)!==Zi(f))return!1}else if(!Ki(i.options.mutationKey,f))return!1}return!(s&&i.state.status!==s||o&&!o(i))}function ko(a,i){return((i==null?void 0:i.queryKeyHashFn)||Zi)(a)}function Zi(a){return JSON.stringify(a,(i,r)=>Eo(r)?Object.keys(r).sort().reduce((s,o)=>(s[o]=r[o],s),{}):r)}function Ki(a,i){return a===i?!0:typeof a!=typeof i?!1:a&&i&&typeof a=="object"&&typeof i=="object"?Object.keys(i).every(r=>Ki(a[r],i[r])):!1}function tp(a,i){if(a===i)return a;const r=ry(a)&&ry(i);if(r||Eo(a)&&Eo(i)){const s=r?a:Object.keys(a),o=s.length,f=r?i:Object.keys(i),h=f.length,g=r?[]:{};let p=0;for(let m=0;m<h;m++){const v=r?m:f[m];(!r&&s.includes(v)||r)&&a[v]===void 0&&i[v]===void 0?(g[v]=void 0,p++):(g[v]=tp(a[v],i[v]),g[v]===a[v]&&a[v]!==void 0&&p++)}return o===h&&p===o?a:g}return i}function ry(a){return Array.isArray(a)&&a.length===Object.keys(a).length}function Eo(a){if(!sy(a))return!1;const i=a.constructor;if(i===void 0)return!0;const r=i.prototype;return!(!sy(r)||!r.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(a)!==Object.prototype)}function sy(a){return Object.prototype.toString.call(a)==="[object Object]"}function Ib(a){return new Promise(i=>{setTimeout(i,a)})}function e1(a,i,r){return typeof r.structuralSharing=="function"?r.structuralSharing(a,i):r.structuralSharing!==!1?tp(a,i):i}function t1(a,i,r=0){const s=[...a,i];return r&&s.length>r?s.slice(1):s}function n1(a,i,r=0){const s=[i,...a];return r&&s.length>r?s.slice(0,-1):s}var Go=Symbol();function np(a,i){return!a.queryFn&&(i!=null&&i.initialPromise)?()=>i.initialPromise:!a.queryFn||a.queryFn===Go?()=>Promise.reject(new Error(`Missing queryFn: '${a.queryHash}'`)):a.queryFn}var Dl,ul,wa,zy,l1=(zy=class extends Ur{constructor(){super();Ee(this,Dl);Ee(this,ul);Ee(this,wa);ce(this,wa,i=>{if(!jr&&window.addEventListener){const r=()=>i();return window.addEventListener("visibilitychange",r,!1),()=>{window.removeEventListener("visibilitychange",r)}}})}onSubscribe(){k(this,ul)||this.setEventListener(k(this,wa))}onUnsubscribe(){var i;this.hasListeners()||((i=k(this,ul))==null||i.call(this),ce(this,ul,void 0))}setEventListener(i){var r;ce(this,wa,i),(r=k(this,ul))==null||r.call(this),ce(this,ul,i(s=>{typeof s=="boolean"?this.setFocused(s):this.onFocus()}))}setFocused(i){k(this,Dl)!==i&&(ce(this,Dl,i),this.onFocus())}onFocus(){const i=this.isFocused();this.listeners.forEach(r=>{r(i)})}isFocused(){var i;return typeof k(this,Dl)=="boolean"?k(this,Dl):((i=globalThis.document)==null?void 0:i.visibilityState)!=="hidden"}},Dl=new WeakMap,ul=new WeakMap,wa=new WeakMap,zy),lp=new l1,Oa,rl,Na,_y,a1=(_y=class extends Ur{constructor(){super();Ee(this,Oa,!0);Ee(this,rl);Ee(this,Na);ce(this,Na,i=>{if(!jr&&window.addEventListener){const r=()=>i(!0),s=()=>i(!1);return window.addEventListener("online",r,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",r),window.removeEventListener("offline",s)}}})}onSubscribe(){k(this,rl)||this.setEventListener(k(this,Na))}onUnsubscribe(){var i;this.hasListeners()||((i=k(this,rl))==null||i.call(this),ce(this,rl,void 0))}setEventListener(i){var r;ce(this,Na,i),(r=k(this,rl))==null||r.call(this),ce(this,rl,i(this.setOnline.bind(this)))}setOnline(i){k(this,Oa)!==i&&(ce(this,Oa,i),this.listeners.forEach(s=>{s(i)}))}isOnline(){return k(this,Oa)}},Oa=new WeakMap,rl=new WeakMap,Na=new WeakMap,_y),Nr=new a1;function i1(){let a,i;const r=new Promise((o,f)=>{a=o,i=f});r.status="pending",r.catch(()=>{});function s(o){Object.assign(r,o),delete r.resolve,delete r.reject}return r.resolve=o=>{s({status:"fulfilled",value:o}),a(o)},r.reject=o=>{s({status:"rejected",reason:o}),i(o)},r}function u1(a){return Math.min(1e3*2**a,3e4)}function ap(a){return(a??"online")==="online"?Nr.isOnline():!0}var ip=class extends Error{constructor(a){super("CancelledError"),this.revert=a==null?void 0:a.revert,this.silent=a==null?void 0:a.silent}};function ho(a){return a instanceof ip}function up(a){let i=!1,r=0,s=!1,o;const f=i1(),h=M=>{var C;s||(T(new ip(M)),(C=a.abort)==null||C.call(a))},g=()=>{i=!0},p=()=>{i=!1},m=()=>lp.isFocused()&&(a.networkMode==="always"||Nr.isOnline())&&a.canRun(),v=()=>ap(a.networkMode)&&a.canRun(),E=M=>{var C;s||(s=!0,(C=a.onSuccess)==null||C.call(a,M),o==null||o(),f.resolve(M))},T=M=>{var C;s||(s=!0,(C=a.onError)==null||C.call(a,M),o==null||o(),f.reject(M))},B=()=>new Promise(M=>{var C;o=H=>{(s||m())&&M(H)},(C=a.onPause)==null||C.call(a)}).then(()=>{var M;o=void 0,s||(M=a.onContinue)==null||M.call(a)}),A=()=>{if(s)return;let M;const C=r===0?a.initialPromise:void 0;try{M=C??a.fn()}catch(H){M=Promise.reject(H)}Promise.resolve(M).then(E).catch(H=>{var ue;if(s)return;const Z=a.retry??(jr?0:3),Y=a.retryDelay??u1,P=typeof Y=="function"?Y(r,H):Y,K=Z===!0||typeof Z=="number"&&r<Z||typeof Z=="function"&&Z(r,H);if(i||!K){T(H);return}r++,(ue=a.onFail)==null||ue.call(a,r,H),Ib(P).then(()=>m()?void 0:B()).then(()=>{i?T(H):A()})})};return{promise:f,cancel:h,continue:()=>(o==null||o(),f),cancelRetry:g,continueRetry:p,canStart:v,start:()=>(v()?A():B().then(A),f)}}var r1=a=>setTimeout(a,0);function s1(){let a=[],i=0,r=g=>{g()},s=g=>{g()},o=r1;const f=g=>{i?a.push(g):o(()=>{r(g)})},h=()=>{const g=a;a=[],g.length&&o(()=>{s(()=>{g.forEach(p=>{r(p)})})})};return{batch:g=>{let p;i++;try{p=g()}finally{i--,i||h()}return p},batchCalls:g=>(...p)=>{f(()=>{g(...p)})},schedule:f,setNotifyFunction:g=>{r=g},setBatchNotifyFunction:g=>{s=g},setScheduler:g=>{o=g}}}var dt=s1(),zl,Uy,rp=(Uy=class{constructor(){Ee(this,zl)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),$b(this.gcTime)&&ce(this,zl,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(a){this.gcTime=Math.max(this.gcTime||0,a??(jr?1/0:5*60*1e3))}clearGcTimeout(){k(this,zl)&&(clearTimeout(k(this,zl)),ce(this,zl,void 0))}},zl=new WeakMap,Uy),Ca,Ma,Lt,_l,rt,Fi,Ul,Xt,wn,jy,c1=(jy=class extends rp{constructor(i){super();Ee(this,Xt);Ee(this,Ca);Ee(this,Ma);Ee(this,Lt);Ee(this,_l);Ee(this,rt);Ee(this,Fi);Ee(this,Ul);ce(this,Ul,!1),ce(this,Fi,i.defaultOptions),this.setOptions(i.options),this.observers=[],ce(this,_l,i.client),ce(this,Lt,k(this,_l).getQueryCache()),this.queryKey=i.queryKey,this.queryHash=i.queryHash,ce(this,Ca,f1(this.options)),this.state=i.state??k(this,Ca),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var i;return(i=k(this,rt))==null?void 0:i.promise}setOptions(i){this.options={...k(this,Fi),...i},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&k(this,Lt).remove(this)}setData(i,r){const s=e1(this.state.data,i,this.options);return it(this,Xt,wn).call(this,{data:s,type:"success",dataUpdatedAt:r==null?void 0:r.updatedAt,manual:r==null?void 0:r.manual}),s}setState(i,r){it(this,Xt,wn).call(this,{type:"setState",state:i,setStateOptions:r})}cancel(i){var s,o;const r=(s=k(this,rt))==null?void 0:s.promise;return(o=k(this,rt))==null||o.cancel(i),r?r.then(Vt).catch(Vt):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(k(this,Ca))}isActive(){return this.observers.some(i=>Pb(i.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===Go||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(i=>i.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(i=0){return this.state.isInvalidated||this.state.data===void 0||!Wb(this.state.dataUpdatedAt,i)}onFocus(){var r;const i=this.observers.find(s=>s.shouldFetchOnWindowFocus());i==null||i.refetch({cancelRefetch:!1}),(r=k(this,rt))==null||r.continue()}onOnline(){var r;const i=this.observers.find(s=>s.shouldFetchOnReconnect());i==null||i.refetch({cancelRefetch:!1}),(r=k(this,rt))==null||r.continue()}addObserver(i){this.observers.includes(i)||(this.observers.push(i),this.clearGcTimeout(),k(this,Lt).notify({type:"observerAdded",query:this,observer:i}))}removeObserver(i){this.observers.includes(i)&&(this.observers=this.observers.filter(r=>r!==i),this.observers.length||(k(this,rt)&&(k(this,Ul)?k(this,rt).cancel({revert:!0}):k(this,rt).cancelRetry()),this.scheduleGc()),k(this,Lt).notify({type:"observerRemoved",query:this,observer:i}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||it(this,Xt,wn).call(this,{type:"invalidate"})}fetch(i,r){var p,m,v;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(r!=null&&r.cancelRefetch))this.cancel({silent:!0});else if(k(this,rt))return k(this,rt).continueRetry(),k(this,rt).promise}if(i&&this.setOptions(i),!this.options.queryFn){const E=this.observers.find(T=>T.options.queryFn);E&&this.setOptions(E.options)}const s=new AbortController,o=E=>{Object.defineProperty(E,"signal",{enumerable:!0,get:()=>(ce(this,Ul,!0),s.signal)})},f=()=>{const E=np(this.options,r),T={client:k(this,_l),queryKey:this.queryKey,meta:this.meta};return o(T),ce(this,Ul,!1),this.options.persister?this.options.persister(E,T,this):E(T)},h={fetchOptions:r,options:this.options,queryKey:this.queryKey,client:k(this,_l),state:this.state,fetchFn:f};o(h),(p=this.options.behavior)==null||p.onFetch(h,this),ce(this,Ma,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((m=h.fetchOptions)==null?void 0:m.meta))&&it(this,Xt,wn).call(this,{type:"fetch",meta:(v=h.fetchOptions)==null?void 0:v.meta});const g=E=>{var T,B,A,M;ho(E)&&E.silent||it(this,Xt,wn).call(this,{type:"error",error:E}),ho(E)||((B=(T=k(this,Lt).config).onError)==null||B.call(T,E,this),(M=(A=k(this,Lt).config).onSettled)==null||M.call(A,this.state.data,E,this)),this.scheduleGc()};return ce(this,rt,up({initialPromise:r==null?void 0:r.initialPromise,fn:h.fetchFn,abort:s.abort.bind(s),onSuccess:E=>{var T,B,A,M;if(E===void 0){g(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(E)}catch(C){g(C);return}(B=(T=k(this,Lt).config).onSuccess)==null||B.call(T,E,this),(M=(A=k(this,Lt).config).onSettled)==null||M.call(A,E,this.state.error,this),this.scheduleGc()},onError:g,onFail:(E,T)=>{it(this,Xt,wn).call(this,{type:"failed",failureCount:E,error:T})},onPause:()=>{it(this,Xt,wn).call(this,{type:"pause"})},onContinue:()=>{it(this,Xt,wn).call(this,{type:"continue"})},retry:h.options.retry,retryDelay:h.options.retryDelay,networkMode:h.options.networkMode,canRun:()=>!0})),k(this,rt).start()}},Ca=new WeakMap,Ma=new WeakMap,Lt=new WeakMap,_l=new WeakMap,rt=new WeakMap,Fi=new WeakMap,Ul=new WeakMap,Xt=new WeakSet,wn=function(i){const r=s=>{switch(i.type){case"failed":return{...s,fetchFailureCount:i.failureCount,fetchFailureReason:i.error};case"pause":return{...s,fetchStatus:"paused"};case"continue":return{...s,fetchStatus:"fetching"};case"fetch":return{...s,...o1(s.data,this.options),fetchMeta:i.meta??null};case"success":return{...s,data:i.data,dataUpdateCount:s.dataUpdateCount+1,dataUpdatedAt:i.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!i.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const o=i.error;return ho(o)&&o.revert&&k(this,Ma)?{...k(this,Ma),fetchStatus:"idle"}:{...s,error:o,errorUpdateCount:s.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:s.fetchFailureCount+1,fetchFailureReason:o,fetchStatus:"idle",status:"error"};case"invalidate":return{...s,isInvalidated:!0};case"setState":return{...s,...i.state}}};this.state=r(this.state),dt.batch(()=>{this.observers.forEach(s=>{s.onQueryUpdate()}),k(this,Lt).notify({query:this,type:"updated",action:i})})},jy);function o1(a,i){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:ap(i.networkMode)?"fetching":"paused",...a===void 0&&{error:null,status:"pending"}}}function f1(a){const i=typeof a.initialData=="function"?a.initialData():a.initialData,r=i!==void 0,s=r?typeof a.initialDataUpdatedAt=="function"?a.initialDataUpdatedAt():a.initialDataUpdatedAt:0;return{data:i,dataUpdateCount:0,dataUpdatedAt:r?s??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}var un,Hy,d1=(Hy=class extends Ur{constructor(i={}){super();Ee(this,un);this.config=i,ce(this,un,new Map)}build(i,r,s){const o=r.queryKey,f=r.queryHash??ko(o,r);let h=this.get(f);return h||(h=new c1({client:i,queryKey:o,queryHash:f,options:i.defaultQueryOptions(r),state:s,defaultOptions:i.getQueryDefaults(o)}),this.add(h)),h}add(i){k(this,un).has(i.queryHash)||(k(this,un).set(i.queryHash,i),this.notify({type:"added",query:i}))}remove(i){const r=k(this,un).get(i.queryHash);r&&(i.destroy(),r===i&&k(this,un).delete(i.queryHash),this.notify({type:"removed",query:i}))}clear(){dt.batch(()=>{this.getAll().forEach(i=>{this.remove(i)})})}get(i){return k(this,un).get(i)}getAll(){return[...k(this,un).values()]}find(i){const r={exact:!0,...i};return this.getAll().find(s=>iy(r,s))}findAll(i={}){const r=this.getAll();return Object.keys(i).length>0?r.filter(s=>iy(i,s)):r}notify(i){dt.batch(()=>{this.listeners.forEach(r=>{r(i)})})}onFocus(){dt.batch(()=>{this.getAll().forEach(i=>{i.onFocus()})})}onOnline(){dt.batch(()=>{this.getAll().forEach(i=>{i.onOnline()})})}},un=new WeakMap,Hy),rn,ft,jl,sn,il,qy,h1=(qy=class extends rp{constructor(i){super();Ee(this,sn);Ee(this,rn);Ee(this,ft);Ee(this,jl);this.mutationId=i.mutationId,ce(this,ft,i.mutationCache),ce(this,rn,[]),this.state=i.state||m1(),this.setOptions(i.options),this.scheduleGc()}setOptions(i){this.options=i,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(i){k(this,rn).includes(i)||(k(this,rn).push(i),this.clearGcTimeout(),k(this,ft).notify({type:"observerAdded",mutation:this,observer:i}))}removeObserver(i){ce(this,rn,k(this,rn).filter(r=>r!==i)),this.scheduleGc(),k(this,ft).notify({type:"observerRemoved",mutation:this,observer:i})}optionalRemove(){k(this,rn).length||(this.state.status==="pending"?this.scheduleGc():k(this,ft).remove(this))}continue(){var i;return((i=k(this,jl))==null?void 0:i.continue())??this.execute(this.state.variables)}async execute(i){var f,h,g,p,m,v,E,T,B,A,M,C,H,Z,Y,P,K,ue,fe,F;const r=()=>{it(this,sn,il).call(this,{type:"continue"})};ce(this,jl,up({fn:()=>this.options.mutationFn?this.options.mutationFn(i):Promise.reject(new Error("No mutationFn found")),onFail:(le,je)=>{it(this,sn,il).call(this,{type:"failed",failureCount:le,error:je})},onPause:()=>{it(this,sn,il).call(this,{type:"pause"})},onContinue:r,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>k(this,ft).canRun(this)}));const s=this.state.status==="pending",o=!k(this,jl).canStart();try{if(s)r();else{it(this,sn,il).call(this,{type:"pending",variables:i,isPaused:o}),await((h=(f=k(this,ft).config).onMutate)==null?void 0:h.call(f,i,this));const je=await((p=(g=this.options).onMutate)==null?void 0:p.call(g,i));je!==this.state.context&&it(this,sn,il).call(this,{type:"pending",context:je,variables:i,isPaused:o})}const le=await k(this,jl).start();return await((v=(m=k(this,ft).config).onSuccess)==null?void 0:v.call(m,le,i,this.state.context,this)),await((T=(E=this.options).onSuccess)==null?void 0:T.call(E,le,i,this.state.context)),await((A=(B=k(this,ft).config).onSettled)==null?void 0:A.call(B,le,null,this.state.variables,this.state.context,this)),await((C=(M=this.options).onSettled)==null?void 0:C.call(M,le,null,i,this.state.context)),it(this,sn,il).call(this,{type:"success",data:le}),le}catch(le){try{throw await((Z=(H=k(this,ft).config).onError)==null?void 0:Z.call(H,le,i,this.state.context,this)),await((P=(Y=this.options).onError)==null?void 0:P.call(Y,le,i,this.state.context)),await((ue=(K=k(this,ft).config).onSettled)==null?void 0:ue.call(K,void 0,le,this.state.variables,this.state.context,this)),await((F=(fe=this.options).onSettled)==null?void 0:F.call(fe,void 0,le,i,this.state.context)),le}finally{it(this,sn,il).call(this,{type:"error",error:le})}}finally{k(this,ft).runNext(this)}}},rn=new WeakMap,ft=new WeakMap,jl=new WeakMap,sn=new WeakSet,il=function(i){const r=s=>{switch(i.type){case"failed":return{...s,failureCount:i.failureCount,failureReason:i.error};case"pause":return{...s,isPaused:!0};case"continue":return{...s,isPaused:!1};case"pending":return{...s,context:i.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:i.isPaused,status:"pending",variables:i.variables,submittedAt:Date.now()};case"success":return{...s,data:i.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...s,data:void 0,error:i.error,failureCount:s.failureCount+1,failureReason:i.error,isPaused:!1,status:"error"}}};this.state=r(this.state),dt.batch(()=>{k(this,rn).forEach(s=>{s.onMutationUpdate(i)}),k(this,ft).notify({mutation:this,type:"updated",action:i})})},qy);function m1(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var On,Zt,$i,By,y1=(By=class extends Ur{constructor(i={}){super();Ee(this,On);Ee(this,Zt);Ee(this,$i);this.config=i,ce(this,On,new Set),ce(this,Zt,new Map),ce(this,$i,0)}build(i,r,s){const o=new h1({mutationCache:this,mutationId:++pr(this,$i)._,options:i.defaultMutationOptions(r),state:s});return this.add(o),o}add(i){k(this,On).add(i);const r=vr(i);if(typeof r=="string"){const s=k(this,Zt).get(r);s?s.push(i):k(this,Zt).set(r,[i])}this.notify({type:"added",mutation:i})}remove(i){if(k(this,On).delete(i)){const r=vr(i);if(typeof r=="string"){const s=k(this,Zt).get(r);if(s)if(s.length>1){const o=s.indexOf(i);o!==-1&&s.splice(o,1)}else s[0]===i&&k(this,Zt).delete(r)}}this.notify({type:"removed",mutation:i})}canRun(i){const r=vr(i);if(typeof r=="string"){const s=k(this,Zt).get(r),o=s==null?void 0:s.find(f=>f.state.status==="pending");return!o||o===i}else return!0}runNext(i){var s;const r=vr(i);if(typeof r=="string"){const o=(s=k(this,Zt).get(r))==null?void 0:s.find(f=>f!==i&&f.state.isPaused);return(o==null?void 0:o.continue())??Promise.resolve()}else return Promise.resolve()}clear(){dt.batch(()=>{k(this,On).forEach(i=>{this.notify({type:"removed",mutation:i})}),k(this,On).clear(),k(this,Zt).clear()})}getAll(){return Array.from(k(this,On))}find(i){const r={exact:!0,...i};return this.getAll().find(s=>uy(r,s))}findAll(i={}){return this.getAll().filter(r=>uy(i,r))}notify(i){dt.batch(()=>{this.listeners.forEach(r=>{r(i)})})}resumePausedMutations(){const i=this.getAll().filter(r=>r.state.isPaused);return dt.batch(()=>Promise.all(i.map(r=>r.continue().catch(Vt))))}},On=new WeakMap,Zt=new WeakMap,$i=new WeakMap,By);function vr(a){var i;return(i=a.options.scope)==null?void 0:i.id}function cy(a){return{onFetch:(i,r)=>{var v,E,T,B,A;const s=i.options,o=(T=(E=(v=i.fetchOptions)==null?void 0:v.meta)==null?void 0:E.fetchMore)==null?void 0:T.direction,f=((B=i.state.data)==null?void 0:B.pages)||[],h=((A=i.state.data)==null?void 0:A.pageParams)||[];let g={pages:[],pageParams:[]},p=0;const m=async()=>{let M=!1;const C=Y=>{Object.defineProperty(Y,"signal",{enumerable:!0,get:()=>(i.signal.aborted?M=!0:i.signal.addEventListener("abort",()=>{M=!0}),i.signal)})},H=np(i.options,i.fetchOptions),Z=async(Y,P,K)=>{if(M)return Promise.reject();if(P==null&&Y.pages.length)return Promise.resolve(Y);const ue={client:i.client,queryKey:i.queryKey,pageParam:P,direction:K?"backward":"forward",meta:i.options.meta};C(ue);const fe=await H(ue),{maxPages:F}=i.options,le=K?n1:t1;return{pages:le(Y.pages,fe,F),pageParams:le(Y.pageParams,P,F)}};if(o&&f.length){const Y=o==="backward",P=Y?p1:oy,K={pages:f,pageParams:h},ue=P(s,K);g=await Z(K,ue,Y)}else{const Y=a??f.length;do{const P=p===0?h[0]??s.initialPageParam:oy(s,g);if(p>0&&P==null)break;g=await Z(g,P),p++}while(p<Y)}return g};i.options.persister?i.fetchFn=()=>{var M,C;return(C=(M=i.options).persister)==null?void 0:C.call(M,m,{client:i.client,queryKey:i.queryKey,meta:i.options.meta,signal:i.signal},r)}:i.fetchFn=m}}}function oy(a,{pages:i,pageParams:r}){const s=i.length-1;return i.length>0?a.getNextPageParam(i[s],i,r[s],r):void 0}function p1(a,{pages:i,pageParams:r}){var s;return i.length>0?(s=a.getPreviousPageParam)==null?void 0:s.call(a,i[0],i,r[0],r):void 0}var ke,sl,cl,Da,za,ol,_a,Ua,Ly,g1=(Ly=class{constructor(a={}){Ee(this,ke);Ee(this,sl);Ee(this,cl);Ee(this,Da);Ee(this,za);Ee(this,ol);Ee(this,_a);Ee(this,Ua);ce(this,ke,a.queryCache||new d1),ce(this,sl,a.mutationCache||new y1),ce(this,cl,a.defaultOptions||{}),ce(this,Da,new Map),ce(this,za,new Map),ce(this,ol,0)}mount(){pr(this,ol)._++,k(this,ol)===1&&(ce(this,_a,lp.subscribe(async a=>{a&&(await this.resumePausedMutations(),k(this,ke).onFocus())})),ce(this,Ua,Nr.subscribe(async a=>{a&&(await this.resumePausedMutations(),k(this,ke).onOnline())})))}unmount(){var a,i;pr(this,ol)._--,k(this,ol)===0&&((a=k(this,_a))==null||a.call(this),ce(this,_a,void 0),(i=k(this,Ua))==null||i.call(this),ce(this,Ua,void 0))}isFetching(a){return k(this,ke).findAll({...a,fetchStatus:"fetching"}).length}isMutating(a){return k(this,sl).findAll({...a,status:"pending"}).length}getQueryData(a){var r;const i=this.defaultQueryOptions({queryKey:a});return(r=k(this,ke).get(i.queryHash))==null?void 0:r.state.data}ensureQueryData(a){const i=this.defaultQueryOptions(a),r=k(this,ke).build(this,i),s=r.state.data;return s===void 0?this.fetchQuery(a):(a.revalidateIfStale&&r.isStaleByTime(ay(i.staleTime,r))&&this.prefetchQuery(i),Promise.resolve(s))}getQueriesData(a){return k(this,ke).findAll(a).map(({queryKey:i,state:r})=>{const s=r.data;return[i,s]})}setQueryData(a,i,r){const s=this.defaultQueryOptions({queryKey:a}),o=k(this,ke).get(s.queryHash),f=o==null?void 0:o.state.data,h=Fb(i,f);if(h!==void 0)return k(this,ke).build(this,s).setData(h,{...r,manual:!0})}setQueriesData(a,i,r){return dt.batch(()=>k(this,ke).findAll(a).map(({queryKey:s})=>[s,this.setQueryData(s,i,r)]))}getQueryState(a){var r;const i=this.defaultQueryOptions({queryKey:a});return(r=k(this,ke).get(i.queryHash))==null?void 0:r.state}removeQueries(a){const i=k(this,ke);dt.batch(()=>{i.findAll(a).forEach(r=>{i.remove(r)})})}resetQueries(a,i){const r=k(this,ke);return dt.batch(()=>(r.findAll(a).forEach(s=>{s.reset()}),this.refetchQueries({type:"active",...a},i)))}cancelQueries(a,i={}){const r={revert:!0,...i},s=dt.batch(()=>k(this,ke).findAll(a).map(o=>o.cancel(r)));return Promise.all(s).then(Vt).catch(Vt)}invalidateQueries(a,i={}){return dt.batch(()=>(k(this,ke).findAll(a).forEach(r=>{r.invalidate()}),(a==null?void 0:a.refetchType)==="none"?Promise.resolve():this.refetchQueries({...a,type:(a==null?void 0:a.refetchType)??(a==null?void 0:a.type)??"active"},i)))}refetchQueries(a,i={}){const r={...i,cancelRefetch:i.cancelRefetch??!0},s=dt.batch(()=>k(this,ke).findAll(a).filter(o=>!o.isDisabled()).map(o=>{let f=o.fetch(void 0,r);return r.throwOnError||(f=f.catch(Vt)),o.state.fetchStatus==="paused"?Promise.resolve():f}));return Promise.all(s).then(Vt)}fetchQuery(a){const i=this.defaultQueryOptions(a);i.retry===void 0&&(i.retry=!1);const r=k(this,ke).build(this,i);return r.isStaleByTime(ay(i.staleTime,r))?r.fetch(i):Promise.resolve(r.state.data)}prefetchQuery(a){return this.fetchQuery(a).then(Vt).catch(Vt)}fetchInfiniteQuery(a){return a.behavior=cy(a.pages),this.fetchQuery(a)}prefetchInfiniteQuery(a){return this.fetchInfiniteQuery(a).then(Vt).catch(Vt)}ensureInfiniteQueryData(a){return a.behavior=cy(a.pages),this.ensureQueryData(a)}resumePausedMutations(){return Nr.isOnline()?k(this,sl).resumePausedMutations():Promise.resolve()}getQueryCache(){return k(this,ke)}getMutationCache(){return k(this,sl)}getDefaultOptions(){return k(this,cl)}setDefaultOptions(a){ce(this,cl,a)}setQueryDefaults(a,i){k(this,Da).set(Zi(a),{queryKey:a,defaultOptions:i})}getQueryDefaults(a){const i=[...k(this,Da).values()],r={};return i.forEach(s=>{Ki(a,s.queryKey)&&Object.assign(r,s.defaultOptions)}),r}setMutationDefaults(a,i){k(this,za).set(Zi(a),{mutationKey:a,defaultOptions:i})}getMutationDefaults(a){const i=[...k(this,za).values()],r={};return i.forEach(s=>{Ki(a,s.mutationKey)&&Object.assign(r,s.defaultOptions)}),r}defaultQueryOptions(a){if(a._defaulted)return a;const i={...k(this,cl).queries,...this.getQueryDefaults(a.queryKey),...a,_defaulted:!0};return i.queryHash||(i.queryHash=ko(i.queryKey,i)),i.refetchOnReconnect===void 0&&(i.refetchOnReconnect=i.networkMode!=="always"),i.throwOnError===void 0&&(i.throwOnError=!!i.suspense),!i.networkMode&&i.persister&&(i.networkMode="offlineFirst"),i.queryFn===Go&&(i.enabled=!1),i}defaultMutationOptions(a){return a!=null&&a._defaulted?a:{...k(this,cl).mutations,...(a==null?void 0:a.mutationKey)&&this.getMutationDefaults(a.mutationKey),...a,_defaulted:!0}}clear(){k(this,ke).clear(),k(this,sl).clear()}},ke=new WeakMap,sl=new WeakMap,cl=new WeakMap,Da=new WeakMap,za=new WeakMap,ol=new WeakMap,_a=new WeakMap,Ua=new WeakMap,Ly),v1=_.createContext(void 0),b1=({client:a,children:i})=>(_.useEffect(()=>(a.mount(),()=>{a.unmount()}),[a]),O.jsx(v1.Provider,{value:a,children:i}));function sp(a,i){return function(){return a.apply(i,arguments)}}const{toString:S1}=Object.prototype,{getPrototypeOf:Yo}=Object,{iterator:Hr,toStringTag:cp}=Symbol,qr=(a=>i=>{const r=S1.call(i);return a[r]||(a[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),$t=a=>(a=a.toLowerCase(),i=>qr(i)===a),Br=a=>i=>typeof i===a,{isArray:Ba}=Array,Ji=Br("undefined");function x1(a){return a!==null&&!Ji(a)&&a.constructor!==null&&!Ji(a.constructor)&&vt(a.constructor.isBuffer)&&a.constructor.isBuffer(a)}const op=$t("ArrayBuffer");function E1(a){let i;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?i=ArrayBuffer.isView(a):i=a&&a.buffer&&op(a.buffer),i}const A1=Br("string"),vt=Br("function"),fp=Br("number"),Lr=a=>a!==null&&typeof a=="object",R1=a=>a===!0||a===!1,Rr=a=>{if(qr(a)!=="object")return!1;const i=Yo(a);return(i===null||i===Object.prototype||Object.getPrototypeOf(i)===null)&&!(cp in a)&&!(Hr in a)},T1=$t("Date"),w1=$t("File"),O1=$t("Blob"),N1=$t("FileList"),C1=a=>Lr(a)&&vt(a.pipe),M1=a=>{let i;return a&&(typeof FormData=="function"&&a instanceof FormData||vt(a.append)&&((i=qr(a))==="formdata"||i==="object"&&vt(a.toString)&&a.toString()==="[object FormData]"))},D1=$t("URLSearchParams"),[z1,_1,U1,j1]=["ReadableStream","Request","Response","Headers"].map($t),H1=a=>a.trim?a.trim():a.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function eu(a,i,{allOwnKeys:r=!1}={}){if(a===null||typeof a>"u")return;let s,o;if(typeof a!="object"&&(a=[a]),Ba(a))for(s=0,o=a.length;s<o;s++)i.call(null,a[s],s,a);else{const f=r?Object.getOwnPropertyNames(a):Object.keys(a),h=f.length;let g;for(s=0;s<h;s++)g=f[s],i.call(null,a[g],g,a)}}function dp(a,i){i=i.toLowerCase();const r=Object.keys(a);let s=r.length,o;for(;s-- >0;)if(o=r[s],i===o.toLowerCase())return o;return null}const Ml=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,hp=a=>!Ji(a)&&a!==Ml;function Ao(){const{caseless:a}=hp(this)&&this||{},i={},r=(s,o)=>{const f=a&&dp(i,o)||o;Rr(i[f])&&Rr(s)?i[f]=Ao(i[f],s):Rr(s)?i[f]=Ao({},s):Ba(s)?i[f]=s.slice():i[f]=s};for(let s=0,o=arguments.length;s<o;s++)arguments[s]&&eu(arguments[s],r);return i}const q1=(a,i,r,{allOwnKeys:s}={})=>(eu(i,(o,f)=>{r&&vt(o)?a[f]=sp(o,r):a[f]=o},{allOwnKeys:s}),a),B1=a=>(a.charCodeAt(0)===65279&&(a=a.slice(1)),a),L1=(a,i,r,s)=>{a.prototype=Object.create(i.prototype,s),a.prototype.constructor=a,Object.defineProperty(a,"super",{value:i.prototype}),r&&Object.assign(a.prototype,r)},k1=(a,i,r,s)=>{let o,f,h;const g={};if(i=i||{},a==null)return i;do{for(o=Object.getOwnPropertyNames(a),f=o.length;f-- >0;)h=o[f],(!s||s(h,a,i))&&!g[h]&&(i[h]=a[h],g[h]=!0);a=r!==!1&&Yo(a)}while(a&&(!r||r(a,i))&&a!==Object.prototype);return i},G1=(a,i,r)=>{a=String(a),(r===void 0||r>a.length)&&(r=a.length),r-=i.length;const s=a.indexOf(i,r);return s!==-1&&s===r},Y1=a=>{if(!a)return null;if(Ba(a))return a;let i=a.length;if(!fp(i))return null;const r=new Array(i);for(;i-- >0;)r[i]=a[i];return r},Q1=(a=>i=>a&&i instanceof a)(typeof Uint8Array<"u"&&Yo(Uint8Array)),V1=(a,i)=>{const s=(a&&a[Hr]).call(a);let o;for(;(o=s.next())&&!o.done;){const f=o.value;i.call(a,f[0],f[1])}},X1=(a,i)=>{let r;const s=[];for(;(r=a.exec(i))!==null;)s.push(r);return s},Z1=$t("HTMLFormElement"),K1=a=>a.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,s,o){return s.toUpperCase()+o}),fy=(({hasOwnProperty:a})=>(i,r)=>a.call(i,r))(Object.prototype),J1=$t("RegExp"),mp=(a,i)=>{const r=Object.getOwnPropertyDescriptors(a),s={};eu(r,(o,f)=>{let h;(h=i(o,f,a))!==!1&&(s[f]=h||o)}),Object.defineProperties(a,s)},F1=a=>{mp(a,(i,r)=>{if(vt(a)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const s=a[r];if(vt(s)){if(i.enumerable=!1,"writable"in i){i.writable=!1;return}i.set||(i.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},$1=(a,i)=>{const r={},s=o=>{o.forEach(f=>{r[f]=!0})};return Ba(a)?s(a):s(String(a).split(i)),r},W1=()=>{},P1=(a,i)=>a!=null&&Number.isFinite(a=+a)?a:i;function I1(a){return!!(a&&vt(a.append)&&a[cp]==="FormData"&&a[Hr])}const eS=a=>{const i=new Array(10),r=(s,o)=>{if(Lr(s)){if(i.indexOf(s)>=0)return;if(!("toJSON"in s)){i[o]=s;const f=Ba(s)?[]:{};return eu(s,(h,g)=>{const p=r(h,o+1);!Ji(p)&&(f[g]=p)}),i[o]=void 0,f}}return s};return r(a,0)},tS=$t("AsyncFunction"),nS=a=>a&&(Lr(a)||vt(a))&&vt(a.then)&&vt(a.catch),yp=((a,i)=>a?setImmediate:i?((r,s)=>(Ml.addEventListener("message",({source:o,data:f})=>{o===Ml&&f===r&&s.length&&s.shift()()},!1),o=>{s.push(o),Ml.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",vt(Ml.postMessage)),lS=typeof queueMicrotask<"u"?queueMicrotask.bind(Ml):typeof process<"u"&&process.nextTick||yp,aS=a=>a!=null&&vt(a[Hr]),U={isArray:Ba,isArrayBuffer:op,isBuffer:x1,isFormData:M1,isArrayBufferView:E1,isString:A1,isNumber:fp,isBoolean:R1,isObject:Lr,isPlainObject:Rr,isReadableStream:z1,isRequest:_1,isResponse:U1,isHeaders:j1,isUndefined:Ji,isDate:T1,isFile:w1,isBlob:O1,isRegExp:J1,isFunction:vt,isStream:C1,isURLSearchParams:D1,isTypedArray:Q1,isFileList:N1,forEach:eu,merge:Ao,extend:q1,trim:H1,stripBOM:B1,inherits:L1,toFlatObject:k1,kindOf:qr,kindOfTest:$t,endsWith:G1,toArray:Y1,forEachEntry:V1,matchAll:X1,isHTMLForm:Z1,hasOwnProperty:fy,hasOwnProp:fy,reduceDescriptors:mp,freezeMethods:F1,toObjectSet:$1,toCamelCase:K1,noop:W1,toFiniteNumber:P1,findKey:dp,global:Ml,isContextDefined:hp,isSpecCompliantForm:I1,toJSONObject:eS,isAsyncFn:tS,isThenable:nS,setImmediate:yp,asap:lS,isIterable:aS};function oe(a,i,r,s,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=a,this.name="AxiosError",i&&(this.code=i),r&&(this.config=r),s&&(this.request=s),o&&(this.response=o,this.status=o.status?o.status:null)}U.inherits(oe,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:U.toJSONObject(this.config),code:this.code,status:this.status}}});const pp=oe.prototype,gp={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(a=>{gp[a]={value:a}});Object.defineProperties(oe,gp);Object.defineProperty(pp,"isAxiosError",{value:!0});oe.from=(a,i,r,s,o,f)=>{const h=Object.create(pp);return U.toFlatObject(a,h,function(p){return p!==Error.prototype},g=>g!=="isAxiosError"),oe.call(h,a.message,i,r,s,o),h.cause=a,h.name=a.name,f&&Object.assign(h,f),h};const iS=null;function Ro(a){return U.isPlainObject(a)||U.isArray(a)}function vp(a){return U.endsWith(a,"[]")?a.slice(0,-2):a}function dy(a,i,r){return a?a.concat(i).map(function(o,f){return o=vp(o),!r&&f?"["+o+"]":o}).join(r?".":""):i}function uS(a){return U.isArray(a)&&!a.some(Ro)}const rS=U.toFlatObject(U,{},null,function(i){return/^is[A-Z]/.test(i)});function kr(a,i,r){if(!U.isObject(a))throw new TypeError("target must be an object");i=i||new FormData,r=U.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(M,C){return!U.isUndefined(C[M])});const s=r.metaTokens,o=r.visitor||v,f=r.dots,h=r.indexes,p=(r.Blob||typeof Blob<"u"&&Blob)&&U.isSpecCompliantForm(i);if(!U.isFunction(o))throw new TypeError("visitor must be a function");function m(A){if(A===null)return"";if(U.isDate(A))return A.toISOString();if(!p&&U.isBlob(A))throw new oe("Blob is not supported. Use a Buffer instead.");return U.isArrayBuffer(A)||U.isTypedArray(A)?p&&typeof Blob=="function"?new Blob([A]):Buffer.from(A):A}function v(A,M,C){let H=A;if(A&&!C&&typeof A=="object"){if(U.endsWith(M,"{}"))M=s?M:M.slice(0,-2),A=JSON.stringify(A);else if(U.isArray(A)&&uS(A)||(U.isFileList(A)||U.endsWith(M,"[]"))&&(H=U.toArray(A)))return M=vp(M),H.forEach(function(Y,P){!(U.isUndefined(Y)||Y===null)&&i.append(h===!0?dy([M],P,f):h===null?M:M+"[]",m(Y))}),!1}return Ro(A)?!0:(i.append(dy(C,M,f),m(A)),!1)}const E=[],T=Object.assign(rS,{defaultVisitor:v,convertValue:m,isVisitable:Ro});function B(A,M){if(!U.isUndefined(A)){if(E.indexOf(A)!==-1)throw Error("Circular reference detected in "+M.join("."));E.push(A),U.forEach(A,function(H,Z){(!(U.isUndefined(H)||H===null)&&o.call(i,H,U.isString(Z)?Z.trim():Z,M,T))===!0&&B(H,M?M.concat(Z):[Z])}),E.pop()}}if(!U.isObject(a))throw new TypeError("data must be an object");return B(a),i}function hy(a){const i={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(a).replace(/[!'()~]|%20|%00/g,function(s){return i[s]})}function Qo(a,i){this._pairs=[],a&&kr(a,this,i)}const bp=Qo.prototype;bp.append=function(i,r){this._pairs.push([i,r])};bp.toString=function(i){const r=i?function(s){return i.call(this,s,hy)}:hy;return this._pairs.map(function(o){return r(o[0])+"="+r(o[1])},"").join("&")};function sS(a){return encodeURIComponent(a).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Sp(a,i,r){if(!i)return a;const s=r&&r.encode||sS;U.isFunction(r)&&(r={serialize:r});const o=r&&r.serialize;let f;if(o?f=o(i,r):f=U.isURLSearchParams(i)?i.toString():new Qo(i,r).toString(s),f){const h=a.indexOf("#");h!==-1&&(a=a.slice(0,h)),a+=(a.indexOf("?")===-1?"?":"&")+f}return a}class my{constructor(){this.handlers=[]}use(i,r,s){return this.handlers.push({fulfilled:i,rejected:r,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(i){this.handlers[i]&&(this.handlers[i]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(i){U.forEach(this.handlers,function(s){s!==null&&i(s)})}}const xp={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},cS=typeof URLSearchParams<"u"?URLSearchParams:Qo,oS=typeof FormData<"u"?FormData:null,fS=typeof Blob<"u"?Blob:null,dS={isBrowser:!0,classes:{URLSearchParams:cS,FormData:oS,Blob:fS},protocols:["http","https","file","blob","url","data"]},Vo=typeof window<"u"&&typeof document<"u",To=typeof navigator=="object"&&navigator||void 0,hS=Vo&&(!To||["ReactNative","NativeScript","NS"].indexOf(To.product)<0),mS=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",yS=Vo&&window.location.href||"http://localhost",pS=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Vo,hasStandardBrowserEnv:hS,hasStandardBrowserWebWorkerEnv:mS,navigator:To,origin:yS},Symbol.toStringTag,{value:"Module"})),st={...pS,...dS};function gS(a,i){return kr(a,new st.classes.URLSearchParams,Object.assign({visitor:function(r,s,o,f){return st.isNode&&U.isBuffer(r)?(this.append(s,r.toString("base64")),!1):f.defaultVisitor.apply(this,arguments)}},i))}function vS(a){return U.matchAll(/\w+|\[(\w*)]/g,a).map(i=>i[0]==="[]"?"":i[1]||i[0])}function bS(a){const i={},r=Object.keys(a);let s;const o=r.length;let f;for(s=0;s<o;s++)f=r[s],i[f]=a[f];return i}function Ep(a){function i(r,s,o,f){let h=r[f++];if(h==="__proto__")return!0;const g=Number.isFinite(+h),p=f>=r.length;return h=!h&&U.isArray(o)?o.length:h,p?(U.hasOwnProp(o,h)?o[h]=[o[h],s]:o[h]=s,!g):((!o[h]||!U.isObject(o[h]))&&(o[h]=[]),i(r,s,o[h],f)&&U.isArray(o[h])&&(o[h]=bS(o[h])),!g)}if(U.isFormData(a)&&U.isFunction(a.entries)){const r={};return U.forEachEntry(a,(s,o)=>{i(vS(s),o,r,0)}),r}return null}function SS(a,i,r){if(U.isString(a))try{return(i||JSON.parse)(a),U.trim(a)}catch(s){if(s.name!=="SyntaxError")throw s}return(r||JSON.stringify)(a)}const tu={transitional:xp,adapter:["xhr","http","fetch"],transformRequest:[function(i,r){const s=r.getContentType()||"",o=s.indexOf("application/json")>-1,f=U.isObject(i);if(f&&U.isHTMLForm(i)&&(i=new FormData(i)),U.isFormData(i))return o?JSON.stringify(Ep(i)):i;if(U.isArrayBuffer(i)||U.isBuffer(i)||U.isStream(i)||U.isFile(i)||U.isBlob(i)||U.isReadableStream(i))return i;if(U.isArrayBufferView(i))return i.buffer;if(U.isURLSearchParams(i))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),i.toString();let g;if(f){if(s.indexOf("application/x-www-form-urlencoded")>-1)return gS(i,this.formSerializer).toString();if((g=U.isFileList(i))||s.indexOf("multipart/form-data")>-1){const p=this.env&&this.env.FormData;return kr(g?{"files[]":i}:i,p&&new p,this.formSerializer)}}return f||o?(r.setContentType("application/json",!1),SS(i)):i}],transformResponse:[function(i){const r=this.transitional||tu.transitional,s=r&&r.forcedJSONParsing,o=this.responseType==="json";if(U.isResponse(i)||U.isReadableStream(i))return i;if(i&&U.isString(i)&&(s&&!this.responseType||o)){const h=!(r&&r.silentJSONParsing)&&o;try{return JSON.parse(i)}catch(g){if(h)throw g.name==="SyntaxError"?oe.from(g,oe.ERR_BAD_RESPONSE,this,null,this.response):g}}return i}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:st.classes.FormData,Blob:st.classes.Blob},validateStatus:function(i){return i>=200&&i<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};U.forEach(["delete","get","head","post","put","patch"],a=>{tu.headers[a]={}});const xS=U.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),ES=a=>{const i={};let r,s,o;return a&&a.split(`
`).forEach(function(h){o=h.indexOf(":"),r=h.substring(0,o).trim().toLowerCase(),s=h.substring(o+1).trim(),!(!r||i[r]&&xS[r])&&(r==="set-cookie"?i[r]?i[r].push(s):i[r]=[s]:i[r]=i[r]?i[r]+", "+s:s)}),i},yy=Symbol("internals");function Qi(a){return a&&String(a).trim().toLowerCase()}function Tr(a){return a===!1||a==null?a:U.isArray(a)?a.map(Tr):String(a)}function AS(a){const i=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=r.exec(a);)i[s[1]]=s[2];return i}const RS=a=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(a.trim());function mo(a,i,r,s,o){if(U.isFunction(s))return s.call(this,i,r);if(o&&(i=r),!!U.isString(i)){if(U.isString(s))return i.indexOf(s)!==-1;if(U.isRegExp(s))return s.test(i)}}function TS(a){return a.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(i,r,s)=>r.toUpperCase()+s)}function wS(a,i){const r=U.toCamelCase(" "+i);["get","set","has"].forEach(s=>{Object.defineProperty(a,s+r,{value:function(o,f,h){return this[s].call(this,i,o,f,h)},configurable:!0})})}let bt=class{constructor(i){i&&this.set(i)}set(i,r,s){const o=this;function f(g,p,m){const v=Qi(p);if(!v)throw new Error("header name must be a non-empty string");const E=U.findKey(o,v);(!E||o[E]===void 0||m===!0||m===void 0&&o[E]!==!1)&&(o[E||p]=Tr(g))}const h=(g,p)=>U.forEach(g,(m,v)=>f(m,v,p));if(U.isPlainObject(i)||i instanceof this.constructor)h(i,r);else if(U.isString(i)&&(i=i.trim())&&!RS(i))h(ES(i),r);else if(U.isObject(i)&&U.isIterable(i)){let g={},p,m;for(const v of i){if(!U.isArray(v))throw TypeError("Object iterator must return a key-value pair");g[m=v[0]]=(p=g[m])?U.isArray(p)?[...p,v[1]]:[p,v[1]]:v[1]}h(g,r)}else i!=null&&f(r,i,s);return this}get(i,r){if(i=Qi(i),i){const s=U.findKey(this,i);if(s){const o=this[s];if(!r)return o;if(r===!0)return AS(o);if(U.isFunction(r))return r.call(this,o,s);if(U.isRegExp(r))return r.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(i,r){if(i=Qi(i),i){const s=U.findKey(this,i);return!!(s&&this[s]!==void 0&&(!r||mo(this,this[s],s,r)))}return!1}delete(i,r){const s=this;let o=!1;function f(h){if(h=Qi(h),h){const g=U.findKey(s,h);g&&(!r||mo(s,s[g],g,r))&&(delete s[g],o=!0)}}return U.isArray(i)?i.forEach(f):f(i),o}clear(i){const r=Object.keys(this);let s=r.length,o=!1;for(;s--;){const f=r[s];(!i||mo(this,this[f],f,i,!0))&&(delete this[f],o=!0)}return o}normalize(i){const r=this,s={};return U.forEach(this,(o,f)=>{const h=U.findKey(s,f);if(h){r[h]=Tr(o),delete r[f];return}const g=i?TS(f):String(f).trim();g!==f&&delete r[f],r[g]=Tr(o),s[g]=!0}),this}concat(...i){return this.constructor.concat(this,...i)}toJSON(i){const r=Object.create(null);return U.forEach(this,(s,o)=>{s!=null&&s!==!1&&(r[o]=i&&U.isArray(s)?s.join(", "):s)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([i,r])=>i+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(i){return i instanceof this?i:new this(i)}static concat(i,...r){const s=new this(i);return r.forEach(o=>s.set(o)),s}static accessor(i){const s=(this[yy]=this[yy]={accessors:{}}).accessors,o=this.prototype;function f(h){const g=Qi(h);s[g]||(wS(o,h),s[g]=!0)}return U.isArray(i)?i.forEach(f):f(i),this}};bt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);U.reduceDescriptors(bt.prototype,({value:a},i)=>{let r=i[0].toUpperCase()+i.slice(1);return{get:()=>a,set(s){this[r]=s}}});U.freezeMethods(bt);function yo(a,i){const r=this||tu,s=i||r,o=bt.from(s.headers);let f=s.data;return U.forEach(a,function(g){f=g.call(r,f,o.normalize(),i?i.status:void 0)}),o.normalize(),f}function Ap(a){return!!(a&&a.__CANCEL__)}function La(a,i,r){oe.call(this,a??"canceled",oe.ERR_CANCELED,i,r),this.name="CanceledError"}U.inherits(La,oe,{__CANCEL__:!0});function Rp(a,i,r){const s=r.config.validateStatus;!r.status||!s||s(r.status)?a(r):i(new oe("Request failed with status code "+r.status,[oe.ERR_BAD_REQUEST,oe.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function OS(a){const i=/^([-+\w]{1,25})(:?\/\/|:)/.exec(a);return i&&i[1]||""}function NS(a,i){a=a||10;const r=new Array(a),s=new Array(a);let o=0,f=0,h;return i=i!==void 0?i:1e3,function(p){const m=Date.now(),v=s[f];h||(h=m),r[o]=p,s[o]=m;let E=f,T=0;for(;E!==o;)T+=r[E++],E=E%a;if(o=(o+1)%a,o===f&&(f=(f+1)%a),m-h<i)return;const B=v&&m-v;return B?Math.round(T*1e3/B):void 0}}function CS(a,i){let r=0,s=1e3/i,o,f;const h=(m,v=Date.now())=>{r=v,o=null,f&&(clearTimeout(f),f=null),a.apply(null,m)};return[(...m)=>{const v=Date.now(),E=v-r;E>=s?h(m,v):(o=m,f||(f=setTimeout(()=>{f=null,h(o)},s-E)))},()=>o&&h(o)]}const Cr=(a,i,r=3)=>{let s=0;const o=NS(50,250);return CS(f=>{const h=f.loaded,g=f.lengthComputable?f.total:void 0,p=h-s,m=o(p),v=h<=g;s=h;const E={loaded:h,total:g,progress:g?h/g:void 0,bytes:p,rate:m||void 0,estimated:m&&g&&v?(g-h)/m:void 0,event:f,lengthComputable:g!=null,[i?"download":"upload"]:!0};a(E)},r)},py=(a,i)=>{const r=a!=null;return[s=>i[0]({lengthComputable:r,total:a,loaded:s}),i[1]]},gy=a=>(...i)=>U.asap(()=>a(...i)),MS=st.hasStandardBrowserEnv?((a,i)=>r=>(r=new URL(r,st.origin),a.protocol===r.protocol&&a.host===r.host&&(i||a.port===r.port)))(new URL(st.origin),st.navigator&&/(msie|trident)/i.test(st.navigator.userAgent)):()=>!0,DS=st.hasStandardBrowserEnv?{write(a,i,r,s,o,f){const h=[a+"="+encodeURIComponent(i)];U.isNumber(r)&&h.push("expires="+new Date(r).toGMTString()),U.isString(s)&&h.push("path="+s),U.isString(o)&&h.push("domain="+o),f===!0&&h.push("secure"),document.cookie=h.join("; ")},read(a){const i=document.cookie.match(new RegExp("(^|;\\s*)("+a+")=([^;]*)"));return i?decodeURIComponent(i[3]):null},remove(a){this.write(a,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function zS(a){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(a)}function _S(a,i){return i?a.replace(/\/?\/$/,"")+"/"+i.replace(/^\/+/,""):a}function Tp(a,i,r){let s=!zS(i);return a&&(s||r==!1)?_S(a,i):i}const vy=a=>a instanceof bt?{...a}:a;function ql(a,i){i=i||{};const r={};function s(m,v,E,T){return U.isPlainObject(m)&&U.isPlainObject(v)?U.merge.call({caseless:T},m,v):U.isPlainObject(v)?U.merge({},v):U.isArray(v)?v.slice():v}function o(m,v,E,T){if(U.isUndefined(v)){if(!U.isUndefined(m))return s(void 0,m,E,T)}else return s(m,v,E,T)}function f(m,v){if(!U.isUndefined(v))return s(void 0,v)}function h(m,v){if(U.isUndefined(v)){if(!U.isUndefined(m))return s(void 0,m)}else return s(void 0,v)}function g(m,v,E){if(E in i)return s(m,v);if(E in a)return s(void 0,m)}const p={url:f,method:f,data:f,baseURL:h,transformRequest:h,transformResponse:h,paramsSerializer:h,timeout:h,timeoutMessage:h,withCredentials:h,withXSRFToken:h,adapter:h,responseType:h,xsrfCookieName:h,xsrfHeaderName:h,onUploadProgress:h,onDownloadProgress:h,decompress:h,maxContentLength:h,maxBodyLength:h,beforeRedirect:h,transport:h,httpAgent:h,httpsAgent:h,cancelToken:h,socketPath:h,responseEncoding:h,validateStatus:g,headers:(m,v,E)=>o(vy(m),vy(v),E,!0)};return U.forEach(Object.keys(Object.assign({},a,i)),function(v){const E=p[v]||o,T=E(a[v],i[v],v);U.isUndefined(T)&&E!==g||(r[v]=T)}),r}const wp=a=>{const i=ql({},a);let{data:r,withXSRFToken:s,xsrfHeaderName:o,xsrfCookieName:f,headers:h,auth:g}=i;i.headers=h=bt.from(h),i.url=Sp(Tp(i.baseURL,i.url,i.allowAbsoluteUrls),a.params,a.paramsSerializer),g&&h.set("Authorization","Basic "+btoa((g.username||"")+":"+(g.password?unescape(encodeURIComponent(g.password)):"")));let p;if(U.isFormData(r)){if(st.hasStandardBrowserEnv||st.hasStandardBrowserWebWorkerEnv)h.setContentType(void 0);else if((p=h.getContentType())!==!1){const[m,...v]=p?p.split(";").map(E=>E.trim()).filter(Boolean):[];h.setContentType([m||"multipart/form-data",...v].join("; "))}}if(st.hasStandardBrowserEnv&&(s&&U.isFunction(s)&&(s=s(i)),s||s!==!1&&MS(i.url))){const m=o&&f&&DS.read(f);m&&h.set(o,m)}return i},US=typeof XMLHttpRequest<"u",jS=US&&function(a){return new Promise(function(r,s){const o=wp(a);let f=o.data;const h=bt.from(o.headers).normalize();let{responseType:g,onUploadProgress:p,onDownloadProgress:m}=o,v,E,T,B,A;function M(){B&&B(),A&&A(),o.cancelToken&&o.cancelToken.unsubscribe(v),o.signal&&o.signal.removeEventListener("abort",v)}let C=new XMLHttpRequest;C.open(o.method.toUpperCase(),o.url,!0),C.timeout=o.timeout;function H(){if(!C)return;const Y=bt.from("getAllResponseHeaders"in C&&C.getAllResponseHeaders()),K={data:!g||g==="text"||g==="json"?C.responseText:C.response,status:C.status,statusText:C.statusText,headers:Y,config:a,request:C};Rp(function(fe){r(fe),M()},function(fe){s(fe),M()},K),C=null}"onloadend"in C?C.onloadend=H:C.onreadystatechange=function(){!C||C.readyState!==4||C.status===0&&!(C.responseURL&&C.responseURL.indexOf("file:")===0)||setTimeout(H)},C.onabort=function(){C&&(s(new oe("Request aborted",oe.ECONNABORTED,a,C)),C=null)},C.onerror=function(){s(new oe("Network Error",oe.ERR_NETWORK,a,C)),C=null},C.ontimeout=function(){let P=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const K=o.transitional||xp;o.timeoutErrorMessage&&(P=o.timeoutErrorMessage),s(new oe(P,K.clarifyTimeoutError?oe.ETIMEDOUT:oe.ECONNABORTED,a,C)),C=null},f===void 0&&h.setContentType(null),"setRequestHeader"in C&&U.forEach(h.toJSON(),function(P,K){C.setRequestHeader(K,P)}),U.isUndefined(o.withCredentials)||(C.withCredentials=!!o.withCredentials),g&&g!=="json"&&(C.responseType=o.responseType),m&&([T,A]=Cr(m,!0),C.addEventListener("progress",T)),p&&C.upload&&([E,B]=Cr(p),C.upload.addEventListener("progress",E),C.upload.addEventListener("loadend",B)),(o.cancelToken||o.signal)&&(v=Y=>{C&&(s(!Y||Y.type?new La(null,a,C):Y),C.abort(),C=null)},o.cancelToken&&o.cancelToken.subscribe(v),o.signal&&(o.signal.aborted?v():o.signal.addEventListener("abort",v)));const Z=OS(o.url);if(Z&&st.protocols.indexOf(Z)===-1){s(new oe("Unsupported protocol "+Z+":",oe.ERR_BAD_REQUEST,a));return}C.send(f||null)})},HS=(a,i)=>{const{length:r}=a=a?a.filter(Boolean):[];if(i||r){let s=new AbortController,o;const f=function(m){if(!o){o=!0,g();const v=m instanceof Error?m:this.reason;s.abort(v instanceof oe?v:new La(v instanceof Error?v.message:v))}};let h=i&&setTimeout(()=>{h=null,f(new oe(`timeout ${i} of ms exceeded`,oe.ETIMEDOUT))},i);const g=()=>{a&&(h&&clearTimeout(h),h=null,a.forEach(m=>{m.unsubscribe?m.unsubscribe(f):m.removeEventListener("abort",f)}),a=null)};a.forEach(m=>m.addEventListener("abort",f));const{signal:p}=s;return p.unsubscribe=()=>U.asap(g),p}},qS=function*(a,i){let r=a.byteLength;if(r<i){yield a;return}let s=0,o;for(;s<r;)o=s+i,yield a.slice(s,o),s=o},BS=async function*(a,i){for await(const r of LS(a))yield*qS(r,i)},LS=async function*(a){if(a[Symbol.asyncIterator]){yield*a;return}const i=a.getReader();try{for(;;){const{done:r,value:s}=await i.read();if(r)break;yield s}}finally{await i.cancel()}},by=(a,i,r,s)=>{const o=BS(a,i);let f=0,h,g=p=>{h||(h=!0,s&&s(p))};return new ReadableStream({async pull(p){try{const{done:m,value:v}=await o.next();if(m){g(),p.close();return}let E=v.byteLength;if(r){let T=f+=E;r(T)}p.enqueue(new Uint8Array(v))}catch(m){throw g(m),m}},cancel(p){return g(p),o.return()}},{highWaterMark:2})},Gr=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Op=Gr&&typeof ReadableStream=="function",kS=Gr&&(typeof TextEncoder=="function"?(a=>i=>a.encode(i))(new TextEncoder):async a=>new Uint8Array(await new Response(a).arrayBuffer())),Np=(a,...i)=>{try{return!!a(...i)}catch{return!1}},GS=Op&&Np(()=>{let a=!1;const i=new Request(st.origin,{body:new ReadableStream,method:"POST",get duplex(){return a=!0,"half"}}).headers.has("Content-Type");return a&&!i}),Sy=64*1024,wo=Op&&Np(()=>U.isReadableStream(new Response("").body)),Mr={stream:wo&&(a=>a.body)};Gr&&(a=>{["text","arrayBuffer","blob","formData","stream"].forEach(i=>{!Mr[i]&&(Mr[i]=U.isFunction(a[i])?r=>r[i]():(r,s)=>{throw new oe(`Response type '${i}' is not supported`,oe.ERR_NOT_SUPPORT,s)})})})(new Response);const YS=async a=>{if(a==null)return 0;if(U.isBlob(a))return a.size;if(U.isSpecCompliantForm(a))return(await new Request(st.origin,{method:"POST",body:a}).arrayBuffer()).byteLength;if(U.isArrayBufferView(a)||U.isArrayBuffer(a))return a.byteLength;if(U.isURLSearchParams(a)&&(a=a+""),U.isString(a))return(await kS(a)).byteLength},QS=async(a,i)=>{const r=U.toFiniteNumber(a.getContentLength());return r??YS(i)},VS=Gr&&(async a=>{let{url:i,method:r,data:s,signal:o,cancelToken:f,timeout:h,onDownloadProgress:g,onUploadProgress:p,responseType:m,headers:v,withCredentials:E="same-origin",fetchOptions:T}=wp(a);m=m?(m+"").toLowerCase():"text";let B=HS([o,f&&f.toAbortSignal()],h),A;const M=B&&B.unsubscribe&&(()=>{B.unsubscribe()});let C;try{if(p&&GS&&r!=="get"&&r!=="head"&&(C=await QS(v,s))!==0){let K=new Request(i,{method:"POST",body:s,duplex:"half"}),ue;if(U.isFormData(s)&&(ue=K.headers.get("content-type"))&&v.setContentType(ue),K.body){const[fe,F]=py(C,Cr(gy(p)));s=by(K.body,Sy,fe,F)}}U.isString(E)||(E=E?"include":"omit");const H="credentials"in Request.prototype;A=new Request(i,{...T,signal:B,method:r.toUpperCase(),headers:v.normalize().toJSON(),body:s,duplex:"half",credentials:H?E:void 0});let Z=await fetch(A);const Y=wo&&(m==="stream"||m==="response");if(wo&&(g||Y&&M)){const K={};["status","statusText","headers"].forEach(le=>{K[le]=Z[le]});const ue=U.toFiniteNumber(Z.headers.get("content-length")),[fe,F]=g&&py(ue,Cr(gy(g),!0))||[];Z=new Response(by(Z.body,Sy,fe,()=>{F&&F(),M&&M()}),K)}m=m||"text";let P=await Mr[U.findKey(Mr,m)||"text"](Z,a);return!Y&&M&&M(),await new Promise((K,ue)=>{Rp(K,ue,{data:P,headers:bt.from(Z.headers),status:Z.status,statusText:Z.statusText,config:a,request:A})})}catch(H){throw M&&M(),H&&H.name==="TypeError"&&/Load failed|fetch/i.test(H.message)?Object.assign(new oe("Network Error",oe.ERR_NETWORK,a,A),{cause:H.cause||H}):oe.from(H,H&&H.code,a,A)}}),Oo={http:iS,xhr:jS,fetch:VS};U.forEach(Oo,(a,i)=>{if(a){try{Object.defineProperty(a,"name",{value:i})}catch{}Object.defineProperty(a,"adapterName",{value:i})}});const xy=a=>`- ${a}`,XS=a=>U.isFunction(a)||a===null||a===!1,Cp={getAdapter:a=>{a=U.isArray(a)?a:[a];const{length:i}=a;let r,s;const o={};for(let f=0;f<i;f++){r=a[f];let h;if(s=r,!XS(r)&&(s=Oo[(h=String(r)).toLowerCase()],s===void 0))throw new oe(`Unknown adapter '${h}'`);if(s)break;o[h||"#"+f]=s}if(!s){const f=Object.entries(o).map(([g,p])=>`adapter ${g} `+(p===!1?"is not supported by the environment":"is not available in the build"));let h=i?f.length>1?`since :
`+f.map(xy).join(`
`):" "+xy(f[0]):"as no adapter specified";throw new oe("There is no suitable adapter to dispatch the request "+h,"ERR_NOT_SUPPORT")}return s},adapters:Oo};function po(a){if(a.cancelToken&&a.cancelToken.throwIfRequested(),a.signal&&a.signal.aborted)throw new La(null,a)}function Ey(a){return po(a),a.headers=bt.from(a.headers),a.data=yo.call(a,a.transformRequest),["post","put","patch"].indexOf(a.method)!==-1&&a.headers.setContentType("application/x-www-form-urlencoded",!1),Cp.getAdapter(a.adapter||tu.adapter)(a).then(function(s){return po(a),s.data=yo.call(a,a.transformResponse,s),s.headers=bt.from(s.headers),s},function(s){return Ap(s)||(po(a),s&&s.response&&(s.response.data=yo.call(a,a.transformResponse,s.response),s.response.headers=bt.from(s.response.headers))),Promise.reject(s)})}const Mp="1.9.0",Yr={};["object","boolean","number","function","string","symbol"].forEach((a,i)=>{Yr[a]=function(s){return typeof s===a||"a"+(i<1?"n ":" ")+a}});const Ay={};Yr.transitional=function(i,r,s){function o(f,h){return"[Axios v"+Mp+"] Transitional option '"+f+"'"+h+(s?". "+s:"")}return(f,h,g)=>{if(i===!1)throw new oe(o(h," has been removed"+(r?" in "+r:"")),oe.ERR_DEPRECATED);return r&&!Ay[h]&&(Ay[h]=!0,console.warn(o(h," has been deprecated since v"+r+" and will be removed in the near future"))),i?i(f,h,g):!0}};Yr.spelling=function(i){return(r,s)=>(console.warn(`${s} is likely a misspelling of ${i}`),!0)};function ZS(a,i,r){if(typeof a!="object")throw new oe("options must be an object",oe.ERR_BAD_OPTION_VALUE);const s=Object.keys(a);let o=s.length;for(;o-- >0;){const f=s[o],h=i[f];if(h){const g=a[f],p=g===void 0||h(g,f,a);if(p!==!0)throw new oe("option "+f+" must be "+p,oe.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new oe("Unknown option "+f,oe.ERR_BAD_OPTION)}}const wr={assertOptions:ZS,validators:Yr},an=wr.validators;let Hl=class{constructor(i){this.defaults=i||{},this.interceptors={request:new my,response:new my}}async request(i,r){try{return await this._request(i,r)}catch(s){if(s instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const f=o.stack?o.stack.replace(/^.+\n/,""):"";try{s.stack?f&&!String(s.stack).endsWith(f.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+f):s.stack=f}catch{}}throw s}}_request(i,r){typeof i=="string"?(r=r||{},r.url=i):r=i||{},r=ql(this.defaults,r);const{transitional:s,paramsSerializer:o,headers:f}=r;s!==void 0&&wr.assertOptions(s,{silentJSONParsing:an.transitional(an.boolean),forcedJSONParsing:an.transitional(an.boolean),clarifyTimeoutError:an.transitional(an.boolean)},!1),o!=null&&(U.isFunction(o)?r.paramsSerializer={serialize:o}:wr.assertOptions(o,{encode:an.function,serialize:an.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),wr.assertOptions(r,{baseUrl:an.spelling("baseURL"),withXsrfToken:an.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let h=f&&U.merge(f.common,f[r.method]);f&&U.forEach(["delete","get","head","post","put","patch","common"],A=>{delete f[A]}),r.headers=bt.concat(h,f);const g=[];let p=!0;this.interceptors.request.forEach(function(M){typeof M.runWhen=="function"&&M.runWhen(r)===!1||(p=p&&M.synchronous,g.unshift(M.fulfilled,M.rejected))});const m=[];this.interceptors.response.forEach(function(M){m.push(M.fulfilled,M.rejected)});let v,E=0,T;if(!p){const A=[Ey.bind(this),void 0];for(A.unshift.apply(A,g),A.push.apply(A,m),T=A.length,v=Promise.resolve(r);E<T;)v=v.then(A[E++],A[E++]);return v}T=g.length;let B=r;for(E=0;E<T;){const A=g[E++],M=g[E++];try{B=A(B)}catch(C){M.call(this,C);break}}try{v=Ey.call(this,B)}catch(A){return Promise.reject(A)}for(E=0,T=m.length;E<T;)v=v.then(m[E++],m[E++]);return v}getUri(i){i=ql(this.defaults,i);const r=Tp(i.baseURL,i.url,i.allowAbsoluteUrls);return Sp(r,i.params,i.paramsSerializer)}};U.forEach(["delete","get","head","options"],function(i){Hl.prototype[i]=function(r,s){return this.request(ql(s||{},{method:i,url:r,data:(s||{}).data}))}});U.forEach(["post","put","patch"],function(i){function r(s){return function(f,h,g){return this.request(ql(g||{},{method:i,headers:s?{"Content-Type":"multipart/form-data"}:{},url:f,data:h}))}}Hl.prototype[i]=r(),Hl.prototype[i+"Form"]=r(!0)});let KS=class Dp{constructor(i){if(typeof i!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(f){r=f});const s=this;this.promise.then(o=>{if(!s._listeners)return;let f=s._listeners.length;for(;f-- >0;)s._listeners[f](o);s._listeners=null}),this.promise.then=o=>{let f;const h=new Promise(g=>{s.subscribe(g),f=g}).then(o);return h.cancel=function(){s.unsubscribe(f)},h},i(function(f,h,g){s.reason||(s.reason=new La(f,h,g),r(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(i){if(this.reason){i(this.reason);return}this._listeners?this._listeners.push(i):this._listeners=[i]}unsubscribe(i){if(!this._listeners)return;const r=this._listeners.indexOf(i);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const i=new AbortController,r=s=>{i.abort(s)};return this.subscribe(r),i.signal.unsubscribe=()=>this.unsubscribe(r),i.signal}static source(){let i;return{token:new Dp(function(o){i=o}),cancel:i}}};function JS(a){return function(r){return a.apply(null,r)}}function FS(a){return U.isObject(a)&&a.isAxiosError===!0}const No={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(No).forEach(([a,i])=>{No[i]=a});function zp(a){const i=new Hl(a),r=sp(Hl.prototype.request,i);return U.extend(r,Hl.prototype,i,{allOwnKeys:!0}),U.extend(r,i,null,{allOwnKeys:!0}),r.create=function(o){return zp(ql(a,o))},r}const Ve=zp(tu);Ve.Axios=Hl;Ve.CanceledError=La;Ve.CancelToken=KS;Ve.isCancel=Ap;Ve.VERSION=Mp;Ve.toFormData=kr;Ve.AxiosError=oe;Ve.Cancel=Ve.CanceledError;Ve.all=function(i){return Promise.all(i)};Ve.spread=JS;Ve.isAxiosError=FS;Ve.mergeConfig=ql;Ve.AxiosHeaders=bt;Ve.formToJSON=a=>Ep(U.isHTMLForm(a)?new FormData(a):a);Ve.getAdapter=Cp.getAdapter;Ve.HttpStatusCode=No;Ve.default=Ve;const{Axios:dE,AxiosError:hE,CanceledError:mE,isCancel:yE,CancelToken:pE,VERSION:gE,all:vE,Cancel:bE,isAxiosError:SE,spread:xE,toFormData:EE,AxiosHeaders:AE,HttpStatusCode:RE,formToJSON:TE,getAdapter:wE,mergeConfig:OE}=Ve,$S=()=>"",Dr=Ve.create({baseURL:$S(),headers:{"Content-Type":"application/json"}});Dr.interceptors.request.use(a=>{const i=localStorage.getItem("auth_token");return i&&(a.headers.Authorization=`Bearer ${i}`),a},a=>Promise.reject(a));Dr.interceptors.response.use(a=>a,a=>{var i;return((i=a.response)==null?void 0:i.status)===401&&(localStorage.removeItem("auth_token"),window.location.href="/auth/login"),Promise.reject(a)});const br={login:async(a,i)=>{const r=new URLSearchParams;r.append("grant_type","password"),r.append("username",a),r.append("password",i),r.append("client_id","Ekb_App"),r.append("scope","Ekb");const s=await Dr.post("/connect/token",r,{headers:{"Content-Type":"application/x-www-form-urlencoded"}});return s.data.access_token&&localStorage.setItem("auth_token",s.data.access_token),s.data},logout:()=>{localStorage.removeItem("auth_token")},getCurrentUser:async()=>await Dr.get("/api/identity/my-profile")},_p=_.createContext(void 0);function WS({children:a}){const[i,r]=_.useState(null),[s,o]=_.useState(!0);_.useEffect(()=>{(async()=>{if(localStorage.getItem("auth_token"))try{const v=await br.getCurrentUser();r(v.data)}catch(v){console.error("Failed to get current user:",v),localStorage.removeItem("auth_token")}o(!1)})()},[]);const g={user:i,isAuthenticated:!!i,isLoading:s,login:async(p,m)=>{o(!0);try{await br.login(p,m);const v=await br.getCurrentUser();r(v.data)}catch(v){throw console.error("Login failed:",v),v}finally{o(!1)}},logout:()=>{br.logout(),r(null)}};return O.jsx(_p.Provider,{value:g,children:a})}function Qr(){const a=_.useContext(_p);if(a===void 0)throw new Error("useAuth must be used within an AuthProvider");return a}function go({children:a}){const{isAuthenticated:i,isLoading:r}=Qr(),s=Ft();return r?O.jsx("div",{className:"min-h-screen flex items-center justify-center",children:O.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary"})}):i?O.jsx(O.Fragment,{children:a}):O.jsx(Fy,{to:"/auth/login",state:{from:s},replace:!0})}function PS(){const{isAuthenticated:a,isLoading:i}=Qr(),r=Pi(),s=Ft();return _.useEffect(()=>{a&&!i&&s.pathname==="/auth/login"&&r("/",{replace:!0})},[a,i,s.pathname,r]),{isAuthenticated:a,isLoading:i}}function Ry(a,i){if(typeof a=="function")return a(i);a!=null&&(a.current=i)}function IS(...a){return i=>{let r=!1;const s=a.map(o=>{const f=Ry(o,i);return!r&&typeof f=="function"&&(r=!0),f});if(r)return()=>{for(let o=0;o<s.length;o++){const f=s[o];typeof f=="function"?f():Ry(a[o],null)}}}}function ex(...a){return _.useCallback(IS(...a),a)}function tx(a){const i=lx(a),r=_.forwardRef((s,o)=>{const{children:f,...h}=s,g=_.Children.toArray(f),p=g.find(ix);if(p){const m=p.props.children,v=g.map(E=>E===p?_.Children.count(m)>1?_.Children.only(null):_.isValidElement(m)?m.props.children:null:E);return O.jsx(i,{...h,ref:o,children:_.isValidElement(m)?_.cloneElement(m,void 0,v):null})}return O.jsx(i,{...h,ref:o,children:f})});return r.displayName=`${a}.Slot`,r}var nx=tx("Slot");function lx(a){const i=_.forwardRef((r,s)=>{const{children:o,...f}=r,h=_.isValidElement(o)?rx(o):void 0,g=ex(h,s);if(_.isValidElement(o)){const p=ux(f,o.props);return o.type!==_.Fragment&&(p.ref=g),_.cloneElement(o,p)}return _.Children.count(o)>1?_.Children.only(null):null});return i.displayName=`${a}.SlotClone`,i}var ax=Symbol("radix.slottable");function ix(a){return _.isValidElement(a)&&typeof a.type=="function"&&"__radixId"in a.type&&a.type.__radixId===ax}function ux(a,i){const r={...i};for(const s in i){const o=a[s],f=i[s];/^on[A-Z]/.test(s)?o&&f?r[s]=(...g)=>{const p=f(...g);return o(...g),p}:o&&(r[s]=o):s==="style"?r[s]={...o,...f}:s==="className"&&(r[s]=[o,f].filter(Boolean).join(" "))}return{...a,...r}}function rx(a){var s,o;let i=(s=Object.getOwnPropertyDescriptor(a.props,"ref"))==null?void 0:s.get,r=i&&"isReactWarning"in i&&i.isReactWarning;return r?a.ref:(i=(o=Object.getOwnPropertyDescriptor(a,"ref"))==null?void 0:o.get,r=i&&"isReactWarning"in i&&i.isReactWarning,r?a.props.ref:a.props.ref||a.ref)}function Up(a){var i,r,s="";if(typeof a=="string"||typeof a=="number")s+=a;else if(typeof a=="object")if(Array.isArray(a)){var o=a.length;for(i=0;i<o;i++)a[i]&&(r=Up(a[i]))&&(s&&(s+=" "),s+=r)}else for(r in a)a[r]&&(s&&(s+=" "),s+=r);return s}function jp(){for(var a,i,r=0,s="",o=arguments.length;r<o;r++)(a=arguments[r])&&(i=Up(a))&&(s&&(s+=" "),s+=i);return s}const Ty=a=>typeof a=="boolean"?`${a}`:a===0?"0":a,wy=jp,sx=(a,i)=>r=>{var s;if((i==null?void 0:i.variants)==null)return wy(a,r==null?void 0:r.class,r==null?void 0:r.className);const{variants:o,defaultVariants:f}=i,h=Object.keys(o).map(m=>{const v=r==null?void 0:r[m],E=f==null?void 0:f[m];if(v===null)return null;const T=Ty(v)||Ty(E);return o[m][T]}),g=r&&Object.entries(r).reduce((m,v)=>{let[E,T]=v;return T===void 0||(m[E]=T),m},{}),p=i==null||(s=i.compoundVariants)===null||s===void 0?void 0:s.reduce((m,v)=>{let{class:E,className:T,...B}=v;return Object.entries(B).every(A=>{let[M,C]=A;return Array.isArray(C)?C.includes({...f,...g}[M]):{...f,...g}[M]===C})?[...m,E,T]:m},[]);return wy(a,h,p,r==null?void 0:r.class,r==null?void 0:r.className)},Xo="-",cx=a=>{const i=fx(a),{conflictingClassGroups:r,conflictingClassGroupModifiers:s}=a;return{getClassGroupId:h=>{const g=h.split(Xo);return g[0]===""&&g.length!==1&&g.shift(),Hp(g,i)||ox(h)},getConflictingClassGroupIds:(h,g)=>{const p=r[h]||[];return g&&s[h]?[...p,...s[h]]:p}}},Hp=(a,i)=>{var h;if(a.length===0)return i.classGroupId;const r=a[0],s=i.nextPart.get(r),o=s?Hp(a.slice(1),s):void 0;if(o)return o;if(i.validators.length===0)return;const f=a.join(Xo);return(h=i.validators.find(({validator:g})=>g(f)))==null?void 0:h.classGroupId},Oy=/^\[(.+)\]$/,ox=a=>{if(Oy.test(a)){const i=Oy.exec(a)[1],r=i==null?void 0:i.substring(0,i.indexOf(":"));if(r)return"arbitrary.."+r}},fx=a=>{const{theme:i,classGroups:r}=a,s={nextPart:new Map,validators:[]};for(const o in r)Co(r[o],s,o,i);return s},Co=(a,i,r,s)=>{a.forEach(o=>{if(typeof o=="string"){const f=o===""?i:Ny(i,o);f.classGroupId=r;return}if(typeof o=="function"){if(dx(o)){Co(o(s),i,r,s);return}i.validators.push({validator:o,classGroupId:r});return}Object.entries(o).forEach(([f,h])=>{Co(h,Ny(i,f),r,s)})})},Ny=(a,i)=>{let r=a;return i.split(Xo).forEach(s=>{r.nextPart.has(s)||r.nextPart.set(s,{nextPart:new Map,validators:[]}),r=r.nextPart.get(s)}),r},dx=a=>a.isThemeGetter,hx=a=>{if(a<1)return{get:()=>{},set:()=>{}};let i=0,r=new Map,s=new Map;const o=(f,h)=>{r.set(f,h),i++,i>a&&(i=0,s=r,r=new Map)};return{get(f){let h=r.get(f);if(h!==void 0)return h;if((h=s.get(f))!==void 0)return o(f,h),h},set(f,h){r.has(f)?r.set(f,h):o(f,h)}}},Mo="!",Do=":",mx=Do.length,yx=a=>{const{prefix:i,experimentalParseClassName:r}=a;let s=o=>{const f=[];let h=0,g=0,p=0,m;for(let A=0;A<o.length;A++){let M=o[A];if(h===0&&g===0){if(M===Do){f.push(o.slice(p,A)),p=A+mx;continue}if(M==="/"){m=A;continue}}M==="["?h++:M==="]"?h--:M==="("?g++:M===")"&&g--}const v=f.length===0?o:o.substring(p),E=px(v),T=E!==v,B=m&&m>p?m-p:void 0;return{modifiers:f,hasImportantModifier:T,baseClassName:E,maybePostfixModifierPosition:B}};if(i){const o=i+Do,f=s;s=h=>h.startsWith(o)?f(h.substring(o.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:h,maybePostfixModifierPosition:void 0}}if(r){const o=s;s=f=>r({className:f,parseClassName:o})}return s},px=a=>a.endsWith(Mo)?a.substring(0,a.length-1):a.startsWith(Mo)?a.substring(1):a,gx=a=>{const i=Object.fromEntries(a.orderSensitiveModifiers.map(s=>[s,!0]));return s=>{if(s.length<=1)return s;const o=[];let f=[];return s.forEach(h=>{h[0]==="["||i[h]?(o.push(...f.sort(),h),f=[]):f.push(h)}),o.push(...f.sort()),o}},vx=a=>({cache:hx(a.cacheSize),parseClassName:yx(a),sortModifiers:gx(a),...cx(a)}),bx=/\s+/,Sx=(a,i)=>{const{parseClassName:r,getClassGroupId:s,getConflictingClassGroupIds:o,sortModifiers:f}=i,h=[],g=a.trim().split(bx);let p="";for(let m=g.length-1;m>=0;m-=1){const v=g[m],{isExternal:E,modifiers:T,hasImportantModifier:B,baseClassName:A,maybePostfixModifierPosition:M}=r(v);if(E){p=v+(p.length>0?" "+p:p);continue}let C=!!M,H=s(C?A.substring(0,M):A);if(!H){if(!C){p=v+(p.length>0?" "+p:p);continue}if(H=s(A),!H){p=v+(p.length>0?" "+p:p);continue}C=!1}const Z=f(T).join(":"),Y=B?Z+Mo:Z,P=Y+H;if(h.includes(P))continue;h.push(P);const K=o(H,C);for(let ue=0;ue<K.length;++ue){const fe=K[ue];h.push(Y+fe)}p=v+(p.length>0?" "+p:p)}return p};function xx(){let a=0,i,r,s="";for(;a<arguments.length;)(i=arguments[a++])&&(r=qp(i))&&(s&&(s+=" "),s+=r);return s}const qp=a=>{if(typeof a=="string")return a;let i,r="";for(let s=0;s<a.length;s++)a[s]&&(i=qp(a[s]))&&(r&&(r+=" "),r+=i);return r};function Ex(a,...i){let r,s,o,f=h;function h(p){const m=i.reduce((v,E)=>E(v),a());return r=vx(m),s=r.cache.get,o=r.cache.set,f=g,g(p)}function g(p){const m=s(p);if(m)return m;const v=Sx(p,r);return o(p,v),v}return function(){return f(xx.apply(null,arguments))}}const Fe=a=>{const i=r=>r[a]||[];return i.isThemeGetter=!0,i},Bp=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Lp=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Ax=/^\d+\/\d+$/,Rx=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Tx=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,wx=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Ox=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Nx=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Aa=a=>Ax.test(a),me=a=>!!a&&!Number.isNaN(Number(a)),al=a=>!!a&&Number.isInteger(Number(a)),vo=a=>a.endsWith("%")&&me(a.slice(0,-1)),Tn=a=>Rx.test(a),Cx=()=>!0,Mx=a=>Tx.test(a)&&!wx.test(a),kp=()=>!1,Dx=a=>Ox.test(a),zx=a=>Nx.test(a),_x=a=>!ee(a)&&!te(a),Ux=a=>ka(a,Qp,kp),ee=a=>Bp.test(a),Cl=a=>ka(a,Vp,Mx),bo=a=>ka(a,Lx,me),Cy=a=>ka(a,Gp,kp),jx=a=>ka(a,Yp,zx),Sr=a=>ka(a,Xp,Dx),te=a=>Lp.test(a),Vi=a=>Ga(a,Vp),Hx=a=>Ga(a,kx),My=a=>Ga(a,Gp),qx=a=>Ga(a,Qp),Bx=a=>Ga(a,Yp),xr=a=>Ga(a,Xp,!0),ka=(a,i,r)=>{const s=Bp.exec(a);return s?s[1]?i(s[1]):r(s[2]):!1},Ga=(a,i,r=!1)=>{const s=Lp.exec(a);return s?s[1]?i(s[1]):r:!1},Gp=a=>a==="position"||a==="percentage",Yp=a=>a==="image"||a==="url",Qp=a=>a==="length"||a==="size"||a==="bg-size",Vp=a=>a==="length",Lx=a=>a==="number",kx=a=>a==="family-name",Xp=a=>a==="shadow",Gx=()=>{const a=Fe("color"),i=Fe("font"),r=Fe("text"),s=Fe("font-weight"),o=Fe("tracking"),f=Fe("leading"),h=Fe("breakpoint"),g=Fe("container"),p=Fe("spacing"),m=Fe("radius"),v=Fe("shadow"),E=Fe("inset-shadow"),T=Fe("text-shadow"),B=Fe("drop-shadow"),A=Fe("blur"),M=Fe("perspective"),C=Fe("aspect"),H=Fe("ease"),Z=Fe("animate"),Y=()=>["auto","avoid","all","avoid-page","page","left","right","column"],P=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],K=()=>[...P(),te,ee],ue=()=>["auto","hidden","clip","visible","scroll"],fe=()=>["auto","contain","none"],F=()=>[te,ee,p],le=()=>[Aa,"full","auto",...F()],je=()=>[al,"none","subgrid",te,ee],ct=()=>["auto",{span:["full",al,te,ee]},al,te,ee],Ge=()=>[al,"auto",te,ee],Wt=()=>["auto","min","max","fr",te,ee],kt=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],He=()=>["start","end","center","stretch","center-safe","end-safe"],j=()=>["auto",...F()],J=()=>[Aa,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...F()],V=()=>[a,te,ee],Re=()=>[...P(),My,Cy,{position:[te,ee]}],S=()=>["no-repeat",{repeat:["","x","y","space","round"]}],Q=()=>["auto","cover","contain",qx,Ux,{size:[te,ee]}],$=()=>[vo,Vi,Cl],X=()=>["","none","full",m,te,ee],W=()=>["",me,Vi,Cl],pe=()=>["solid","dashed","dotted","double"],se=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],Se=()=>[me,vo,My,Cy],Me=()=>["","none",A,te,ee],St=()=>["none",me,te,ee],Dn=()=>["none",me,te,ee],zn=()=>[me,te,ee],_n=()=>[Aa,"full",...F()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Tn],breakpoint:[Tn],color:[Cx],container:[Tn],"drop-shadow":[Tn],ease:["in","out","in-out"],font:[_x],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Tn],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Tn],shadow:[Tn],spacing:["px",me],text:[Tn],"text-shadow":[Tn],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Aa,ee,te,C]}],container:["container"],columns:[{columns:[me,ee,te,g]}],"break-after":[{"break-after":Y()}],"break-before":[{"break-before":Y()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:K()}],overflow:[{overflow:ue()}],"overflow-x":[{"overflow-x":ue()}],"overflow-y":[{"overflow-y":ue()}],overscroll:[{overscroll:fe()}],"overscroll-x":[{"overscroll-x":fe()}],"overscroll-y":[{"overscroll-y":fe()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:le()}],"inset-x":[{"inset-x":le()}],"inset-y":[{"inset-y":le()}],start:[{start:le()}],end:[{end:le()}],top:[{top:le()}],right:[{right:le()}],bottom:[{bottom:le()}],left:[{left:le()}],visibility:["visible","invisible","collapse"],z:[{z:[al,"auto",te,ee]}],basis:[{basis:[Aa,"full","auto",g,...F()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[me,Aa,"auto","initial","none",ee]}],grow:[{grow:["",me,te,ee]}],shrink:[{shrink:["",me,te,ee]}],order:[{order:[al,"first","last","none",te,ee]}],"grid-cols":[{"grid-cols":je()}],"col-start-end":[{col:ct()}],"col-start":[{"col-start":Ge()}],"col-end":[{"col-end":Ge()}],"grid-rows":[{"grid-rows":je()}],"row-start-end":[{row:ct()}],"row-start":[{"row-start":Ge()}],"row-end":[{"row-end":Ge()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Wt()}],"auto-rows":[{"auto-rows":Wt()}],gap:[{gap:F()}],"gap-x":[{"gap-x":F()}],"gap-y":[{"gap-y":F()}],"justify-content":[{justify:[...kt(),"normal"]}],"justify-items":[{"justify-items":[...He(),"normal"]}],"justify-self":[{"justify-self":["auto",...He()]}],"align-content":[{content:["normal",...kt()]}],"align-items":[{items:[...He(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...He(),{baseline:["","last"]}]}],"place-content":[{"place-content":kt()}],"place-items":[{"place-items":[...He(),"baseline"]}],"place-self":[{"place-self":["auto",...He()]}],p:[{p:F()}],px:[{px:F()}],py:[{py:F()}],ps:[{ps:F()}],pe:[{pe:F()}],pt:[{pt:F()}],pr:[{pr:F()}],pb:[{pb:F()}],pl:[{pl:F()}],m:[{m:j()}],mx:[{mx:j()}],my:[{my:j()}],ms:[{ms:j()}],me:[{me:j()}],mt:[{mt:j()}],mr:[{mr:j()}],mb:[{mb:j()}],ml:[{ml:j()}],"space-x":[{"space-x":F()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":F()}],"space-y-reverse":["space-y-reverse"],size:[{size:J()}],w:[{w:[g,"screen",...J()]}],"min-w":[{"min-w":[g,"screen","none",...J()]}],"max-w":[{"max-w":[g,"screen","none","prose",{screen:[h]},...J()]}],h:[{h:["screen","lh",...J()]}],"min-h":[{"min-h":["screen","lh","none",...J()]}],"max-h":[{"max-h":["screen","lh",...J()]}],"font-size":[{text:["base",r,Vi,Cl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[s,te,bo]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",vo,ee]}],"font-family":[{font:[Hx,ee,i]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,te,ee]}],"line-clamp":[{"line-clamp":[me,"none",te,bo]}],leading:[{leading:[f,...F()]}],"list-image":[{"list-image":["none",te,ee]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",te,ee]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:V()}],"text-color":[{text:V()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...pe(),"wavy"]}],"text-decoration-thickness":[{decoration:[me,"from-font","auto",te,Cl]}],"text-decoration-color":[{decoration:V()}],"underline-offset":[{"underline-offset":[me,"auto",te,ee]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:F()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",te,ee]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",te,ee]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:Re()}],"bg-repeat":[{bg:S()}],"bg-size":[{bg:Q()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},al,te,ee],radial:["",te,ee],conic:[al,te,ee]},Bx,jx]}],"bg-color":[{bg:V()}],"gradient-from-pos":[{from:$()}],"gradient-via-pos":[{via:$()}],"gradient-to-pos":[{to:$()}],"gradient-from":[{from:V()}],"gradient-via":[{via:V()}],"gradient-to":[{to:V()}],rounded:[{rounded:X()}],"rounded-s":[{"rounded-s":X()}],"rounded-e":[{"rounded-e":X()}],"rounded-t":[{"rounded-t":X()}],"rounded-r":[{"rounded-r":X()}],"rounded-b":[{"rounded-b":X()}],"rounded-l":[{"rounded-l":X()}],"rounded-ss":[{"rounded-ss":X()}],"rounded-se":[{"rounded-se":X()}],"rounded-ee":[{"rounded-ee":X()}],"rounded-es":[{"rounded-es":X()}],"rounded-tl":[{"rounded-tl":X()}],"rounded-tr":[{"rounded-tr":X()}],"rounded-br":[{"rounded-br":X()}],"rounded-bl":[{"rounded-bl":X()}],"border-w":[{border:W()}],"border-w-x":[{"border-x":W()}],"border-w-y":[{"border-y":W()}],"border-w-s":[{"border-s":W()}],"border-w-e":[{"border-e":W()}],"border-w-t":[{"border-t":W()}],"border-w-r":[{"border-r":W()}],"border-w-b":[{"border-b":W()}],"border-w-l":[{"border-l":W()}],"divide-x":[{"divide-x":W()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":W()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...pe(),"hidden","none"]}],"divide-style":[{divide:[...pe(),"hidden","none"]}],"border-color":[{border:V()}],"border-color-x":[{"border-x":V()}],"border-color-y":[{"border-y":V()}],"border-color-s":[{"border-s":V()}],"border-color-e":[{"border-e":V()}],"border-color-t":[{"border-t":V()}],"border-color-r":[{"border-r":V()}],"border-color-b":[{"border-b":V()}],"border-color-l":[{"border-l":V()}],"divide-color":[{divide:V()}],"outline-style":[{outline:[...pe(),"none","hidden"]}],"outline-offset":[{"outline-offset":[me,te,ee]}],"outline-w":[{outline:["",me,Vi,Cl]}],"outline-color":[{outline:V()}],shadow:[{shadow:["","none",v,xr,Sr]}],"shadow-color":[{shadow:V()}],"inset-shadow":[{"inset-shadow":["none",E,xr,Sr]}],"inset-shadow-color":[{"inset-shadow":V()}],"ring-w":[{ring:W()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:V()}],"ring-offset-w":[{"ring-offset":[me,Cl]}],"ring-offset-color":[{"ring-offset":V()}],"inset-ring-w":[{"inset-ring":W()}],"inset-ring-color":[{"inset-ring":V()}],"text-shadow":[{"text-shadow":["none",T,xr,Sr]}],"text-shadow-color":[{"text-shadow":V()}],opacity:[{opacity:[me,te,ee]}],"mix-blend":[{"mix-blend":[...se(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":se()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[me]}],"mask-image-linear-from-pos":[{"mask-linear-from":Se()}],"mask-image-linear-to-pos":[{"mask-linear-to":Se()}],"mask-image-linear-from-color":[{"mask-linear-from":V()}],"mask-image-linear-to-color":[{"mask-linear-to":V()}],"mask-image-t-from-pos":[{"mask-t-from":Se()}],"mask-image-t-to-pos":[{"mask-t-to":Se()}],"mask-image-t-from-color":[{"mask-t-from":V()}],"mask-image-t-to-color":[{"mask-t-to":V()}],"mask-image-r-from-pos":[{"mask-r-from":Se()}],"mask-image-r-to-pos":[{"mask-r-to":Se()}],"mask-image-r-from-color":[{"mask-r-from":V()}],"mask-image-r-to-color":[{"mask-r-to":V()}],"mask-image-b-from-pos":[{"mask-b-from":Se()}],"mask-image-b-to-pos":[{"mask-b-to":Se()}],"mask-image-b-from-color":[{"mask-b-from":V()}],"mask-image-b-to-color":[{"mask-b-to":V()}],"mask-image-l-from-pos":[{"mask-l-from":Se()}],"mask-image-l-to-pos":[{"mask-l-to":Se()}],"mask-image-l-from-color":[{"mask-l-from":V()}],"mask-image-l-to-color":[{"mask-l-to":V()}],"mask-image-x-from-pos":[{"mask-x-from":Se()}],"mask-image-x-to-pos":[{"mask-x-to":Se()}],"mask-image-x-from-color":[{"mask-x-from":V()}],"mask-image-x-to-color":[{"mask-x-to":V()}],"mask-image-y-from-pos":[{"mask-y-from":Se()}],"mask-image-y-to-pos":[{"mask-y-to":Se()}],"mask-image-y-from-color":[{"mask-y-from":V()}],"mask-image-y-to-color":[{"mask-y-to":V()}],"mask-image-radial":[{"mask-radial":[te,ee]}],"mask-image-radial-from-pos":[{"mask-radial-from":Se()}],"mask-image-radial-to-pos":[{"mask-radial-to":Se()}],"mask-image-radial-from-color":[{"mask-radial-from":V()}],"mask-image-radial-to-color":[{"mask-radial-to":V()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":P()}],"mask-image-conic-pos":[{"mask-conic":[me]}],"mask-image-conic-from-pos":[{"mask-conic-from":Se()}],"mask-image-conic-to-pos":[{"mask-conic-to":Se()}],"mask-image-conic-from-color":[{"mask-conic-from":V()}],"mask-image-conic-to-color":[{"mask-conic-to":V()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:Re()}],"mask-repeat":[{mask:S()}],"mask-size":[{mask:Q()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",te,ee]}],filter:[{filter:["","none",te,ee]}],blur:[{blur:Me()}],brightness:[{brightness:[me,te,ee]}],contrast:[{contrast:[me,te,ee]}],"drop-shadow":[{"drop-shadow":["","none",B,xr,Sr]}],"drop-shadow-color":[{"drop-shadow":V()}],grayscale:[{grayscale:["",me,te,ee]}],"hue-rotate":[{"hue-rotate":[me,te,ee]}],invert:[{invert:["",me,te,ee]}],saturate:[{saturate:[me,te,ee]}],sepia:[{sepia:["",me,te,ee]}],"backdrop-filter":[{"backdrop-filter":["","none",te,ee]}],"backdrop-blur":[{"backdrop-blur":Me()}],"backdrop-brightness":[{"backdrop-brightness":[me,te,ee]}],"backdrop-contrast":[{"backdrop-contrast":[me,te,ee]}],"backdrop-grayscale":[{"backdrop-grayscale":["",me,te,ee]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[me,te,ee]}],"backdrop-invert":[{"backdrop-invert":["",me,te,ee]}],"backdrop-opacity":[{"backdrop-opacity":[me,te,ee]}],"backdrop-saturate":[{"backdrop-saturate":[me,te,ee]}],"backdrop-sepia":[{"backdrop-sepia":["",me,te,ee]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":F()}],"border-spacing-x":[{"border-spacing-x":F()}],"border-spacing-y":[{"border-spacing-y":F()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",te,ee]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[me,"initial",te,ee]}],ease:[{ease:["linear","initial",H,te,ee]}],delay:[{delay:[me,te,ee]}],animate:[{animate:["none",Z,te,ee]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[M,te,ee]}],"perspective-origin":[{"perspective-origin":K()}],rotate:[{rotate:St()}],"rotate-x":[{"rotate-x":St()}],"rotate-y":[{"rotate-y":St()}],"rotate-z":[{"rotate-z":St()}],scale:[{scale:Dn()}],"scale-x":[{"scale-x":Dn()}],"scale-y":[{"scale-y":Dn()}],"scale-z":[{"scale-z":Dn()}],"scale-3d":["scale-3d"],skew:[{skew:zn()}],"skew-x":[{"skew-x":zn()}],"skew-y":[{"skew-y":zn()}],transform:[{transform:[te,ee,"","none","gpu","cpu"]}],"transform-origin":[{origin:K()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:_n()}],"translate-x":[{"translate-x":_n()}],"translate-y":[{"translate-y":_n()}],"translate-z":[{"translate-z":_n()}],"translate-none":["translate-none"],accent:[{accent:V()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:V()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",te,ee]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":F()}],"scroll-mx":[{"scroll-mx":F()}],"scroll-my":[{"scroll-my":F()}],"scroll-ms":[{"scroll-ms":F()}],"scroll-me":[{"scroll-me":F()}],"scroll-mt":[{"scroll-mt":F()}],"scroll-mr":[{"scroll-mr":F()}],"scroll-mb":[{"scroll-mb":F()}],"scroll-ml":[{"scroll-ml":F()}],"scroll-p":[{"scroll-p":F()}],"scroll-px":[{"scroll-px":F()}],"scroll-py":[{"scroll-py":F()}],"scroll-ps":[{"scroll-ps":F()}],"scroll-pe":[{"scroll-pe":F()}],"scroll-pt":[{"scroll-pt":F()}],"scroll-pr":[{"scroll-pr":F()}],"scroll-pb":[{"scroll-pb":F()}],"scroll-pl":[{"scroll-pl":F()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",te,ee]}],fill:[{fill:["none",...V()]}],"stroke-w":[{stroke:[me,Vi,Cl,bo]}],stroke:[{stroke:["none",...V()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Yx=Ex(Gx);function Qx(...a){return Yx(jp(a))}const Vx=sx("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),Nn=_.forwardRef(({className:a,variant:i,size:r,asChild:s=!1,...o},f)=>{const h=s?nx:"button";return O.jsx(h,{className:Qx(Vx({variant:i,size:r,className:a})),ref:f,...o})});Nn.displayName="Button";function Xx(){var B,A;const[a,i]=_.useState(""),[r,s]=_.useState(""),[o,f]=_.useState(!1),[h,g]=_.useState(null),{login:p}=Qr(),m=Pi(),E=((A=(B=Ft().state)==null?void 0:B.from)==null?void 0:A.pathname)||"/",T=async M=>{M.preventDefault(),f(!0),g(null);try{await p(a,r),m(E,{replace:!0})}catch(C){console.error("Login error:",C),g("Invalid username or password. Please try again."),f(!1)}};return O.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background",children:O.jsxs("div",{className:"w-full max-w-md p-8 space-y-8 bg-card rounded-lg shadow-lg",children:[O.jsxs("div",{className:"text-center",children:[O.jsx("h1",{className:"text-2xl font-bold",children:"Imip.Ekb Dashboard"}),O.jsx("p",{className:"text-muted-foreground mt-2",children:"Sign in to your account"})]}),h&&O.jsx("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative",role:"alert",children:O.jsx("span",{className:"block sm:inline",children:h})}),O.jsxs("form",{className:"space-y-6",onSubmit:T,children:[O.jsxs("div",{children:[O.jsx("label",{htmlFor:"username",className:"block text-sm font-medium mb-1",children:"Username"}),O.jsx("input",{id:"username",name:"username",type:"text",required:!0,className:"w-full p-2 border rounded-md",value:a,onChange:M=>i(M.target.value)})]}),O.jsxs("div",{children:[O.jsx("label",{htmlFor:"password",className:"block text-sm font-medium mb-1",children:"Password"}),O.jsx("input",{id:"password",name:"password",type:"password",required:!0,className:"w-full p-2 border rounded-md",value:r,onChange:M=>s(M.target.value)})]}),O.jsxs("div",{className:"flex items-center justify-between",children:[O.jsxs("div",{className:"flex items-center",children:[O.jsx("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 rounded border-gray-300"}),O.jsx("label",{htmlFor:"remember-me",className:"ml-2 block text-sm",children:"Remember me"})]}),O.jsx("div",{className:"text-sm",children:O.jsx(Ta,{to:"/auth/forgot-password",className:"text-primary hover:underline",children:"Forgot your password?"})})]}),O.jsx("div",{children:O.jsx(Nn,{type:"submit",className:"w-full",disabled:o,children:o?"Signing in...":"Sign in"})})]})]})})}/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zx=a=>a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Kx=a=>a.replace(/^([A-Z])|[\s-_]+(\w)/g,(i,r,s)=>s?s.toUpperCase():r.toLowerCase()),Dy=a=>{const i=Kx(a);return i.charAt(0).toUpperCase()+i.slice(1)},Zp=(...a)=>a.filter((i,r,s)=>!!i&&i.trim()!==""&&s.indexOf(i)===r).join(" ").trim(),Jx=a=>{for(const i in a)if(i.startsWith("aria-")||i==="role"||i==="title")return!0};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Fx={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $x=_.forwardRef(({color:a="currentColor",size:i=24,strokeWidth:r=2,absoluteStrokeWidth:s,className:o="",children:f,iconNode:h,...g},p)=>_.createElement("svg",{ref:p,...Fx,width:i,height:i,stroke:a,strokeWidth:s?Number(r)*24/Number(i):r,className:Zp("lucide",o),...!f&&!Jx(g)&&{"aria-hidden":"true"},...g},[...h.map(([m,v])=>_.createElement(m,v)),...Array.isArray(f)?f:[f]]));/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zo=(a,i)=>{const r=_.forwardRef(({className:s,...o},f)=>_.createElement($x,{ref:f,iconNode:i,className:Zp(`lucide-${Zx(Dy(a))}`,`lucide-${a}`,s),...o}));return r.displayName=Dy(a),r};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wx=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],Px=Zo("house",Wx);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ix=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],eE=Zo("settings",Ix);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tE=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],nE=Zo("users",tE);function Ko({children:a}){const{user:i,logout:r}=Qr(),s=Pi(),o=()=>{r(),s("/auth/login")};return O.jsxs("div",{className:"min-h-screen flex flex-col",children:[O.jsx("header",{className:"bg-primary text-primary-foreground py-4 px-6 shadow-md",children:O.jsxs("div",{className:"container mx-auto flex justify-between items-center",children:[O.jsx("h1",{className:"text-xl font-bold",children:"Imip.Ekb Dashboard"}),O.jsxs("div",{className:"flex items-center space-x-4",children:[O.jsxs("span",{children:["Welcome, ",(i==null?void 0:i.name)||(i==null?void 0:i.userName)||"User"]}),O.jsx("button",{className:"bg-primary-foreground/20 hover:bg-primary-foreground/30 px-3 py-1 rounded-md",onClick:o,children:"Logout"})]})]})}),O.jsxs("div",{className:"flex flex-1",children:[O.jsx("aside",{className:"w-64 bg-card text-card-foreground border-r",children:O.jsx("nav",{className:"p-4",children:O.jsxs("ul",{className:"space-y-2",children:[O.jsx("li",{children:O.jsxs(Ta,{to:"/",className:"flex items-center space-x-2 p-2 rounded-md hover:bg-accent hover:text-accent-foreground",children:[O.jsx(Px,{className:"h-5 w-5"}),O.jsx("span",{children:"Dashboard"})]})}),O.jsx("li",{children:O.jsxs(Ta,{to:"/users",className:"flex items-center space-x-2 p-2 rounded-md hover:bg-accent hover:text-accent-foreground",children:[O.jsx(nE,{className:"h-5 w-5"}),O.jsx("span",{children:"Users"})]})}),O.jsx("li",{children:O.jsxs(Ta,{to:"/settings",className:"flex items-center space-x-2 p-2 rounded-md hover:bg-accent hover:text-accent-foreground",children:[O.jsx(eE,{className:"h-5 w-5"}),O.jsx("span",{children:"Settings"})]})})]})})}),O.jsx("main",{className:"flex-1 p-6 bg-background",children:a})]}),O.jsx("footer",{className:"bg-card text-card-foreground py-4 px-6 border-t",children:O.jsx("div",{className:"container mx-auto text-center",children:O.jsxs("p",{children:["© ",new Date().getFullYear()," Imip.Ekb. All rights reserved."]})})})]})}function lE(){return O.jsx(Ko,{children:O.jsxs("div",{className:"space-y-6",children:[O.jsxs("div",{className:"flex justify-between items-center",children:[O.jsx("h1",{className:"text-3xl font-bold",children:"Dashboard"}),O.jsx(Nn,{children:"Refresh"})]}),O.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[O.jsxs("div",{className:"bg-card text-card-foreground rounded-lg shadow-sm p-6",children:[O.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Users"}),O.jsx("p",{className:"text-4xl font-bold",children:"128"}),O.jsx("p",{className:"text-muted-foreground mt-2",children:"Total users in the system"})]}),O.jsxs("div",{className:"bg-card text-card-foreground rounded-lg shadow-sm p-6",children:[O.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Active Sessions"}),O.jsx("p",{className:"text-4xl font-bold",children:"24"}),O.jsx("p",{className:"text-muted-foreground mt-2",children:"Current active sessions"})]}),O.jsxs("div",{className:"bg-card text-card-foreground rounded-lg shadow-sm p-6",children:[O.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Resources"}),O.jsx("p",{className:"text-4xl font-bold",children:"56"}),O.jsx("p",{className:"text-muted-foreground mt-2",children:"Available resources"})]})]}),O.jsxs("div",{className:"bg-card text-card-foreground rounded-lg shadow-sm p-6",children:[O.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Recent Activity"}),O.jsx("div",{className:"space-y-4",children:[1,2,3,4,5].map(a=>O.jsxs("div",{className:"border-b pb-4 last:border-0",children:[O.jsxs("div",{className:"flex justify-between",children:[O.jsxs("p",{className:"font-medium",children:["Activity ",a]}),O.jsx("p",{className:"text-muted-foreground text-sm",children:new Date().toLocaleDateString()})]}),O.jsxs("p",{className:"text-muted-foreground",children:["This is a sample activity description for item ",a,"."]})]},a))})]})]})})}function aE(){return O.jsx(Ko,{children:O.jsxs("div",{className:"space-y-6",children:[O.jsxs("div",{className:"flex justify-between items-center",children:[O.jsx("h1",{className:"text-3xl font-bold",children:"Users"}),O.jsx(Nn,{children:"Add User"})]}),O.jsx("div",{className:"bg-card text-card-foreground rounded-lg shadow-sm overflow-hidden",children:O.jsxs("table",{className:"w-full",children:[O.jsx("thead",{className:"bg-muted",children:O.jsxs("tr",{children:[O.jsx("th",{className:"text-left p-4",children:"ID"}),O.jsx("th",{className:"text-left p-4",children:"Name"}),O.jsx("th",{className:"text-left p-4",children:"Email"}),O.jsx("th",{className:"text-left p-4",children:"Role"}),O.jsx("th",{className:"text-left p-4",children:"Actions"})]})}),O.jsx("tbody",{children:[1,2,3,4,5].map(a=>O.jsxs("tr",{className:"border-b last:border-0",children:[O.jsx("td",{className:"p-4",children:a}),O.jsxs("td",{className:"p-4",children:["User ",a]}),O.jsxs("td",{className:"p-4",children:["user",a,"@example.com"]}),O.jsx("td",{className:"p-4",children:a%2===0?"Admin":"User"}),O.jsx("td",{className:"p-4",children:O.jsxs("div",{className:"flex space-x-2",children:[O.jsx(Nn,{variant:"outline",size:"sm",children:"Edit"}),O.jsx(Nn,{variant:"destructive",size:"sm",children:"Delete"})]})})]},a))})]})}),O.jsxs("div",{className:"flex justify-between items-center",children:[O.jsx("p",{className:"text-muted-foreground",children:"Showing 5 of 128 users"}),O.jsxs("div",{className:"flex space-x-2",children:[O.jsx(Nn,{variant:"outline",size:"sm",children:"Previous"}),O.jsx(Nn,{variant:"outline",size:"sm",children:"Next"})]})]})]})})}function iE(){return O.jsx(Ko,{children:O.jsxs("div",{className:"space-y-6",children:[O.jsxs("div",{className:"flex justify-between items-center",children:[O.jsx("h1",{className:"text-3xl font-bold",children:"Settings"}),O.jsx(Nn,{children:"Save Changes"})]}),O.jsxs("div",{className:"bg-card text-card-foreground rounded-lg shadow-sm p-6",children:[O.jsx("h2",{className:"text-xl font-semibold mb-4",children:"General Settings"}),O.jsxs("div",{className:"space-y-4",children:[O.jsxs("div",{children:[O.jsx("label",{className:"block text-sm font-medium mb-1",htmlFor:"site-name",children:"Site Name"}),O.jsx("input",{id:"site-name",type:"text",className:"w-full p-2 border rounded-md",defaultValue:"Imip.Ekb Dashboard"})]}),O.jsxs("div",{children:[O.jsx("label",{className:"block text-sm font-medium mb-1",htmlFor:"site-description",children:"Site Description"}),O.jsx("textarea",{id:"site-description",className:"w-full p-2 border rounded-md",rows:3,defaultValue:"A dashboard for managing Imip.Ekb application."})]}),O.jsxs("div",{className:"flex items-center space-x-2",children:[O.jsx("input",{id:"enable-dark-mode",type:"checkbox",className:"rounded"}),O.jsx("label",{htmlFor:"enable-dark-mode",children:"Enable Dark Mode"})]})]})]}),O.jsxs("div",{className:"bg-card text-card-foreground rounded-lg shadow-sm p-6",children:[O.jsx("h2",{className:"text-xl font-semibold mb-4",children:"API Settings"}),O.jsxs("div",{className:"space-y-4",children:[O.jsxs("div",{children:[O.jsx("label",{className:"block text-sm font-medium mb-1",htmlFor:"api-url",children:"API URL"}),O.jsx("input",{id:"api-url",type:"text",className:"w-full p-2 border rounded-md",defaultValue:"https://localhost:44359"})]}),O.jsxs("div",{children:[O.jsx("label",{className:"block text-sm font-medium mb-1",htmlFor:"api-key",children:"API Key"}),O.jsx("input",{id:"api-key",type:"password",className:"w-full p-2 border rounded-md",defaultValue:"••••••••••••••••"})]}),O.jsxs("div",{className:"flex items-center space-x-2",children:[O.jsx("input",{id:"enable-api-logging",type:"checkbox",className:"rounded",defaultChecked:!0}),O.jsx("label",{htmlFor:"enable-api-logging",children:"Enable API Logging"})]})]})]})]})})}const uE=new g1;function rE(){return PS(),O.jsxs(yb,{children:[O.jsx(Ra,{path:"/auth/login",element:O.jsx(Xx,{})}),O.jsx(Ra,{path:"/",element:O.jsx(go,{children:O.jsx(lE,{})})}),O.jsx(Ra,{path:"/users",element:O.jsx(go,{children:O.jsx(aE,{})})}),O.jsx(Ra,{path:"/settings",element:O.jsx(go,{children:O.jsx(iE,{})})}),O.jsx(Ra,{path:"*",element:O.jsx(Fy,{to:"/",replace:!0})})]})}function sE(){return O.jsx(b1,{client:uE,children:O.jsx(WS,{children:O.jsx(Lb,{children:O.jsx(rE,{})})})})}R0.createRoot(document.getElementById("root")).render(O.jsx(_.StrictMode,{children:O.jsx(sE,{})}));
