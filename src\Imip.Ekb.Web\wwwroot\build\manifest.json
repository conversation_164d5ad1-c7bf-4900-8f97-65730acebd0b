{"_alert-BKei1lJX.js": {"file": "assets/alert-BKei1lJX.js", "name": "alert", "imports": ["src/App.tsx", "_card-YgUuHGlh.js"]}, "_alert-dialog-BpfNpeUW.js": {"file": "assets/alert-dialog-BpfNpeUW.js", "name": "alert-dialog", "imports": ["_card-YgUuHGlh.js", "src/App.tsx"]}, "_arrow-left-Ji5qialn.js": {"file": "assets/arrow-left-Ji5qialn.js", "name": "arrow-left", "imports": ["_card-YgUuHGlh.js"]}, "_card-YgUuHGlh.js": {"file": "assets/card-YgUuHGlh.js", "name": "card", "imports": ["src/App.tsx"]}, "_select-CfwQ2FQL.js": {"file": "assets/select-CfwQ2FQL.js", "name": "select", "imports": ["src/App.tsx", "_card-YgUuHGlh.js"]}, "src/App.tsx": {"file": "assets/App-BZgNu-zL.js", "name": "App", "src": "src/App.tsx", "isEntry": true, "dynamicImports": ["src/Pages/Employees/Create.tsx", "src/Pages/Employees/Edit.tsx", "src/Pages/Employees/Index.tsx", "src/Pages/Employees/Show.tsx", "src/Pages/Home.tsx"], "css": ["assets/App-CHdW1L7K.css"]}, "src/Pages/Employees/Create.tsx": {"file": "assets/Create-B5iWlxmm.js", "name": "Create", "src": "src/Pages/Employees/Create.tsx", "isDynamicEntry": true, "imports": ["src/App.tsx", "_card-YgUuHGlh.js", "_select-CfwQ2FQL.js", "_alert-BKei1lJX.js", "_arrow-left-Ji5qialn.js"]}, "src/Pages/Employees/Edit.tsx": {"file": "assets/Edit-DgmktHhj.js", "name": "Edit", "src": "src/Pages/Employees/Edit.tsx", "isDynamicEntry": true, "imports": ["src/App.tsx", "_card-YgUuHGlh.js", "_select-CfwQ2FQL.js", "_alert-BKei1lJX.js", "_arrow-left-Ji5qialn.js"]}, "src/Pages/Employees/Index.tsx": {"file": "assets/Index-BlDbDGVw.js", "name": "Index", "src": "src/Pages/Employees/Index.tsx", "isDynamicEntry": true, "imports": ["src/App.tsx", "_card-YgUuHGlh.js", "_select-CfwQ2FQL.js", "_alert-dialog-BpfNpeUW.js"]}, "src/Pages/Employees/Show.tsx": {"file": "assets/Show-WnFh0Ppv.js", "name": "Show", "src": "src/Pages/Employees/Show.tsx", "isDynamicEntry": true, "imports": ["src/App.tsx", "_card-YgUuHGlh.js", "_alert-dialog-BpfNpeUW.js", "_arrow-left-Ji5qialn.js"]}, "src/Pages/Home.tsx": {"file": "assets/Home-DieSi8T8.js", "name": "Home", "src": "src/Pages/Home.tsx", "isDynamicEntry": true, "imports": ["src/App.tsx"]}}