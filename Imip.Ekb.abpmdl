{"template": "app", "imports": {"Volo.Abp.LeptonXLiteTheme": {"version": "4.1.1", "isInstalled": true}, "Volo.Abp.Account": {"version": "9.1.1", "isInstalled": true}, "Volo.Abp.OpenIddict": {"version": "9.1.1", "isInstalled": true}, "Volo.Abp.Identity": {"version": "9.1.1", "isInstalled": true}, "Volo.Abp.TenantManagement": {"version": "9.1.1", "isInstalled": true}, "Volo.Abp.SettingManagement": {"version": "9.1.1", "isInstalled": true}, "Volo.Abp.PermissionManagement": {"version": "9.1.1", "isInstalled": true}, "Volo.Abp.FeatureManagement": {"version": "9.1.1", "isInstalled": true}}, "folders": {"items": {"src": {}, "test": {}}}, "packages": {"Imip.Ekb.Application": {"path": "src/Imip.Ekb.Application/Imip.Ekb.Application.abppkg", "folder": "src"}, "Imip.Ekb.Application.Tests": {"path": "test/Imip.Ekb.Application.Tests/Imip.Ekb.Application.Tests.abppkg", "folder": "test"}, "Imip.Ekb.Domain.Shared": {"path": "src/Imip.Ekb.Domain.Shared/Imip.Ekb.Domain.Shared.abppkg", "folder": "src"}, "Imip.Ekb.Application.Contracts": {"path": "src/Imip.Ekb.Application.Contracts/Imip.Ekb.Application.Contracts.abppkg", "folder": "src"}, "Imip.Ekb.HttpApi": {"path": "src/Imip.Ekb.HttpApi/Imip.Ekb.HttpApi.abppkg", "folder": "src"}, "Imip.Ekb.HttpApi.Client": {"path": "src/Imip.Ekb.HttpApi.Client/Imip.Ekb.HttpApi.Client.abppkg", "folder": "src"}, "Imip.Ekb.Web": {"path": "src/Imip.Ekb.Web/Imip.Ekb.Web.abppkg", "folder": "src"}, "Imip.Ekb.Web.Tests": {"path": "test/Imip.Ekb.Web.Tests/Imip.Ekb.Web.Tests.abppkg", "folder": "test"}, "Imip.Ekb.EntityFrameworkCore.Tests": {"path": "test/Imip.Ekb.EntityFrameworkCore.Tests/Imip.Ekb.EntityFrameworkCore.Tests.abppkg", "folder": "test"}, "Imip.Ekb.EntityFrameworkCore": {"path": "src/Imip.Ekb.EntityFrameworkCore/Imip.Ekb.EntityFrameworkCore.abppkg", "folder": "src"}, "Imip.Ekb.TestBase": {"path": "test/Imip.Ekb.TestBase/Imip.Ekb.TestBase.abppkg", "folder": "test"}, "Imip.Ekb.Domain.Tests": {"path": "test/Imip.Ekb.Domain.Tests/Imip.Ekb.Domain.Tests.abppkg", "folder": "test"}, "Imip.Ekb.HttpApi.Client.ConsoleTestApp": {"path": "test/Imip.Ekb.HttpApi.Client.ConsoleTestApp/Imip.Ekb.HttpApi.Client.ConsoleTestApp.abppkg", "folder": "test"}, "Imip.Ekb.DbMigrator": {"path": "src/Imip.Ekb.DbMigrator/Imip.Ekb.DbMigrator.abppkg", "folder": "src"}, "Imip.Ekb.Domain": {"path": "src/Imip.Ekb.Domain/Imip.Ekb.Domain.abppkg", "folder": "src"}}}