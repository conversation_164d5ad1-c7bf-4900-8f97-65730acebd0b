using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Products
{
    /// <summary>
    /// Interface for product application service
    /// </summary>
    public interface IProductAppService : 
        ICrudAppService<
            ProductDto,                  // Used to show products
            Guid,                        // Primary key type
            GetProductListDto,           // Used for paging/sorting/filtering
            CreateUpdateProductDto,      // Used to create a product
            CreateUpdateProductDto>      // Used to update a product
    {
        /// <summary>
        /// Checks if a SKU is already in use
        /// </summary>
        /// <param name="sku">The SKU to check</param>
        /// <returns>True if the SKU is already in use, false otherwise</returns>
        Task<bool> IsSkuInUseAsync(string sku);
        
        /// <summary>
        /// Updates the stock quantity of a product
        /// </summary>
        /// <param name="id">Product ID</param>
        /// <param name="newQuantity">New stock quantity</param>
        /// <returns>Updated product DTO</returns>
        Task<ProductDto> UpdateStockQuantityAsync(Guid id, int newQuantity);
    }
}
