﻿using Volo.Abp.Ui.Branding;
using Volo.Abp.DependencyInjection;
using Microsoft.Extensions.Localization;
using Imip.Ekb.Localization;

namespace Imip.Ekb.Web;

[Dependency(ReplaceServices = true)]
public class EkbBrandingProvider : DefaultBrandingProvider
{
    private IStringLocalizer<EkbResource> _localizer;

    public EkbBrandingProvider(IStringLocalizer<EkbResource> localizer)
    {
        _localizer = localizer;
    }

    public override string AppName => _localizer["AppName"];
}
