using Microsoft.AspNetCore.Builder;

namespace Imip.Ekb.Web.Middleware
{
    /// <summary>
    /// Extension methods for the CsrfTokenMiddleware
    /// </summary>
    public static class CsrfTokenMiddlewareExtensions
    {
        /// <summary>
        /// Adds the CSRF token middleware to the request pipeline
        /// </summary>
        public static IApplicationBuilder UseCsrfTokenMiddleware(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<CsrfTokenMiddleware>();
        }
    }
}
