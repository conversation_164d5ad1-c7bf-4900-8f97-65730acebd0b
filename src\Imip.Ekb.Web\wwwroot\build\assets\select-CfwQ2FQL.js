import{j as v,R as he,r as u}from"./App-BZgNu-zL.js";import{d as ge,g as st,h as Y,j as Ze,r as ct,P as U,k as Q,l as It,u as yt,m as lt,i as z,n as En,o as On,p as In,R as Nn,F as _n,D as Mn,e as at}from"./card-YgUuHGlh.js";function Fr({className:e,type:t,...n}){return v.jsx("input",{type:t,"data-slot":"input",className:ge("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...n})}function St(e,[t,n]){return Math.min(n,Math.max(t,e))}function Dn(e){const t=e+"CollectionProvider",[n,o]=st(t),[r,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=d=>{const{scope:g,children:S}=d,m=he.useRef(null),x=he.useRef(new Map).current;return v.jsx(r,{scope:g,itemMap:x,collectionRef:m,children:S})};i.displayName=t;const c=e+"CollectionSlot",l=Ze(c),a=he.forwardRef((d,g)=>{const{scope:S,children:m}=d,x=s(c,S),b=Y(g,x.collectionRef);return v.jsx(l,{ref:b,children:m})});a.displayName=c;const f=e+"CollectionItemSlot",p="data-radix-collection-item",y=Ze(f),h=he.forwardRef((d,g)=>{const{scope:S,children:m,...x}=d,b=he.useRef(null),C=Y(g,b),T=s(f,S);return he.useEffect(()=>(T.itemMap.set(b,{ref:b,...x}),()=>void T.itemMap.delete(b))),v.jsx(y,{[p]:"",ref:C,children:m})});h.displayName=f;function w(d){const g=s(e+"CollectionConsumer",d);return he.useCallback(()=>{const m=g.collectionRef.current;if(!m)return[];const x=Array.from(m.querySelectorAll(`[${p}]`));return Array.from(g.itemMap.values()).sort((T,A)=>x.indexOf(T.ref.current)-x.indexOf(A.ref.current))},[g.collectionRef,g.itemMap])}return[{Provider:i,Slot:a,ItemSlot:h},w,o]}var Ln=u.createContext(void 0);function jn(e){const t=u.useContext(Ln);return e||t||"ltr"}const kn=["top","right","bottom","left"],de=Math.min,q=Math.max,He=Math.round,je=Math.floor,oe=e=>({x:e,y:e}),Hn={left:"right",right:"left",bottom:"top",top:"bottom"},Bn={start:"end",end:"start"};function Je(e,t,n){return q(e,de(t,n))}function ce(e,t){return typeof e=="function"?e(t):e}function le(e){return e.split("-")[0]}function Ce(e){return e.split("-")[1]}function dt(e){return e==="x"?"y":"x"}function ft(e){return e==="y"?"height":"width"}function se(e){return["top","bottom"].includes(le(e))?"y":"x"}function ut(e){return dt(se(e))}function Vn(e,t,n){n===void 0&&(n=!1);const o=Ce(e),r=ut(e),s=ft(r);let i=r==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=Be(i)),[i,Be(i)]}function Fn(e){const t=Be(e);return[Qe(e),t,Qe(t)]}function Qe(e){return e.replace(/start|end/g,t=>Bn[t])}function Wn(e,t,n){const o=["left","right"],r=["right","left"],s=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?r:o:t?o:r;case"left":case"right":return t?s:i;default:return[]}}function $n(e,t,n,o){const r=Ce(e);let s=Wn(le(e),n==="start",o);return r&&(s=s.map(i=>i+"-"+r),t&&(s=s.concat(s.map(Qe)))),s}function Be(e){return e.replace(/left|right|bottom|top/g,t=>Hn[t])}function zn(e){return{top:0,right:0,bottom:0,left:0,...e}}function Nt(e){return typeof e!="number"?zn(e):{top:e,right:e,bottom:e,left:e}}function Ve(e){const{x:t,y:n,width:o,height:r}=e;return{width:o,height:r,top:n,left:t,right:t+o,bottom:n+r,x:t,y:n}}function bt(e,t,n){let{reference:o,floating:r}=e;const s=se(t),i=ut(t),c=ft(i),l=le(t),a=s==="y",f=o.x+o.width/2-r.width/2,p=o.y+o.height/2-r.height/2,y=o[c]/2-r[c]/2;let h;switch(l){case"top":h={x:f,y:o.y-r.height};break;case"bottom":h={x:f,y:o.y+o.height};break;case"right":h={x:o.x+o.width,y:p};break;case"left":h={x:o.x-r.width,y:p};break;default:h={x:o.x,y:o.y}}switch(Ce(t)){case"start":h[i]-=y*(n&&a?-1:1);break;case"end":h[i]+=y*(n&&a?-1:1);break}return h}const Un=async(e,t,n)=>{const{placement:o="bottom",strategy:r="absolute",middleware:s=[],platform:i}=n,c=s.filter(Boolean),l=await(i.isRTL==null?void 0:i.isRTL(t));let a=await i.getElementRects({reference:e,floating:t,strategy:r}),{x:f,y:p}=bt(a,o,l),y=o,h={},w=0;for(let d=0;d<c.length;d++){const{name:g,fn:S}=c[d],{x:m,y:x,data:b,reset:C}=await S({x:f,y:p,initialPlacement:o,placement:y,strategy:r,middlewareData:h,rects:a,platform:i,elements:{reference:e,floating:t}});f=m??f,p=x??p,h={...h,[g]:{...h[g],...b}},C&&w<=50&&(w++,typeof C=="object"&&(C.placement&&(y=C.placement),C.rects&&(a=C.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:r}):C.rects),{x:f,y:p}=bt(a,y,l)),d=-1)}return{x:f,y:p,placement:y,strategy:r,middlewareData:h}};async function _e(e,t){var n;t===void 0&&(t={});const{x:o,y:r,platform:s,rects:i,elements:c,strategy:l}=e,{boundary:a="clippingAncestors",rootBoundary:f="viewport",elementContext:p="floating",altBoundary:y=!1,padding:h=0}=ce(t,e),w=Nt(h),g=c[y?p==="floating"?"reference":"floating":p],S=Ve(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(g)))==null||n?g:g.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(c.floating)),boundary:a,rootBoundary:f,strategy:l})),m=p==="floating"?{x:o,y:r,width:i.floating.width,height:i.floating.height}:i.reference,x=await(s.getOffsetParent==null?void 0:s.getOffsetParent(c.floating)),b=await(s.isElement==null?void 0:s.isElement(x))?await(s.getScale==null?void 0:s.getScale(x))||{x:1,y:1}:{x:1,y:1},C=Ve(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:m,offsetParent:x,strategy:l}):m);return{top:(S.top-C.top+w.top)/b.y,bottom:(C.bottom-S.bottom+w.bottom)/b.y,left:(S.left-C.left+w.left)/b.x,right:(C.right-S.right+w.right)/b.x}}const Kn=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:r,rects:s,platform:i,elements:c,middlewareData:l}=t,{element:a,padding:f=0}=ce(e,t)||{};if(a==null)return{};const p=Nt(f),y={x:n,y:o},h=ut(r),w=ft(h),d=await i.getDimensions(a),g=h==="y",S=g?"top":"left",m=g?"bottom":"right",x=g?"clientHeight":"clientWidth",b=s.reference[w]+s.reference[h]-y[h]-s.floating[w],C=y[h]-s.reference[h],T=await(i.getOffsetParent==null?void 0:i.getOffsetParent(a));let A=T?T[x]:0;(!A||!await(i.isElement==null?void 0:i.isElement(T)))&&(A=c.floating[x]||s.floating[w]);const O=b/2-C/2,W=A/2-d[w]/2-1,j=de(p[S],W),B=de(p[m],W),V=j,I=A-d[w]-B,k=A/2-d[w]/2+O,D=Je(V,k,I),E=!l.arrow&&Ce(r)!=null&&k!==D&&s.reference[w]/2-(k<V?j:B)-d[w]/2<0,N=E?k<V?k-V:k-I:0;return{[h]:y[h]+N,data:{[h]:D,centerOffset:k-D-N,...E&&{alignmentOffset:N}},reset:E}}}),Yn=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,o;const{placement:r,middlewareData:s,rects:i,initialPlacement:c,platform:l,elements:a}=t,{mainAxis:f=!0,crossAxis:p=!0,fallbackPlacements:y,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:w="none",flipAlignment:d=!0,...g}=ce(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const S=le(r),m=se(c),x=le(c)===c,b=await(l.isRTL==null?void 0:l.isRTL(a.floating)),C=y||(x||!d?[Be(c)]:Fn(c)),T=w!=="none";!y&&T&&C.push(...$n(c,d,w,b));const A=[c,...C],O=await _e(t,g),W=[];let j=((o=s.flip)==null?void 0:o.overflows)||[];if(f&&W.push(O[S]),p){const D=Vn(r,i,b);W.push(O[D[0]],O[D[1]])}if(j=[...j,{placement:r,overflows:W}],!W.every(D=>D<=0)){var B,V;const D=(((B=s.flip)==null?void 0:B.index)||0)+1,E=A[D];if(E){var I;const P=p==="alignment"?m!==se(E):!1,L=((I=j[0])==null?void 0:I.overflows[0])>0;if(!P||L)return{data:{index:D,overflows:j},reset:{placement:E}}}let N=(V=j.filter(P=>P.overflows[0]<=0).sort((P,L)=>P.overflows[1]-L.overflows[1])[0])==null?void 0:V.placement;if(!N)switch(h){case"bestFit":{var k;const P=(k=j.filter(L=>{if(T){const $=se(L.placement);return $===m||$==="y"}return!0}).map(L=>[L.placement,L.overflows.filter($=>$>0).reduce(($,Z)=>$+Z,0)]).sort((L,$)=>L[1]-$[1])[0])==null?void 0:k[0];P&&(N=P);break}case"initialPlacement":N=c;break}if(r!==N)return{reset:{placement:N}}}return{}}}};function Ct(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Rt(e){return kn.some(t=>e[t]>=0)}const Xn=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:o="referenceHidden",...r}=ce(e,t);switch(o){case"referenceHidden":{const s=await _e(t,{...r,elementContext:"reference"}),i=Ct(s,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:Rt(i)}}}case"escaped":{const s=await _e(t,{...r,altBoundary:!0}),i=Ct(s,n.floating);return{data:{escapedOffsets:i,escaped:Rt(i)}}}default:return{}}}}};async function qn(e,t){const{placement:n,platform:o,elements:r}=e,s=await(o.isRTL==null?void 0:o.isRTL(r.floating)),i=le(n),c=Ce(n),l=se(n)==="y",a=["left","top"].includes(i)?-1:1,f=s&&l?-1:1,p=ce(t,e);let{mainAxis:y,crossAxis:h,alignmentAxis:w}=typeof p=="number"?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return c&&typeof w=="number"&&(h=c==="end"?w*-1:w),l?{x:h*f,y:y*a}:{x:y*a,y:h*f}}const Gn=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,o;const{x:r,y:s,placement:i,middlewareData:c}=t,l=await qn(t,e);return i===((n=c.offset)==null?void 0:n.placement)&&(o=c.arrow)!=null&&o.alignmentOffset?{}:{x:r+l.x,y:s+l.y,data:{...l,placement:i}}}}},Zn=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:o,placement:r}=t,{mainAxis:s=!0,crossAxis:i=!1,limiter:c={fn:g=>{let{x:S,y:m}=g;return{x:S,y:m}}},...l}=ce(e,t),a={x:n,y:o},f=await _e(t,l),p=se(le(r)),y=dt(p);let h=a[y],w=a[p];if(s){const g=y==="y"?"top":"left",S=y==="y"?"bottom":"right",m=h+f[g],x=h-f[S];h=Je(m,h,x)}if(i){const g=p==="y"?"top":"left",S=p==="y"?"bottom":"right",m=w+f[g],x=w-f[S];w=Je(m,w,x)}const d=c.fn({...t,[y]:h,[p]:w});return{...d,data:{x:d.x-n,y:d.y-o,enabled:{[y]:s,[p]:i}}}}}},Jn=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:o,placement:r,rects:s,middlewareData:i}=t,{offset:c=0,mainAxis:l=!0,crossAxis:a=!0}=ce(e,t),f={x:n,y:o},p=se(r),y=dt(p);let h=f[y],w=f[p];const d=ce(c,t),g=typeof d=="number"?{mainAxis:d,crossAxis:0}:{mainAxis:0,crossAxis:0,...d};if(l){const x=y==="y"?"height":"width",b=s.reference[y]-s.floating[x]+g.mainAxis,C=s.reference[y]+s.reference[x]-g.mainAxis;h<b?h=b:h>C&&(h=C)}if(a){var S,m;const x=y==="y"?"width":"height",b=["top","left"].includes(le(r)),C=s.reference[p]-s.floating[x]+(b&&((S=i.offset)==null?void 0:S[p])||0)+(b?0:g.crossAxis),T=s.reference[p]+s.reference[x]+(b?0:((m=i.offset)==null?void 0:m[p])||0)-(b?g.crossAxis:0);w<C?w=C:w>T&&(w=T)}return{[y]:h,[p]:w}}}},Qn=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,o;const{placement:r,rects:s,platform:i,elements:c}=t,{apply:l=()=>{},...a}=ce(e,t),f=await _e(t,a),p=le(r),y=Ce(r),h=se(r)==="y",{width:w,height:d}=s.floating;let g,S;p==="top"||p==="bottom"?(g=p,S=y===(await(i.isRTL==null?void 0:i.isRTL(c.floating))?"start":"end")?"left":"right"):(S=p,g=y==="end"?"top":"bottom");const m=d-f.top-f.bottom,x=w-f.left-f.right,b=de(d-f[g],m),C=de(w-f[S],x),T=!t.middlewareData.shift;let A=b,O=C;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(O=x),(o=t.middlewareData.shift)!=null&&o.enabled.y&&(A=m),T&&!y){const j=q(f.left,0),B=q(f.right,0),V=q(f.top,0),I=q(f.bottom,0);h?O=w-2*(j!==0||B!==0?j+B:q(f.left,f.right)):A=d-2*(V!==0||I!==0?V+I:q(f.top,f.bottom))}await l({...t,availableWidth:O,availableHeight:A});const W=await i.getDimensions(c.floating);return w!==W.width||d!==W.height?{reset:{rects:!0}}:{}}}};function $e(){return typeof window<"u"}function Re(e){return _t(e)?(e.nodeName||"").toLowerCase():"#document"}function G(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function ie(e){var t;return(t=(_t(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function _t(e){return $e()?e instanceof Node||e instanceof G(e).Node:!1}function ee(e){return $e()?e instanceof Element||e instanceof G(e).Element:!1}function re(e){return $e()?e instanceof HTMLElement||e instanceof G(e).HTMLElement:!1}function At(e){return!$e()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof G(e).ShadowRoot}function De(e){const{overflow:t,overflowX:n,overflowY:o,display:r}=te(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!["inline","contents"].includes(r)}function eo(e){return["table","td","th"].includes(Re(e))}function ze(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function pt(e){const t=mt(),n=ee(e)?te(e):e;return["transform","translate","scale","rotate","perspective"].some(o=>n[o]?n[o]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(o=>(n.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(n.contain||"").includes(o))}function to(e){let t=fe(e);for(;re(t)&&!be(t);){if(pt(t))return t;if(ze(t))return null;t=fe(t)}return null}function mt(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function be(e){return["html","body","#document"].includes(Re(e))}function te(e){return G(e).getComputedStyle(e)}function Ue(e){return ee(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function fe(e){if(Re(e)==="html")return e;const t=e.assignedSlot||e.parentNode||At(e)&&e.host||ie(e);return At(t)?t.host:t}function Mt(e){const t=fe(e);return be(t)?e.ownerDocument?e.ownerDocument.body:e.body:re(t)&&De(t)?t:Mt(t)}function Me(e,t,n){var o;t===void 0&&(t=[]),n===void 0&&(n=!0);const r=Mt(e),s=r===((o=e.ownerDocument)==null?void 0:o.body),i=G(r);if(s){const c=et(i);return t.concat(i,i.visualViewport||[],De(r)?r:[],c&&n?Me(c):[])}return t.concat(r,Me(r,[],n))}function et(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Dt(e){const t=te(e);let n=parseFloat(t.width)||0,o=parseFloat(t.height)||0;const r=re(e),s=r?e.offsetWidth:n,i=r?e.offsetHeight:o,c=He(n)!==s||He(o)!==i;return c&&(n=s,o=i),{width:n,height:o,$:c}}function ht(e){return ee(e)?e:e.contextElement}function Se(e){const t=ht(e);if(!re(t))return oe(1);const n=t.getBoundingClientRect(),{width:o,height:r,$:s}=Dt(t);let i=(s?He(n.width):n.width)/o,c=(s?He(n.height):n.height)/r;return(!i||!Number.isFinite(i))&&(i=1),(!c||!Number.isFinite(c))&&(c=1),{x:i,y:c}}const no=oe(0);function Lt(e){const t=G(e);return!mt()||!t.visualViewport?no:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function oo(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==G(e)?!1:t}function xe(e,t,n,o){t===void 0&&(t=!1),n===void 0&&(n=!1);const r=e.getBoundingClientRect(),s=ht(e);let i=oe(1);t&&(o?ee(o)&&(i=Se(o)):i=Se(e));const c=oo(s,n,o)?Lt(s):oe(0);let l=(r.left+c.x)/i.x,a=(r.top+c.y)/i.y,f=r.width/i.x,p=r.height/i.y;if(s){const y=G(s),h=o&&ee(o)?G(o):o;let w=y,d=et(w);for(;d&&o&&h!==w;){const g=Se(d),S=d.getBoundingClientRect(),m=te(d),x=S.left+(d.clientLeft+parseFloat(m.paddingLeft))*g.x,b=S.top+(d.clientTop+parseFloat(m.paddingTop))*g.y;l*=g.x,a*=g.y,f*=g.x,p*=g.y,l+=x,a+=b,w=G(d),d=et(w)}}return Ve({width:f,height:p,x:l,y:a})}function gt(e,t){const n=Ue(e).scrollLeft;return t?t.left+n:xe(ie(e)).left+n}function jt(e,t,n){n===void 0&&(n=!1);const o=e.getBoundingClientRect(),r=o.left+t.scrollLeft-(n?0:gt(e,o)),s=o.top+t.scrollTop;return{x:r,y:s}}function ro(e){let{elements:t,rect:n,offsetParent:o,strategy:r}=e;const s=r==="fixed",i=ie(o),c=t?ze(t.floating):!1;if(o===i||c&&s)return n;let l={scrollLeft:0,scrollTop:0},a=oe(1);const f=oe(0),p=re(o);if((p||!p&&!s)&&((Re(o)!=="body"||De(i))&&(l=Ue(o)),re(o))){const h=xe(o);a=Se(o),f.x=h.x+o.clientLeft,f.y=h.y+o.clientTop}const y=i&&!p&&!s?jt(i,l,!0):oe(0);return{width:n.width*a.x,height:n.height*a.y,x:n.x*a.x-l.scrollLeft*a.x+f.x+y.x,y:n.y*a.y-l.scrollTop*a.y+f.y+y.y}}function io(e){return Array.from(e.getClientRects())}function so(e){const t=ie(e),n=Ue(e),o=e.ownerDocument.body,r=q(t.scrollWidth,t.clientWidth,o.scrollWidth,o.clientWidth),s=q(t.scrollHeight,t.clientHeight,o.scrollHeight,o.clientHeight);let i=-n.scrollLeft+gt(e);const c=-n.scrollTop;return te(o).direction==="rtl"&&(i+=q(t.clientWidth,o.clientWidth)-r),{width:r,height:s,x:i,y:c}}function co(e,t){const n=G(e),o=ie(e),r=n.visualViewport;let s=o.clientWidth,i=o.clientHeight,c=0,l=0;if(r){s=r.width,i=r.height;const a=mt();(!a||a&&t==="fixed")&&(c=r.offsetLeft,l=r.offsetTop)}return{width:s,height:i,x:c,y:l}}function lo(e,t){const n=xe(e,!0,t==="fixed"),o=n.top+e.clientTop,r=n.left+e.clientLeft,s=re(e)?Se(e):oe(1),i=e.clientWidth*s.x,c=e.clientHeight*s.y,l=r*s.x,a=o*s.y;return{width:i,height:c,x:l,y:a}}function Pt(e,t,n){let o;if(t==="viewport")o=co(e,n);else if(t==="document")o=so(ie(e));else if(ee(t))o=lo(t,n);else{const r=Lt(e);o={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return Ve(o)}function kt(e,t){const n=fe(e);return n===t||!ee(n)||be(n)?!1:te(n).position==="fixed"||kt(n,t)}function ao(e,t){const n=t.get(e);if(n)return n;let o=Me(e,[],!1).filter(c=>ee(c)&&Re(c)!=="body"),r=null;const s=te(e).position==="fixed";let i=s?fe(e):e;for(;ee(i)&&!be(i);){const c=te(i),l=pt(i);!l&&c.position==="fixed"&&(r=null),(s?!l&&!r:!l&&c.position==="static"&&!!r&&["absolute","fixed"].includes(r.position)||De(i)&&!l&&kt(e,i))?o=o.filter(f=>f!==i):r=c,i=fe(i)}return t.set(e,o),o}function fo(e){let{element:t,boundary:n,rootBoundary:o,strategy:r}=e;const i=[...n==="clippingAncestors"?ze(t)?[]:ao(t,this._c):[].concat(n),o],c=i[0],l=i.reduce((a,f)=>{const p=Pt(t,f,r);return a.top=q(p.top,a.top),a.right=de(p.right,a.right),a.bottom=de(p.bottom,a.bottom),a.left=q(p.left,a.left),a},Pt(t,c,r));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function uo(e){const{width:t,height:n}=Dt(e);return{width:t,height:n}}function po(e,t,n){const o=re(t),r=ie(t),s=n==="fixed",i=xe(e,!0,s,t);let c={scrollLeft:0,scrollTop:0};const l=oe(0);function a(){l.x=gt(r)}if(o||!o&&!s)if((Re(t)!=="body"||De(r))&&(c=Ue(t)),o){const h=xe(t,!0,s,t);l.x=h.x+t.clientLeft,l.y=h.y+t.clientTop}else r&&a();s&&!o&&r&&a();const f=r&&!o&&!s?jt(r,c):oe(0),p=i.left+c.scrollLeft-l.x-f.x,y=i.top+c.scrollTop-l.y-f.y;return{x:p,y,width:i.width,height:i.height}}function qe(e){return te(e).position==="static"}function Tt(e,t){if(!re(e)||te(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return ie(e)===n&&(n=n.ownerDocument.body),n}function Ht(e,t){const n=G(e);if(ze(e))return n;if(!re(e)){let r=fe(e);for(;r&&!be(r);){if(ee(r)&&!qe(r))return r;r=fe(r)}return n}let o=Tt(e,t);for(;o&&eo(o)&&qe(o);)o=Tt(o,t);return o&&be(o)&&qe(o)&&!pt(o)?n:o||to(e)||n}const mo=async function(e){const t=this.getOffsetParent||Ht,n=this.getDimensions,o=await n(e.floating);return{reference:po(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function ho(e){return te(e).direction==="rtl"}const go={convertOffsetParentRelativeRectToViewportRelativeRect:ro,getDocumentElement:ie,getClippingRect:fo,getOffsetParent:Ht,getElementRects:mo,getClientRects:io,getDimensions:uo,getScale:Se,isElement:ee,isRTL:ho};function Bt(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function xo(e,t){let n=null,o;const r=ie(e);function s(){var c;clearTimeout(o),(c=n)==null||c.disconnect(),n=null}function i(c,l){c===void 0&&(c=!1),l===void 0&&(l=1),s();const a=e.getBoundingClientRect(),{left:f,top:p,width:y,height:h}=a;if(c||t(),!y||!h)return;const w=je(p),d=je(r.clientWidth-(f+y)),g=je(r.clientHeight-(p+h)),S=je(f),x={rootMargin:-w+"px "+-d+"px "+-g+"px "+-S+"px",threshold:q(0,de(1,l))||1};let b=!0;function C(T){const A=T[0].intersectionRatio;if(A!==l){if(!b)return i();A?i(!1,A):o=setTimeout(()=>{i(!1,1e-7)},1e3)}A===1&&!Bt(a,e.getBoundingClientRect())&&i(),b=!1}try{n=new IntersectionObserver(C,{...x,root:r.ownerDocument})}catch{n=new IntersectionObserver(C,x)}n.observe(e)}return i(!0),s}function wo(e,t,n,o){o===void 0&&(o={});const{ancestorScroll:r=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:l=!1}=o,a=ht(e),f=r||s?[...a?Me(a):[],...Me(t)]:[];f.forEach(S=>{r&&S.addEventListener("scroll",n,{passive:!0}),s&&S.addEventListener("resize",n)});const p=a&&c?xo(a,n):null;let y=-1,h=null;i&&(h=new ResizeObserver(S=>{let[m]=S;m&&m.target===a&&h&&(h.unobserve(t),cancelAnimationFrame(y),y=requestAnimationFrame(()=>{var x;(x=h)==null||x.observe(t)})),n()}),a&&!l&&h.observe(a),h.observe(t));let w,d=l?xe(e):null;l&&g();function g(){const S=xe(e);d&&!Bt(d,S)&&n(),d=S,w=requestAnimationFrame(g)}return n(),()=>{var S;f.forEach(m=>{r&&m.removeEventListener("scroll",n),s&&m.removeEventListener("resize",n)}),p==null||p(),(S=h)==null||S.disconnect(),h=null,l&&cancelAnimationFrame(w)}}const vo=Gn,yo=Zn,So=Yn,bo=Qn,Co=Xn,Et=Kn,Ro=Jn,Ao=(e,t,n)=>{const o=new Map,r={platform:go,...n},s={...r.platform,_c:o};return Un(e,t,{...r,platform:s})};var ke=typeof document<"u"?u.useLayoutEffect:u.useEffect;function Fe(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,o,r;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(o=n;o--!==0;)if(!Fe(e[o],t[o]))return!1;return!0}if(r=Object.keys(e),n=r.length,n!==Object.keys(t).length)return!1;for(o=n;o--!==0;)if(!{}.hasOwnProperty.call(t,r[o]))return!1;for(o=n;o--!==0;){const s=r[o];if(!(s==="_owner"&&e.$$typeof)&&!Fe(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function Vt(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Ot(e,t){const n=Vt(e);return Math.round(t*n)/n}function Ge(e){const t=u.useRef(e);return ke(()=>{t.current=e}),t}function Po(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:r,elements:{reference:s,floating:i}={},transform:c=!0,whileElementsMounted:l,open:a}=e,[f,p]=u.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[y,h]=u.useState(o);Fe(y,o)||h(o);const[w,d]=u.useState(null),[g,S]=u.useState(null),m=u.useCallback(P=>{P!==T.current&&(T.current=P,d(P))},[]),x=u.useCallback(P=>{P!==A.current&&(A.current=P,S(P))},[]),b=s||w,C=i||g,T=u.useRef(null),A=u.useRef(null),O=u.useRef(f),W=l!=null,j=Ge(l),B=Ge(r),V=Ge(a),I=u.useCallback(()=>{if(!T.current||!A.current)return;const P={placement:t,strategy:n,middleware:y};B.current&&(P.platform=B.current),Ao(T.current,A.current,P).then(L=>{const $={...L,isPositioned:V.current!==!1};k.current&&!Fe(O.current,$)&&(O.current=$,ct.flushSync(()=>{p($)}))})},[y,t,n,B,V]);ke(()=>{a===!1&&O.current.isPositioned&&(O.current.isPositioned=!1,p(P=>({...P,isPositioned:!1})))},[a]);const k=u.useRef(!1);ke(()=>(k.current=!0,()=>{k.current=!1}),[]),ke(()=>{if(b&&(T.current=b),C&&(A.current=C),b&&C){if(j.current)return j.current(b,C,I);I()}},[b,C,I,j,W]);const D=u.useMemo(()=>({reference:T,floating:A,setReference:m,setFloating:x}),[m,x]),E=u.useMemo(()=>({reference:b,floating:C}),[b,C]),N=u.useMemo(()=>{const P={position:n,left:0,top:0};if(!E.floating)return P;const L=Ot(E.floating,f.x),$=Ot(E.floating,f.y);return c?{...P,transform:"translate("+L+"px, "+$+"px)",...Vt(E.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:L,top:$}},[n,c,E.floating,f.x,f.y]);return u.useMemo(()=>({...f,update:I,refs:D,elements:E,floatingStyles:N}),[f,I,D,E,N])}const To=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:o,padding:r}=typeof e=="function"?e(n):e;return o&&t(o)?o.current!=null?Et({element:o.current,padding:r}).fn(n):{}:o?Et({element:o,padding:r}).fn(n):{}}}},Eo=(e,t)=>({...vo(e),options:[e,t]}),Oo=(e,t)=>({...yo(e),options:[e,t]}),Io=(e,t)=>({...Ro(e),options:[e,t]}),No=(e,t)=>({...So(e),options:[e,t]}),_o=(e,t)=>({...bo(e),options:[e,t]}),Mo=(e,t)=>({...Co(e),options:[e,t]}),Do=(e,t)=>({...To(e),options:[e,t]});var Lo="Arrow",Ft=u.forwardRef((e,t)=>{const{children:n,width:o=10,height:r=5,...s}=e;return v.jsx(U.svg,{...s,ref:t,width:o,height:r,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:v.jsx("polygon",{points:"0,0 30,0 15,10"})})});Ft.displayName=Lo;var jo=Ft;function ko(e){const[t,n]=u.useState(void 0);return Q(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const o=new ResizeObserver(r=>{if(!Array.isArray(r)||!r.length)return;const s=r[0];let i,c;if("borderBoxSize"in s){const l=s.borderBoxSize,a=Array.isArray(l)?l[0]:l;i=a.inlineSize,c=a.blockSize}else i=e.offsetWidth,c=e.offsetHeight;n({width:i,height:c})});return o.observe(e,{box:"border-box"}),()=>o.unobserve(e)}else n(void 0)},[e]),t}var xt="Popper",[Wt,$t]=st(xt),[Ho,zt]=Wt(xt),Ut=e=>{const{__scopePopper:t,children:n}=e,[o,r]=u.useState(null);return v.jsx(Ho,{scope:t,anchor:o,onAnchorChange:r,children:n})};Ut.displayName=xt;var Kt="PopperAnchor",Yt=u.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:o,...r}=e,s=zt(Kt,n),i=u.useRef(null),c=Y(t,i);return u.useEffect(()=>{s.onAnchorChange((o==null?void 0:o.current)||i.current)}),o?null:v.jsx(U.div,{...r,ref:c})});Yt.displayName=Kt;var wt="PopperContent",[Bo,Vo]=Wt(wt),Xt=u.forwardRef((e,t)=>{var R,F,K,H,_,M;const{__scopePopper:n,side:o="bottom",sideOffset:r=0,align:s="center",alignOffset:i=0,arrowPadding:c=0,avoidCollisions:l=!0,collisionBoundary:a=[],collisionPadding:f=0,sticky:p="partial",hideWhenDetached:y=!1,updatePositionStrategy:h="optimized",onPlaced:w,...d}=e,g=zt(wt,n),[S,m]=u.useState(null),x=Y(t,X=>m(X)),[b,C]=u.useState(null),T=ko(b),A=(T==null?void 0:T.width)??0,O=(T==null?void 0:T.height)??0,W=o+(s!=="center"?"-"+s:""),j=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},B=Array.isArray(a)?a:[a],V=B.length>0,I={padding:j,boundary:B.filter(Wo),altBoundary:V},{refs:k,floatingStyles:D,placement:E,isPositioned:N,middlewareData:P}=Po({strategy:"fixed",placement:W,whileElementsMounted:(...X)=>wo(...X,{animationFrame:h==="always"}),elements:{reference:g.anchor},middleware:[Eo({mainAxis:r+O,alignmentAxis:i}),l&&Oo({mainAxis:!0,crossAxis:!1,limiter:p==="partial"?Io():void 0,...I}),l&&No({...I}),_o({...I,apply:({elements:X,rects:ne,availableWidth:Ee,availableHeight:Oe})=>{const{width:Ie,height:Tn}=ne.reference,Le=X.floating.style;Le.setProperty("--radix-popper-available-width",`${Ee}px`),Le.setProperty("--radix-popper-available-height",`${Oe}px`),Le.setProperty("--radix-popper-anchor-width",`${Ie}px`),Le.setProperty("--radix-popper-anchor-height",`${Tn}px`)}}),b&&Do({element:b,padding:c}),$o({arrowWidth:A,arrowHeight:O}),y&&Mo({strategy:"referenceHidden",...I})]}),[L,$]=Zt(E),Z=It(w);Q(()=>{N&&(Z==null||Z())},[N,Z]);const Pe=(R=P.arrow)==null?void 0:R.x,Te=(F=P.arrow)==null?void 0:F.y,ae=((K=P.arrow)==null?void 0:K.centerOffset)!==0,[ye,me]=u.useState();return Q(()=>{S&&me(window.getComputedStyle(S).zIndex)},[S]),v.jsx("div",{ref:k.setFloating,"data-radix-popper-content-wrapper":"",style:{...D,transform:N?D.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ye,"--radix-popper-transform-origin":[(H=P.transformOrigin)==null?void 0:H.x,(_=P.transformOrigin)==null?void 0:_.y].join(" "),...((M=P.hide)==null?void 0:M.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:v.jsx(Bo,{scope:n,placedSide:L,onArrowChange:C,arrowX:Pe,arrowY:Te,shouldHideArrow:ae,children:v.jsx(U.div,{"data-side":L,"data-align":$,...d,ref:x,style:{...d.style,animation:N?void 0:"none"}})})})});Xt.displayName=wt;var qt="PopperArrow",Fo={top:"bottom",right:"left",bottom:"top",left:"right"},Gt=u.forwardRef(function(t,n){const{__scopePopper:o,...r}=t,s=Vo(qt,o),i=Fo[s.placedSide];return v.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:v.jsx(jo,{...r,ref:n,style:{...r.style,display:"block"}})})});Gt.displayName=qt;function Wo(e){return e!==null}var $o=e=>({name:"transformOrigin",options:e,fn(t){var g,S,m;const{placement:n,rects:o,middlewareData:r}=t,i=((g=r.arrow)==null?void 0:g.centerOffset)!==0,c=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[a,f]=Zt(n),p={start:"0%",center:"50%",end:"100%"}[f],y=(((S=r.arrow)==null?void 0:S.x)??0)+c/2,h=(((m=r.arrow)==null?void 0:m.y)??0)+l/2;let w="",d="";return a==="bottom"?(w=i?p:`${y}px`,d=`${-l}px`):a==="top"?(w=i?p:`${y}px`,d=`${o.floating.height+l}px`):a==="right"?(w=`${-l}px`,d=i?p:`${h}px`):a==="left"&&(w=`${o.floating.width+l}px`,d=i?p:`${h}px`),{data:{x:w,y:d}}}});function Zt(e){const[t,n="center"]=e.split("-");return[t,n]}var zo=Ut,Uo=Yt,Ko=Xt,Yo=Gt;function Xo(e){const t=u.useRef({value:e,previous:e});return u.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var Jt=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),qo="VisuallyHidden",Go=u.forwardRef((e,t)=>v.jsx(U.span,{...e,ref:t,style:{...Jt,...e.style}}));Go.displayName=qo;var Zo=[" ","Enter","ArrowUp","ArrowDown"],Jo=[" ","Enter"],we="Select",[Ke,Ye,Qo]=Dn(we),[Ae,Wr]=st(we,[Qo,$t]),Xe=$t(),[er,ue]=Ae(we),[tr,nr]=Ae(we),Qt=e=>{const{__scopeSelect:t,children:n,open:o,defaultOpen:r,onOpenChange:s,value:i,defaultValue:c,onValueChange:l,dir:a,name:f,autoComplete:p,disabled:y,required:h,form:w}=e,d=Xe(t),[g,S]=u.useState(null),[m,x]=u.useState(null),[b,C]=u.useState(!1),T=jn(a),[A,O]=yt({prop:o,defaultProp:r??!1,onChange:s,caller:we}),[W,j]=yt({prop:i,defaultProp:c,onChange:l,caller:we}),B=u.useRef(null),V=g?w||!!g.closest("form"):!0,[I,k]=u.useState(new Set),D=Array.from(I).map(E=>E.props.value).join(";");return v.jsx(zo,{...d,children:v.jsxs(er,{required:h,scope:t,trigger:g,onTriggerChange:S,valueNode:m,onValueNodeChange:x,valueNodeHasChildren:b,onValueNodeHasChildrenChange:C,contentId:lt(),value:W,onValueChange:j,open:A,onOpenChange:O,dir:T,triggerPointerDownPosRef:B,disabled:y,children:[v.jsx(Ke.Provider,{scope:t,children:v.jsx(tr,{scope:e.__scopeSelect,onNativeOptionAdd:u.useCallback(E=>{k(N=>new Set(N).add(E))},[]),onNativeOptionRemove:u.useCallback(E=>{k(N=>{const P=new Set(N);return P.delete(E),P})},[]),children:n})}),V?v.jsxs(bn,{"aria-hidden":!0,required:h,tabIndex:-1,name:f,autoComplete:p,value:W,onChange:E=>j(E.target.value),disabled:y,form:w,children:[W===void 0?v.jsx("option",{value:""}):null,Array.from(I)]},D):null]})})};Qt.displayName=we;var en="SelectTrigger",tn=u.forwardRef((e,t)=>{const{__scopeSelect:n,disabled:o=!1,...r}=e,s=Xe(n),i=ue(en,n),c=i.disabled||o,l=Y(t,i.onTriggerChange),a=Ye(n),f=u.useRef("touch"),[p,y,h]=Rn(d=>{const g=a().filter(x=>!x.disabled),S=g.find(x=>x.value===i.value),m=An(g,d,S);m!==void 0&&i.onValueChange(m.value)}),w=d=>{c||(i.onOpenChange(!0),h()),d&&(i.triggerPointerDownPosRef.current={x:Math.round(d.pageX),y:Math.round(d.pageY)})};return v.jsx(Uo,{asChild:!0,...s,children:v.jsx(U.button,{type:"button",role:"combobox","aria-controls":i.contentId,"aria-expanded":i.open,"aria-required":i.required,"aria-autocomplete":"none",dir:i.dir,"data-state":i.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":Cn(i.value)?"":void 0,...r,ref:l,onClick:z(r.onClick,d=>{d.currentTarget.focus(),f.current!=="mouse"&&w(d)}),onPointerDown:z(r.onPointerDown,d=>{f.current=d.pointerType;const g=d.target;g.hasPointerCapture(d.pointerId)&&g.releasePointerCapture(d.pointerId),d.button===0&&d.ctrlKey===!1&&d.pointerType==="mouse"&&(w(d),d.preventDefault())}),onKeyDown:z(r.onKeyDown,d=>{const g=p.current!=="";!(d.ctrlKey||d.altKey||d.metaKey)&&d.key.length===1&&y(d.key),!(g&&d.key===" ")&&Zo.includes(d.key)&&(w(),d.preventDefault())})})})});tn.displayName=en;var nn="SelectValue",on=u.forwardRef((e,t)=>{const{__scopeSelect:n,className:o,style:r,children:s,placeholder:i="",...c}=e,l=ue(nn,n),{onValueNodeHasChildrenChange:a}=l,f=s!==void 0,p=Y(t,l.onValueNodeChange);return Q(()=>{a(f)},[a,f]),v.jsx(U.span,{...c,ref:p,style:{pointerEvents:"none"},children:Cn(l.value)?v.jsx(v.Fragment,{children:i}):s})});on.displayName=nn;var or="SelectIcon",rn=u.forwardRef((e,t)=>{const{__scopeSelect:n,children:o,...r}=e;return v.jsx(U.span,{"aria-hidden":!0,...r,ref:t,children:o||"▼"})});rn.displayName=or;var rr="SelectPortal",sn=e=>v.jsx(En,{asChild:!0,...e});sn.displayName=rr;var ve="SelectContent",cn=u.forwardRef((e,t)=>{const n=ue(ve,e.__scopeSelect),[o,r]=u.useState();if(Q(()=>{r(new DocumentFragment)},[]),!n.open){const s=o;return s?ct.createPortal(v.jsx(ln,{scope:e.__scopeSelect,children:v.jsx(Ke.Slot,{scope:e.__scopeSelect,children:v.jsx("div",{children:e.children})})}),s):null}return v.jsx(an,{...e,ref:t})});cn.displayName=ve;var J=10,[ln,pe]=Ae(ve),ir="SelectContentImpl",sr=Ze("SelectContent.RemoveScroll"),an=u.forwardRef((e,t)=>{const{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:r,onEscapeKeyDown:s,onPointerDownOutside:i,side:c,sideOffset:l,align:a,alignOffset:f,arrowPadding:p,collisionBoundary:y,collisionPadding:h,sticky:w,hideWhenDetached:d,avoidCollisions:g,...S}=e,m=ue(ve,n),[x,b]=u.useState(null),[C,T]=u.useState(null),A=Y(t,R=>b(R)),[O,W]=u.useState(null),[j,B]=u.useState(null),V=Ye(n),[I,k]=u.useState(!1),D=u.useRef(!1);u.useEffect(()=>{if(x)return On(x)},[x]),In();const E=u.useCallback(R=>{const[F,...K]=V().map(M=>M.ref.current),[H]=K.slice(-1),_=document.activeElement;for(const M of R)if(M===_||(M==null||M.scrollIntoView({block:"nearest"}),M===F&&C&&(C.scrollTop=0),M===H&&C&&(C.scrollTop=C.scrollHeight),M==null||M.focus(),document.activeElement!==_))return},[V,C]),N=u.useCallback(()=>E([O,x]),[E,O,x]);u.useEffect(()=>{I&&N()},[I,N]);const{onOpenChange:P,triggerPointerDownPosRef:L}=m;u.useEffect(()=>{if(x){let R={x:0,y:0};const F=H=>{var _,M;R={x:Math.abs(Math.round(H.pageX)-(((_=L.current)==null?void 0:_.x)??0)),y:Math.abs(Math.round(H.pageY)-(((M=L.current)==null?void 0:M.y)??0))}},K=H=>{R.x<=10&&R.y<=10?H.preventDefault():x.contains(H.target)||P(!1),document.removeEventListener("pointermove",F),L.current=null};return L.current!==null&&(document.addEventListener("pointermove",F),document.addEventListener("pointerup",K,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",F),document.removeEventListener("pointerup",K,{capture:!0})}}},[x,P,L]),u.useEffect(()=>{const R=()=>P(!1);return window.addEventListener("blur",R),window.addEventListener("resize",R),()=>{window.removeEventListener("blur",R),window.removeEventListener("resize",R)}},[P]);const[$,Z]=Rn(R=>{const F=V().filter(_=>!_.disabled),K=F.find(_=>_.ref.current===document.activeElement),H=An(F,R,K);H&&setTimeout(()=>H.ref.current.focus())}),Pe=u.useCallback((R,F,K)=>{const H=!D.current&&!K;(m.value!==void 0&&m.value===F||H)&&(W(R),H&&(D.current=!0))},[m.value]),Te=u.useCallback(()=>x==null?void 0:x.focus(),[x]),ae=u.useCallback((R,F,K)=>{const H=!D.current&&!K;(m.value!==void 0&&m.value===F||H)&&B(R)},[m.value]),ye=o==="popper"?tt:dn,me=ye===tt?{side:c,sideOffset:l,align:a,alignOffset:f,arrowPadding:p,collisionBoundary:y,collisionPadding:h,sticky:w,hideWhenDetached:d,avoidCollisions:g}:{};return v.jsx(ln,{scope:n,content:x,viewport:C,onViewportChange:T,itemRefCallback:Pe,selectedItem:O,onItemLeave:Te,itemTextRefCallback:ae,focusSelectedItem:N,selectedItemText:j,position:o,isPositioned:I,searchRef:$,children:v.jsx(Nn,{as:sr,allowPinchZoom:!0,children:v.jsx(_n,{asChild:!0,trapped:m.open,onMountAutoFocus:R=>{R.preventDefault()},onUnmountAutoFocus:z(r,R=>{var F;(F=m.trigger)==null||F.focus({preventScroll:!0}),R.preventDefault()}),children:v.jsx(Mn,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:s,onPointerDownOutside:i,onFocusOutside:R=>R.preventDefault(),onDismiss:()=>m.onOpenChange(!1),children:v.jsx(ye,{role:"listbox",id:m.contentId,"data-state":m.open?"open":"closed",dir:m.dir,onContextMenu:R=>R.preventDefault(),...S,...me,onPlaced:()=>k(!0),ref:A,style:{display:"flex",flexDirection:"column",outline:"none",...S.style},onKeyDown:z(S.onKeyDown,R=>{const F=R.ctrlKey||R.altKey||R.metaKey;if(R.key==="Tab"&&R.preventDefault(),!F&&R.key.length===1&&Z(R.key),["ArrowUp","ArrowDown","Home","End"].includes(R.key)){let H=V().filter(_=>!_.disabled).map(_=>_.ref.current);if(["ArrowUp","End"].includes(R.key)&&(H=H.slice().reverse()),["ArrowUp","ArrowDown"].includes(R.key)){const _=R.target,M=H.indexOf(_);H=H.slice(M+1)}setTimeout(()=>E(H)),R.preventDefault()}})})})})})})});an.displayName=ir;var cr="SelectItemAlignedPosition",dn=u.forwardRef((e,t)=>{const{__scopeSelect:n,onPlaced:o,...r}=e,s=ue(ve,n),i=pe(ve,n),[c,l]=u.useState(null),[a,f]=u.useState(null),p=Y(t,A=>f(A)),y=Ye(n),h=u.useRef(!1),w=u.useRef(!0),{viewport:d,selectedItem:g,selectedItemText:S,focusSelectedItem:m}=i,x=u.useCallback(()=>{if(s.trigger&&s.valueNode&&c&&a&&d&&g&&S){const A=s.trigger.getBoundingClientRect(),O=a.getBoundingClientRect(),W=s.valueNode.getBoundingClientRect(),j=S.getBoundingClientRect();if(s.dir!=="rtl"){const _=j.left-O.left,M=W.left-_,X=A.left-M,ne=A.width+X,Ee=Math.max(ne,O.width),Oe=window.innerWidth-J,Ie=St(M,[J,Math.max(J,Oe-Ee)]);c.style.minWidth=ne+"px",c.style.left=Ie+"px"}else{const _=O.right-j.right,M=window.innerWidth-W.right-_,X=window.innerWidth-A.right-M,ne=A.width+X,Ee=Math.max(ne,O.width),Oe=window.innerWidth-J,Ie=St(M,[J,Math.max(J,Oe-Ee)]);c.style.minWidth=ne+"px",c.style.right=Ie+"px"}const B=y(),V=window.innerHeight-J*2,I=d.scrollHeight,k=window.getComputedStyle(a),D=parseInt(k.borderTopWidth,10),E=parseInt(k.paddingTop,10),N=parseInt(k.borderBottomWidth,10),P=parseInt(k.paddingBottom,10),L=D+E+I+P+N,$=Math.min(g.offsetHeight*5,L),Z=window.getComputedStyle(d),Pe=parseInt(Z.paddingTop,10),Te=parseInt(Z.paddingBottom,10),ae=A.top+A.height/2-J,ye=V-ae,me=g.offsetHeight/2,R=g.offsetTop+me,F=D+E+R,K=L-F;if(F<=ae){const _=B.length>0&&g===B[B.length-1].ref.current;c.style.bottom="0px";const M=a.clientHeight-d.offsetTop-d.offsetHeight,X=Math.max(ye,me+(_?Te:0)+M+N),ne=F+X;c.style.height=ne+"px"}else{const _=B.length>0&&g===B[0].ref.current;c.style.top="0px";const X=Math.max(ae,D+d.offsetTop+(_?Pe:0)+me)+K;c.style.height=X+"px",d.scrollTop=F-ae+d.offsetTop}c.style.margin=`${J}px 0`,c.style.minHeight=$+"px",c.style.maxHeight=V+"px",o==null||o(),requestAnimationFrame(()=>h.current=!0)}},[y,s.trigger,s.valueNode,c,a,d,g,S,s.dir,o]);Q(()=>x(),[x]);const[b,C]=u.useState();Q(()=>{a&&C(window.getComputedStyle(a).zIndex)},[a]);const T=u.useCallback(A=>{A&&w.current===!0&&(x(),m==null||m(),w.current=!1)},[x,m]);return v.jsx(ar,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:h,onScrollButtonChange:T,children:v.jsx("div",{ref:l,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:b},children:v.jsx(U.div,{...r,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...r.style}})})})});dn.displayName=cr;var lr="SelectPopperPosition",tt=u.forwardRef((e,t)=>{const{__scopeSelect:n,align:o="start",collisionPadding:r=J,...s}=e,i=Xe(n);return v.jsx(Ko,{...i,...s,ref:t,align:o,collisionPadding:r,style:{boxSizing:"border-box",...s.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});tt.displayName=lr;var[ar,vt]=Ae(ve,{}),nt="SelectViewport",fn=u.forwardRef((e,t)=>{const{__scopeSelect:n,nonce:o,...r}=e,s=pe(nt,n),i=vt(nt,n),c=Y(t,s.onViewportChange),l=u.useRef(0);return v.jsxs(v.Fragment,{children:[v.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),v.jsx(Ke.Slot,{scope:n,children:v.jsx(U.div,{"data-radix-select-viewport":"",role:"presentation",...r,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...r.style},onScroll:z(r.onScroll,a=>{const f=a.currentTarget,{contentWrapper:p,shouldExpandOnScrollRef:y}=i;if(y!=null&&y.current&&p){const h=Math.abs(l.current-f.scrollTop);if(h>0){const w=window.innerHeight-J*2,d=parseFloat(p.style.minHeight),g=parseFloat(p.style.height),S=Math.max(d,g);if(S<w){const m=S+h,x=Math.min(w,m),b=m-x;p.style.height=x+"px",p.style.bottom==="0px"&&(f.scrollTop=b>0?b:0,p.style.justifyContent="flex-end")}}}l.current=f.scrollTop})})})]})});fn.displayName=nt;var un="SelectGroup",[dr,fr]=Ae(un),ur=u.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e,r=lt();return v.jsx(dr,{scope:n,id:r,children:v.jsx(U.div,{role:"group","aria-labelledby":r,...o,ref:t})})});ur.displayName=un;var pn="SelectLabel",pr=u.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e,r=fr(pn,n);return v.jsx(U.div,{id:r.id,...o,ref:t})});pr.displayName=pn;var We="SelectItem",[mr,mn]=Ae(We),hn=u.forwardRef((e,t)=>{const{__scopeSelect:n,value:o,disabled:r=!1,textValue:s,...i}=e,c=ue(We,n),l=pe(We,n),a=c.value===o,[f,p]=u.useState(s??""),[y,h]=u.useState(!1),w=Y(t,m=>{var x;return(x=l.itemRefCallback)==null?void 0:x.call(l,m,o,r)}),d=lt(),g=u.useRef("touch"),S=()=>{r||(c.onValueChange(o),c.onOpenChange(!1))};if(o==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return v.jsx(mr,{scope:n,value:o,disabled:r,textId:d,isSelected:a,onItemTextChange:u.useCallback(m=>{p(x=>x||((m==null?void 0:m.textContent)??"").trim())},[]),children:v.jsx(Ke.ItemSlot,{scope:n,value:o,disabled:r,textValue:f,children:v.jsx(U.div,{role:"option","aria-labelledby":d,"data-highlighted":y?"":void 0,"aria-selected":a&&y,"data-state":a?"checked":"unchecked","aria-disabled":r||void 0,"data-disabled":r?"":void 0,tabIndex:r?void 0:-1,...i,ref:w,onFocus:z(i.onFocus,()=>h(!0)),onBlur:z(i.onBlur,()=>h(!1)),onClick:z(i.onClick,()=>{g.current!=="mouse"&&S()}),onPointerUp:z(i.onPointerUp,()=>{g.current==="mouse"&&S()}),onPointerDown:z(i.onPointerDown,m=>{g.current=m.pointerType}),onPointerMove:z(i.onPointerMove,m=>{var x;g.current=m.pointerType,r?(x=l.onItemLeave)==null||x.call(l):g.current==="mouse"&&m.currentTarget.focus({preventScroll:!0})}),onPointerLeave:z(i.onPointerLeave,m=>{var x;m.currentTarget===document.activeElement&&((x=l.onItemLeave)==null||x.call(l))}),onKeyDown:z(i.onKeyDown,m=>{var b;((b=l.searchRef)==null?void 0:b.current)!==""&&m.key===" "||(Jo.includes(m.key)&&S(),m.key===" "&&m.preventDefault())})})})})});hn.displayName=We;var Ne="SelectItemText",gn=u.forwardRef((e,t)=>{const{__scopeSelect:n,className:o,style:r,...s}=e,i=ue(Ne,n),c=pe(Ne,n),l=mn(Ne,n),a=nr(Ne,n),[f,p]=u.useState(null),y=Y(t,S=>p(S),l.onItemTextChange,S=>{var m;return(m=c.itemTextRefCallback)==null?void 0:m.call(c,S,l.value,l.disabled)}),h=f==null?void 0:f.textContent,w=u.useMemo(()=>v.jsx("option",{value:l.value,disabled:l.disabled,children:h},l.value),[l.disabled,l.value,h]),{onNativeOptionAdd:d,onNativeOptionRemove:g}=a;return Q(()=>(d(w),()=>g(w)),[d,g,w]),v.jsxs(v.Fragment,{children:[v.jsx(U.span,{id:l.textId,...s,ref:y}),l.isSelected&&i.valueNode&&!i.valueNodeHasChildren?ct.createPortal(s.children,i.valueNode):null]})});gn.displayName=Ne;var xn="SelectItemIndicator",wn=u.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e;return mn(xn,n).isSelected?v.jsx(U.span,{"aria-hidden":!0,...o,ref:t}):null});wn.displayName=xn;var ot="SelectScrollUpButton",vn=u.forwardRef((e,t)=>{const n=pe(ot,e.__scopeSelect),o=vt(ot,e.__scopeSelect),[r,s]=u.useState(!1),i=Y(t,o.onScrollButtonChange);return Q(()=>{if(n.viewport&&n.isPositioned){let c=function(){const a=l.scrollTop>0;s(a)};const l=n.viewport;return c(),l.addEventListener("scroll",c),()=>l.removeEventListener("scroll",c)}},[n.viewport,n.isPositioned]),r?v.jsx(Sn,{...e,ref:i,onAutoScroll:()=>{const{viewport:c,selectedItem:l}=n;c&&l&&(c.scrollTop=c.scrollTop-l.offsetHeight)}}):null});vn.displayName=ot;var rt="SelectScrollDownButton",yn=u.forwardRef((e,t)=>{const n=pe(rt,e.__scopeSelect),o=vt(rt,e.__scopeSelect),[r,s]=u.useState(!1),i=Y(t,o.onScrollButtonChange);return Q(()=>{if(n.viewport&&n.isPositioned){let c=function(){const a=l.scrollHeight-l.clientHeight,f=Math.ceil(l.scrollTop)<a;s(f)};const l=n.viewport;return c(),l.addEventListener("scroll",c),()=>l.removeEventListener("scroll",c)}},[n.viewport,n.isPositioned]),r?v.jsx(Sn,{...e,ref:i,onAutoScroll:()=>{const{viewport:c,selectedItem:l}=n;c&&l&&(c.scrollTop=c.scrollTop+l.offsetHeight)}}):null});yn.displayName=rt;var Sn=u.forwardRef((e,t)=>{const{__scopeSelect:n,onAutoScroll:o,...r}=e,s=pe("SelectScrollButton",n),i=u.useRef(null),c=Ye(n),l=u.useCallback(()=>{i.current!==null&&(window.clearInterval(i.current),i.current=null)},[]);return u.useEffect(()=>()=>l(),[l]),Q(()=>{var f;const a=c().find(p=>p.ref.current===document.activeElement);(f=a==null?void 0:a.ref.current)==null||f.scrollIntoView({block:"nearest"})},[c]),v.jsx(U.div,{"aria-hidden":!0,...r,ref:t,style:{flexShrink:0,...r.style},onPointerDown:z(r.onPointerDown,()=>{i.current===null&&(i.current=window.setInterval(o,50))}),onPointerMove:z(r.onPointerMove,()=>{var a;(a=s.onItemLeave)==null||a.call(s),i.current===null&&(i.current=window.setInterval(o,50))}),onPointerLeave:z(r.onPointerLeave,()=>{l()})})}),hr="SelectSeparator",gr=u.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e;return v.jsx(U.div,{"aria-hidden":!0,...o,ref:t})});gr.displayName=hr;var it="SelectArrow",xr=u.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e,r=Xe(n),s=ue(it,n),i=pe(it,n);return s.open&&i.position==="popper"?v.jsx(Yo,{...r,...o,ref:t}):null});xr.displayName=it;var wr="SelectBubbleInput",bn=u.forwardRef(({__scopeSelect:e,value:t,...n},o)=>{const r=u.useRef(null),s=Y(o,r),i=Xo(t);return u.useEffect(()=>{const c=r.current;if(!c)return;const l=window.HTMLSelectElement.prototype,f=Object.getOwnPropertyDescriptor(l,"value").set;if(i!==t&&f){const p=new Event("change",{bubbles:!0});f.call(c,t),c.dispatchEvent(p)}},[i,t]),v.jsx(U.select,{...n,style:{...Jt,...n.style},ref:s,defaultValue:t})});bn.displayName=wr;function Cn(e){return e===""||e===void 0}function Rn(e){const t=It(e),n=u.useRef(""),o=u.useRef(0),r=u.useCallback(i=>{const c=n.current+i;t(c),function l(a){n.current=a,window.clearTimeout(o.current),a!==""&&(o.current=window.setTimeout(()=>l(""),1e3))}(c)},[t]),s=u.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return u.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,r,s]}function An(e,t,n){const r=t.length>1&&Array.from(t).every(a=>a===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let i=vr(e,Math.max(s,0));r.length===1&&(i=i.filter(a=>a!==n));const l=i.find(a=>a.textValue.toLowerCase().startsWith(r.toLowerCase()));return l!==n?l:void 0}function vr(e,t){return e.map((n,o)=>e[(t+o)%e.length])}var yr=Qt,Sr=tn,br=on,Cr=rn,Rr=sn,Ar=cn,Pr=fn,Tr=hn,Er=gn,Or=wn,Ir=vn,Nr=yn;/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _r=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],Mr=at("check",_r);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dr=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],Pn=at("chevron-down",Dr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lr=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],jr=at("chevron-up",Lr);function $r({...e}){return v.jsx(yr,{"data-slot":"select",...e})}function zr({...e}){return v.jsx(br,{"data-slot":"select-value",...e})}function Ur({className:e,size:t="default",children:n,...o}){return v.jsxs(Sr,{"data-slot":"select-trigger","data-size":t,className:ge("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...o,children:[n,v.jsx(Cr,{asChild:!0,children:v.jsx(Pn,{className:"size-4 opacity-50"})})]})}function Kr({className:e,children:t,position:n="popper",...o}){return v.jsx(Rr,{children:v.jsxs(Ar,{"data-slot":"select-content",className:ge("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",n==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...o,children:[v.jsx(kr,{}),v.jsx(Pr,{className:ge("p-1",n==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),v.jsx(Hr,{})]})})}function Yr({className:e,children:t,...n}){return v.jsxs(Tr,{"data-slot":"select-item",className:ge("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...n,children:[v.jsx("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:v.jsx(Or,{children:v.jsx(Mr,{className:"size-4"})})}),v.jsx(Er,{children:t})]})}function kr({className:e,...t}){return v.jsx(Ir,{"data-slot":"select-scroll-up-button",className:ge("flex cursor-default items-center justify-center py-1",e),...t,children:v.jsx(jr,{className:"size-4"})})}function Hr({className:e,...t}){return v.jsx(Nr,{"data-slot":"select-scroll-down-button",className:ge("flex cursor-default items-center justify-center py-1",e),...t,children:v.jsx(Pn,{className:"size-4"})})}export{Mr as C,Fr as I,$r as S,Ur as a,zr as b,Kr as c,Yr as d,ko as e,Xo as u};
