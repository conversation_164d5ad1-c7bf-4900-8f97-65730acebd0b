using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Antiforgery;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Imip.Ekb.Web.Middleware
{
    /// <summary>
    /// Middleware to set CSRF cookie for the React app
    /// </summary>
    public class CsrfTokenMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IAntiforgery _antiforgery;
        private readonly ILogger<CsrfTokenMiddleware> _logger;

        public CsrfTokenMiddleware(
            RequestDelegate next,
            IAntiforgery antiforgery,
            ILogger<CsrfTokenMiddleware> logger)
        {
            _next = next;
            _antiforgery = antiforgery;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Only process requests to the React app
            if (context.Request.Path.StartsWithSegments("/") &&
                !context.Request.Path.StartsWithSegments("/api") &&
                !context.Request.Path.StartsWithSegments("/Account") &&
                !context.Request.Path.StartsWithSegments("/connect") &&
                !context.Request.Path.StartsWithSegments("/swagger") &&
                !context.Request.Path.StartsWithSegments("/health-status"))
            {
                try
                {
                    // Generate the CSRF token - this will set the cookie
                    _antiforgery.GetAndStoreTokens(context);
                    _logger.LogInformation($"Generated CSRF token for path: {context.Request.Path}");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error generating CSRF token");
                }
            }

            // Continue with the request pipeline
            await _next(context);
        }
    }
}
