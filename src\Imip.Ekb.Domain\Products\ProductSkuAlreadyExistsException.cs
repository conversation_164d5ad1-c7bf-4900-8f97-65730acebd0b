using Volo.Abp;

namespace Imip.Ekb.Products
{
    /// <summary>
    /// Exception thrown when a product SKU already exists
    /// </summary>
    public class ProductSkuAlreadyExistsException : BusinessException
    {
        public ProductSkuAlreadyExistsException(string sku)
            : base(EkbDomainErrorCodes.ProductSkuAlreadyExists)
        {
            WithData("sku", sku);
        }
    }
}
