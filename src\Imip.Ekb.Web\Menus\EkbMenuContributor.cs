using System.Threading.Tasks;
using Imip.Ekb.Localization;
using Imip.Ekb.Permissions;
using Imip.Ekb.MultiTenancy;
using Volo.Abp.SettingManagement.Web.Navigation;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Identity.Web.Navigation;
using Volo.Abp.UI.Navigation;
using Volo.Abp.TenantManagement.Web.Navigation;
using Microsoft.Extensions.DependencyInjection;

namespace Imip.Ekb.Web.Menus;

public class EkbMenuContributor : IMenuContributor
{
    public async Task ConfigureMenuAsync(MenuConfigurationContext context)
    {
        if (context.Menu.Name == StandardMenus.Main)
        {
            await ConfigureMainMenuAsync(context);
        }
    }

    private static async Task ConfigureMainMenuAsync(MenuConfigurationContext context)
    {
        var l = context.GetLocalizer<EkbResource>();

        //Home (now points to the React app)
        context.Menu.AddItem(
            new ApplicationMenuItem(
                EkbMenus.Home,
                l["Menu:Home"],
                "/",
                icon: "fa fa-home",
                order: 1
            )
        );

        //ABP Home (original ABP home page)
        context.Menu.AddItem(
            new ApplicationMenuItem(
                EkbMenus.AbpHome,
                l["Menu:AbpHome"],
                "/abp-home",
                icon: "fa fa-info-circle",
                order: 3
            )
        );

        //Products
        var productsMenuItem = new ApplicationMenuItem(
            EkbMenus.Products,
            l["Menu:Products"],
            "/products",
            icon: "fa fa-box",
            order: 2
        );

        // Check permission before adding the menu item
        if (await context.IsGrantedAsync(EkbPermissions.Products.Default))
        {
            context.Menu.AddItem(productsMenuItem);
        }


        //Administration
        var administration = context.Menu.GetAdministration();
        administration.Order = 6;

        //Administration->Identity
        administration.SetSubItemOrder(IdentityMenuNames.GroupName, 1);

        if (MultiTenancyConsts.IsEnabled)
        {
            administration.SetSubItemOrder(TenantManagementMenuNames.GroupName, 1);
        }
        else
        {
            administration.TryRemoveMenuItem(TenantManagementMenuNames.GroupName);
        }

        administration.SetSubItemOrder(SettingManagementMenuNames.GroupName, 3);

        //Administration->Settings
        administration.SetSubItemOrder(SettingManagementMenuNames.GroupName, 7);
    }
}
