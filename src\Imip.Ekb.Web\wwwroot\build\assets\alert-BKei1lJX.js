import{r as o,j as r}from"./App-BZgNu-zL.js";import{P as l,d as s,e as n,f as c}from"./card-YgUuHGlh.js";var u="Label",i=o.forwardRef((t,a)=>r.jsx(l.label,{...t,ref:a,onMouseDown:e=>{var d;e.target.closest("button, input, select, textarea")||((d=t.onMouseDown)==null||d.call(t,e),!e.defaultPrevented&&e.detail>1&&e.preventDefault())}}));i.displayName=u;var g=i;function b({className:t,...a}){return r.jsx(g,{"data-slot":"label",className:s("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v=[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]],y=n("save",v),f=c("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function j({className:t,variant:a,...e}){return r.jsx("div",{"data-slot":"alert",role:"alert",className:s(f({variant:a}),t),...e})}function h({className:t,...a}){return r.jsx("div",{"data-slot":"alert-description",className:s("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...a})}export{j as A,b as L,y as S,h as a};
