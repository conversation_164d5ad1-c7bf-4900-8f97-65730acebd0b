using System.ComponentModel.DataAnnotations;

namespace Imip.Ekb.Products
{
    /// <summary>
    /// DTO for creating or updating a product
    /// </summary>
    public class CreateUpdateProductDto
    {
        /// <summary>
        /// Name of the product
        /// </summary>
        [Required]
        [StringLength(128, MinimumLength = 2)]
        public string Name { get; set; }

        /// <summary>
        /// Description of the product
        /// </summary>
        [StringLength(2000)]
        public string Description { get; set; }

        /// <summary>
        /// Price of the product
        /// </summary>
        [Required]
        [Range(0.01, 99999.99)]
        public decimal Price { get; set; }

        /// <summary>
        /// Quantity in stock
        /// </summary>
        [Required]
        [Range(0, int.MaxValue)]
        public int StockQuantity { get; set; }

        /// <summary>
        /// SKU (Stock Keeping Unit) - unique identifier for the product
        /// </summary>
        [Required]
        [StringLength(32, MinimumLength = 3)]
        [RegularExpression(@"^[a-zA-Z0-9-_]+$", ErrorMessage = "SKU can only contain letters, numbers, hyphens, and underscores")]
        public string SKU { get; set; }

        /// <summary>
        /// Whether the product is active and available for sale
        /// </summary>
        public bool IsActive { get; set; } = true;
    }
}
