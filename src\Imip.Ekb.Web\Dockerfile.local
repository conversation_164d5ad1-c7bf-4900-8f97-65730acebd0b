FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
USER $APP_UID
EXPOSE 8080
EXPOSE 8081

COPY bin/Release/net9.0/publish/ app/
WORKDIR /app

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
RUN dotnet dev-certs https -v -ep openiddict.pfx -p 08fceee9-a0a4-4732-b011-be86eb1759ca
RUN chmod 644 openiddict.pfx

FROM base AS final
WORKDIR /app
COPY --from=build /src .

ENTRYPOINT ["dotnet", "Imip.Ekb.Web.dll"]